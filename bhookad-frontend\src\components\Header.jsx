import React from 'react'
import { Menu, X, User } from 'lucide-react'
import { Link, useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import './Header.css'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false)
  const { user, userProfile, signOut } = useAuth()
  const navigate = useNavigate()

  const handleSignOut = async () => {
    await signOut()
    navigate('/')
  }

  const getDashboardLink = () => {
    if (!userProfile?.role) return '/select-role'
    return userProfile.role === 'vendor' ? '/vendor/dashboard' : '/vlogger/dashboard'
  }

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          <div className="logo">
            <Link to="/">
              <h1>Bhookad</h1>
            </Link>
          </div>

          <nav className={`nav ${isMenuOpen ? 'nav-open' : ''}`}>
            <a href="#home" className="nav-link">Home</a>
            <a href="#how-it-works" className="nav-link">How it Works</a>
            <a href="#vendors" className="nav-link">Vendors</a>
            <a href="#about" className="nav-link">About</a>
            <a href="#contact" className="nav-link">Contact</a>
          </nav>

          <div className="header-actions">
            {user ? (
              <div className="user-menu">
                <Link to={getDashboardLink()} className="user-profile">
                  <User size={20} />
                  <span>{userProfile?.full_name || 'Dashboard'}</span>
                </Link>
                <button className="btn-secondary" onClick={handleSignOut}>
                  Sign Out
                </button>
              </div>
            ) : (
              <>
                <Link to="/login" className="btn-secondary">Login</Link>
                <Link to="/register" className="btn-primary">Sign Up</Link>
              </>
            )}
          </div>

          <button
            className="mobile-menu-btn"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>
    </header>
  )
}

export default Header
