# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.2](https://github.com/inspect-js/data-view-byte-length/compare/v1.0.1...v1.0.2) - 2024-12-19

### Commits

- [readme] update URLs [`79df46c`](https://github.com/inspect-js/data-view-byte-length/commit/79df46cdf4c551d83059335ace8b8550369710b0)
- [actions] split out node 10-20, and 20+ [`2890929`](https://github.com/inspect-js/data-view-byte-length/commit/289092965343dfa6fcc931c32ecd1c1286f5f2dc)
- [Dev Deps] update `@arethetypeswrong/cli`, `@ljharb/eslint-config`, `@ljharb/tsconfig`, `@types/object-inspect`, `@types/tape`, `auto-changelog`, `es-value-fixtures`, `object-inspect`, `tape` [`97fac7f`](https://github.com/inspect-js/data-view-byte-length/commit/97fac7fb6c2f3655c9755d9dcf3569cdf6899635)
- [Refactor] use `call-bound` directly [`56f17fa`](https://github.com/inspect-js/data-view-byte-length/commit/56f17fa583a963c2a6d2da5f2db00f7725714533)
- [Deps] update `call-bind`, `is-data-view` [`254a502`](https://github.com/inspect-js/data-view-byte-length/commit/254a5026c0201eb35d20722af2993e0412b2f2ea)
- [Tests] replace `aud` with `npm audit` [`7158679`](https://github.com/inspect-js/data-view-byte-length/commit/7158679d1ef0e15ccf64b6d0169413ed8351f243)
- [Dev Deps] update `@ljharb/tsconfig` [`ed63290`](https://github.com/inspect-js/data-view-byte-length/commit/ed63290bc162fe5a9086412fc6099dd49c70d474)

## [v1.0.1](https://github.com/inspect-js/data-view-byte-length/compare/v1.0.0...v1.0.1) - 2024-03-08

### Commits

- [types] use shared tsconfig [`0d5873c`](https://github.com/inspect-js/data-view-byte-length/commit/0d5873c9fddd413bacbb3a2c32e7e1a7f1f5bd2c)
- [Dev Deps] update `@arethetypeswrong/cli`, `tape` [`13c1eaf`](https://github.com/inspect-js/data-view-byte-length/commit/13c1eafea562993c7d15a450e86d2ee7a1aa8299)
- [patch] fix function name [`a061e7b`](https://github.com/inspect-js/data-view-byte-length/commit/a061e7b457f53bdc39716dacdddbe558ee790745)
- [Deps] update `call-bind` [`6603851`](https://github.com/inspect-js/data-view-byte-length/commit/6603851eef423c5762841221f97abfdc905c8bde)

## v1.0.0 - 2024-03-04

### Commits

- Initial implementation, tests, readme, types [`79ad058`](https://github.com/inspect-js/data-view-byte-length/commit/79ad058a73a4f8425e8ad034211dfc39e643dc86)
- Initial commit [`1a11313`](https://github.com/inspect-js/data-view-byte-length/commit/1a113138783a815f66fd000a9f9c89fdbe8c98fb)
- npm init [`aac0108`](https://github.com/inspect-js/data-view-byte-length/commit/aac010820b20b8604d33e5a74b51591f19f553e2)
- Only apps should have lockfiles [`cdf1a15`](https://github.com/inspect-js/data-view-byte-length/commit/cdf1a15d4c201137982e1e703d56a42582c6d246)
