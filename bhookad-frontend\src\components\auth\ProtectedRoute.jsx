import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'

const ProtectedRoute = ({ children, requiredRole = null, redirectTo = '/login' }) => {
  const { user, userProfile, loading } = useAuth()
  const location = useLocation()

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="auth-container">
        <div className="auth-card">
          <div className="auth-header">
            <h1>Loading... 🍛</h1>
            <p>Checking your authentication</p>
          </div>
          <div className="loading-spinner">
            <div className="spinner"></div>
          </div>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />
  }

  // If role is required but user doesn't have a profile yet
  if (requiredRole && !userProfile) {
    return <Navigate to="/select-role" state={{ from: location }} replace />
  }

  // If specific role is required and user doesn't have it
  if (requiredRole && userProfile && userProfile.role !== requiredRole) {
    // Redirect to appropriate dashboard based on user's actual role
    const redirectPath = userProfile.role === 'vendor' ? '/vendor/dashboard' : '/vlogger/dashboard'
    return <Navigate to={redirectPath} replace />
  }

  // If user is authenticated but no specific role required, check if they have a role
  if (!requiredRole && userProfile && !userProfile.role) {
    return <Navigate to="/select-role" state={{ from: location }} replace />
  }

  // All checks passed, render the protected component
  return children
}

export default ProtectedRoute
