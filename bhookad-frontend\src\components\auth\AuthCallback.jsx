import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { supabase } from '../../lib/supabase'

const AuthCallback = () => {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const navigate = useNavigate()
  const { createUserProfile, fetchUserProfile } = useAuth()

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get the session from the URL hash
        const { data, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Auth callback error:', error)
          setError('Authentication failed. Please try again.')
          setTimeout(() => navigate('/login'), 3000)
          return
        }

        if (data.session && data.session.user) {
          const user = data.session.user
          
          // Check if user profile exists
          const { data: existingProfile } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('id', user.id)
            .single()

          if (!existingProfile) {
            // New user - redirect to role selection or create profile
            const userData = user.user_metadata || {}
            
            // If role is stored in metadata (from registration), create profile
            if (userData.role) {
              await createUserProfile(user.id, {
                email: user.email,
                full_name: userData.full_name || user.email,
                role: userData.role
              })
            } else {
              // Redirect to role selection
              navigate('/select-role')
              return
            }
          }

          // Fetch the latest profile
          await fetchUserProfile(user.id)
          
          // Redirect based on role
          const { data: profile } = await supabase
            .from('user_profiles')
            .select('role')
            .eq('id', user.id)
            .single()

          if (profile?.role === 'vendor') {
            navigate('/vendor/dashboard')
          } else if (profile?.role === 'vlogger') {
            navigate('/vlogger/dashboard')
          } else {
            navigate('/select-role')
          }
        } else {
          setError('No session found. Redirecting to login...')
          setTimeout(() => navigate('/login'), 2000)
        }
      } catch (err) {
        console.error('Callback handling error:', err)
        setError('An unexpected error occurred. Please try again.')
        setTimeout(() => navigate('/login'), 3000)
      } finally {
        setLoading(false)
      }
    }

    handleAuthCallback()
  }, [navigate, createUserProfile, fetchUserProfile])

  if (loading) {
    return (
      <div className="auth-container">
        <div className="auth-card">
          <div className="auth-header">
            <h1>Completing Sign In... 🍛</h1>
            <p>Please wait while we set up your account</p>
          </div>
          <div className="loading-spinner">
            <div className="spinner"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="auth-container">
        <div className="auth-card">
          <div className="auth-header">
            <h1>Authentication Error</h1>
            <p>{error}</p>
          </div>
          <div className="error-actions">
            <button 
              className="btn-primary"
              onClick={() => navigate('/login')}
            >
              Back to Login
            </button>
          </div>
        </div>
      </div>
    )
  }

  return null
}

export default AuthCallback
