Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FEBA
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210285FF9, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB740  0002100690B4 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA20  00021006A49D (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCC54E0000 ntdll.dll
7FFCC4500000 KERNEL32.DLL
7FFCC2CA0000 KERNELBASE.dll
7FFCC42C0000 USER32.dll
7FFCC2C70000 win32u.dll
7FFCC3330000 GDI32.dll
7FFCC3210000 gdi32full.dll
7FFCC2970000 msvcp_win.dll
7FFCC3030000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCC51D0000 advapi32.dll
7FFCC39B0000 msvcrt.dll
7FFCC3770000 sechost.dll
7FFCC47C0000 RPCRT4.dll
7FFCC20F0000 CRYPTBASE.DLL
7FFCC2BF0000 bcryptPrimitives.dll
7FFCC5280000 IMM32.DLL
