{"name": "json-buffer", "description": "JSON parse & stringify that supports binary via bops & base64", "version": "3.0.1", "homepage": "https://github.com/dominictarr/json-buffer", "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "devDependencies": {"tape": "^4.6.3"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "author": "<PERSON> <<EMAIL>> (http://dominictarr.com)", "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}}