.about {
  background: white;
}

.about-intro {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 4rem;
  align-items: start;
}

.about-text h3 {
  font-size: 2rem;
  color: #333;
  margin-bottom: 1.5rem;
  position: relative;
}

.about-text h3::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  border-radius: 2px;
}

.about-text p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #666;
  margin-bottom: 1.5rem;
}

.mission-vision {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.mission-card, .vision-card {
  background: linear-gradient(135deg, #fff3e0, #ffe0b2);
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  transition: transform 0.3s ease;
}

.mission-card:hover, .vision-card:hover {
  transform: translateY(-5px);
}

.card-icon {
  color: #ff6b35;
  margin-bottom: 1rem;
}

.mission-card h4, .vision-card h4 {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 1rem;
}

.mission-card p, .vision-card p {
  color: #666;
  line-height: 1.6;
}

.our-values {
  margin-bottom: 4rem;
}

.values-title {
  font-size: 2rem;
  text-align: center;
  color: #333;
  margin-bottom: 3rem;
  position: relative;
}

.values-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  border-radius: 2px;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.value-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.value-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.value-icon {
  color: #ff6b35;
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
}

.value-title {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 1rem;
}

.value-description {
  color: #666;
  line-height: 1.6;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  padding: 3rem 2rem;
  border-radius: 20px;
  color: white;
}

.stat-box {
  text-align: center;
}

.stat-box h4 {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.stat-box p {
  font-size: 1rem;
  opacity: 0.9;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .about-intro {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .mission-vision {
    gap: 1rem;
  }
  
  .values-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .about-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    padding: 2rem 1rem;
  }
  
  .stat-box h4 {
    font-size: 2rem;
  }
}
