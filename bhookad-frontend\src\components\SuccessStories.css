.success-stories {
  background: linear-gradient(135deg, #fff3e0, #ffe0b2);
}

.stories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.story-card {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  transition: transform 0.3s ease;
}

.story-card:hover {
  transform: translateY(-5px);
}

.quote-icon {
  color: #ff6b35;
  margin-bottom: 1rem;
}

.story-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #333;
  margin-bottom: 1.5rem;
  font-style: italic;
}

.story-rating {
  display: flex;
  gap: 0.25rem;
  margin-bottom: 1.5rem;
}

.star-filled {
  color: #ffc107;
  fill: #ffc107;
}

.star-empty {
  color: #e0e0e0;
}

.story-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-avatar {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.author-name {
  font-size: 1.1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.25rem;
}

.author-location {
  color: #666;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .stories-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .story-card {
    padding: 1.5rem;
  }
}
