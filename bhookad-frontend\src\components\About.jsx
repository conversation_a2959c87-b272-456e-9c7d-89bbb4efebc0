import React from 'react'
import { Target, Eye, Heart, Users, Award, Zap } from 'lucide-react'
import './About.css'

const About = () => {
  const values = [
    {
      icon: <Heart size={32} />,
      title: "Passion for Food",
      description: "Hum authentic street food culture ko promote karte hain aur local vendors ko support karte hain."
    },
    {
      icon: <Users size={32} />,
      title: "Community First",
      description: "Food lovers aur vendors ke beech ek strong community banane mein believe karte hain."
    },
    {
      icon: <Award size={32} />,
      title: "Quality Assurance",
      description: "Sirf best quality aur hygienic food vendors ko hi recommend karte hain."
    },
    {
      icon: <Zap size={32} />,
      title: "Innovation",
      description: "Technology ka use karke street food discovery ko easy aur enjoyable banate hain."
    }
  ]

  return (
    <section className="about section" id="about">
      <div className="container">
        <h2 className="section-title">About Bhookad</h2>
        
        <div className="about-intro">
          <div className="about-text">
            <h3>Hamara Mission</h3>
            <p>
              Bhookad ek revolutionary platform hai jo food lovers ko unke area ke best street food vendors se connect karta hai. 
              Hum believe karte hain ki authentic street food sirf khana nahi, balki ek cultural experience hai.
            </p>
            <p>
              Hamara goal hai ki har food lover ko trending aur quality street food easily mil sake, 
              aur local vendors ko zyada customers aur recognition mile.
            </p>
          </div>
          
          <div className="mission-vision">
            <div className="mission-card">
              <Target className="card-icon" size={40} />
              <h4>Our Mission</h4>
              <p>Street food culture ko digitally transform karna aur local food ecosystem ko strengthen karna.</p>
            </div>
            
            <div className="vision-card">
              <Eye className="card-icon" size={40} />
              <h4>Our Vision</h4>
              <p>India ka #1 street food discovery platform banna aur har city mein food lovers ko connect karna.</p>
            </div>
          </div>
        </div>

        <div className="our-values">
          <h3 className="values-title">Our Values</h3>
          <div className="values-grid">
            {values.map((value, index) => (
              <div key={index} className="value-card">
                <div className="value-icon">{value.icon}</div>
                <h4 className="value-title">{value.title}</h4>
                <p className="value-description">{value.description}</p>
              </div>
            ))}
          </div>
        </div>

        <div className="about-stats">
          <div className="stat-box">
            <h4>200+</h4>
            <p>Happy Customers</p>
          </div>
          <div className="stat-box">
            <h4>100+</h4>
            <p>Partner Vendors</p>
          </div>
          <div className="stat-box">
            <h4>15+</h4>
            <p>Cities Covered</p>
          </div>
          <div className="stat-box">
            <h4>500+</h4>
            <p>Food Items</p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default About
