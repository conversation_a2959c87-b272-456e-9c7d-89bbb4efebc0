{"version": 3, "file": "Browser.js", "sourceRoot": "", "sources": ["../../../../src/api/Browser.ts"], "names": [], "mappings": ";;;AAUA,4DAKwC;AAIxC,+DAAuE;AACvE,+CAM2B;AAC3B,yDAAwE;AA0CxE;;GAEG;AACU,QAAA,qCAAqC,GAAG,IAAI,GAAG,CAG1D;IACA,CAAC,eAAe,EAAE,SAAS,CAAC;IAC5B,CAAC,sBAAsB,EAAE,SAAS,CAAC;IACnC,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;IACrC,CAAC,QAAQ,EAAE,cAAc,CAAC;IAC1B,CAAC,gBAAgB,EAAE,oBAAoB,CAAC;IACxC,CAAC,2BAA2B,EAAE,yBAAyB,CAAC;IACxD,CAAC,iBAAiB,EAAE,oBAAoB,CAAC;IACzC,CAAC,aAAa,EAAE,aAAa,CAAC;IAC9B,CAAC,WAAW,EAAE,SAAS,CAAC;IACxB,CAAC,gBAAgB,EAAE,eAAe,CAAC;IACnC,CAAC,eAAe,EAAE,cAAc,CAAC;IACjC,CAAC,cAAc,EAAE,SAAS,CAAC;IAC3B,CAAC,YAAY,EAAE,cAAc,CAAC;IAC9B,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,eAAe,EAAE,eAAe,CAAC;IAClC,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;IACrC,CAAC,oBAAoB,EAAE,gBAAgB,CAAC;IACxC,CAAC,cAAc,EAAE,aAAa,CAAC;IAC/B,uCAAuC;IACvC,CAAC,YAAY,EAAE,WAAW,CAAC;CAC5B,CAAC,CAAC;AAgHH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsCG;AACH,MAAsB,OAAQ,SAAQ,8BAA2B;IAC/D;;OAEG;IACH;QACE,KAAK,EAAE,CAAC;IACV,CAAC;IAsFD;;;;;;;;;;;;;;OAcG;IACH,KAAK,CAAC,aAAa,CACjB,SAAoD,EACpD,UAAgC,EAAE;QAElC,MAAM,EAAC,OAAO,EAAE,EAAE,GAAG,KAAK,EAAE,MAAM,EAAC,GAAG,OAAO,CAAC;QAC9C,OAAO,MAAM,IAAA,wBAAc,EACzB,IAAA,eAAK,EACH,IAAA,0BAAgB,EAAC,IAAI,mDAA6B,EAClD,IAAA,0BAAgB,EAAC,IAAI,mDAA6B,EAClD,IAAA,cAAI,EAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CACrB,CAAC,IAAI,CACJ,IAAA,qBAAW,EAAC,SAAS,CAAC,EACtB,IAAA,kBAAQ,EAAC,IAAA,yBAAe,EAAC,MAAM,CAAC,EAAE,IAAA,iBAAO,EAAC,EAAE,CAAC,CAAC,CAC/C,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CACpC,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACnC,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC,CAAC,CACH,CAAC;QACF,iBAAiB;QACjB,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;YACpC,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAoCD;;;;;;;OAOG;IACH,KAAK,CAAC,OAAO;QACX,OAAO,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC,OAAO,EAAE,CAAC;IACtD,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,SAAS,CAAC,GAAG,OAAqB;QACtC,OAAO,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,CAAC;IAClE,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,YAAY,CAAC,GAAG,OAAiB;QACrC,OAAO,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC,YAAY,CAAC,GAAG,OAAO,CAAC,CAAC;IACrE,CAAC;IAgBD;;;;OAIG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAOD,gBAAgB;IACP,CAAC,6BAAa,CAAC;QACtB,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;YACnB,OAAO,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,oBAAU,CAAC,CAAC;QAC7C,CAAC;QACD,OAAO,KAAK,IAAI,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,oBAAU,CAAC,CAAC;IAClD,CAAC;IAED,gBAAgB;IAChB,CAAC,kCAAkB,CAAC;QAClB,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;QACtB,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;CAkBF;AApRD,0BAoRC"}