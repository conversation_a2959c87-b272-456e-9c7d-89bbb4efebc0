import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import './Auth.css'

const RoleSelection = () => {
  const [selectedRole, setSelectedRole] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  
  const { user, updateUserProfile, createUserProfile, userProfile } = useAuth()
  const navigate = useNavigate()

  const handleRoleSelection = async (role) => {
    if (!user) {
      setError('User not authenticated')
      return
    }

    setLoading(true)
    setError('')
    setSelectedRole(role)

    try {
      let result
      
      if (userProfile) {
        // Update existing profile
        result = await updateUserProfile({ role })
      } else {
        // Create new profile
        result = await createUserProfile(user.id, {
          email: user.email,
          full_name: user.user_metadata?.full_name || user.email,
          role
        })
      }

      if (result.error) {
        setError(result.error.message)
      } else {
        // Redirect based on role
        if (role === 'vendor') {
          navigate('/vendor/dashboard')
        } else if (role === 'vlogger') {
          navigate('/vlogger/dashboard')
        }
      }
    } catch (err) {
      setError('Failed to set role. Please try again.')
      console.error('Role selection error:', err)
    } finally {
      setLoading(false)
      setSelectedRole('')
    }
  }

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <h1>Choose Your Role 🍛</h1>
          <p>How would you like to use Bhookad?</p>
        </div>

        {error && (
          <div className="error-message">
            {error}
          </div>
        )}

        <div className="role-selection">
          <div 
            className={`role-card ${selectedRole === 'vendor' ? 'selecting' : ''}`}
            onClick={() => !loading && handleRoleSelection('vendor')}
            style={{ 
              opacity: loading && selectedRole !== 'vendor' ? 0.5 : 1,
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            {loading && selectedRole === 'vendor' && (
              <div className="role-loading">Setting up...</div>
            )}
            <div className="role-icon">🏪</div>
            <h3>I'm a Vendor</h3>
            <p>List your street food business and connect with food lovers</p>
            <ul>
              <li>Create your business profile</li>
              <li>Showcase your specialties</li>
              <li>Connect with vloggers</li>
              <li>Grow your customer base</li>
            </ul>
          </div>

          <div 
            className={`role-card ${selectedRole === 'vlogger' ? 'selecting' : ''}`}
            onClick={() => !loading && handleRoleSelection('vlogger')}
            style={{ 
              opacity: loading && selectedRole !== 'vlogger' ? 0.5 : 1,
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            {loading && selectedRole === 'vlogger' && (
              <div className="role-loading">Setting up...</div>
            )}
            <div className="role-icon">📹</div>
            <h3>I'm a Vlogger</h3>
            <p>Discover amazing street food and create content</p>
            <ul>
              <li>Find unique vendors</li>
              <li>Create food content</li>
              <li>Build your audience</li>
              <li>Collaborate with vendors</li>
            </ul>
          </div>
        </div>

        <div className="auth-footer">
          <p style={{ color: '#666', fontSize: '0.85rem' }}>
            You can change your role later in your profile settings
          </p>
        </div>
      </div>
    </div>
  )
}

export default RoleSelection
