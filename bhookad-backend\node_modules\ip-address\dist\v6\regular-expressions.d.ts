export declare function groupPossibilities(possibilities: string[]): string;
export declare function padGroup(group: string): string;
export declare const ADDRESS_BOUNDARY = "[^A-Fa-f0-9:]";
export declare function simpleRegularExpression(groups: string[]): string;
export declare function possibleElisions(elidedGroups: number, moreLeft?: boolean, moreRight?: boolean): string;
//# sourceMappingURL=regular-expressions.d.ts.map