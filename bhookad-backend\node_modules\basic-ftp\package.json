{"name": "basic-ftp", "version": "5.0.5", "description": "FTP client for Node.js, supports FTPS over TLS, IPv6, Async/Await, and Typescript.", "main": "dist/index", "types": "dist/index", "files": ["dist/**/*"], "scripts": {"prepublishOnly": "npm run clean && npm run lint && tsc && mocha", "prepare": "tsc", "test": "npm run prepublishOnly", "clean": "rm -rf dist", "lint": "eslint \"./src/**/*.ts\"", "lint-fix": "eslint --fix \"./src/**/*.ts\"", "dev": "npm run clean && tsc --watch", "tdd": "mocha --watch", "buildOnly": "tsc"}, "repository": {"type": "git", "url": "https://github.com/patrickjuchli/basic-ftp.git"}, "author": "<PERSON> <patrick<PERSON><PERSON><PERSON>@gmail.com>", "license": "MIT", "keywords": ["ftp", "ftps", "promise", "async", "await", "tls", "ipv6", "typescript"], "engines": {"node": ">=10.0.0"}, "devDependencies": {"@types/mocha": "10.0.6", "@types/node": "20.10.4", "@typescript-eslint/eslint-plugin": "6.14.0", "@typescript-eslint/parser": "6.14.0", "eslint": "8.55.0", "mocha": "10.2.0", "typescript": "5.3.3"}}