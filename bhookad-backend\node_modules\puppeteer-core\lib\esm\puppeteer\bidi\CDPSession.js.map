{"version": 3, "file": "CDPSession.js", "sourceRoot": "", "sources": ["../../../../src/bidi/CDPSession.ts"], "names": [], "mappings": "AAQA,OAAO,EAAC,UAAU,EAAC,MAAM,sBAAsB,CAAC;AAEhD,OAAO,EAAC,gBAAgB,EAAE,oBAAoB,EAAC,MAAM,qBAAqB,CAAC;AAC3E,OAAO,EAAC,QAAQ,EAAC,MAAM,qBAAqB,CAAC;AAK7C;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,UAAU;IAC5C,MAAM,CAAC,QAAQ,GAAG,IAAI,GAAG,EAA0B,CAAC;IAEpD,SAAS,GAAG,KAAK,CAAC;IACT,WAAW,CAAkB;IAC7B,UAAU,GAAG,QAAQ,CAAC,MAAM,EAAU,CAAC;IACvC,KAAK,CAAY;IAE1B,YAAY,KAAgB,EAAE,SAAkB;QAC9C,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,YAAY,EAAE,CAAC;YAC9C,OAAO;QACT,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC;QAC1D,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAE9B,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACnC,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC/C,CAAC;aAAM,CAAC;YACN,CAAC,KAAK,IAAI,EAAE;gBACV,IAAI,CAAC;oBACH,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,qBAAqB,EAAE;wBAC5D,OAAO,EAAE,KAAK,CAAC,GAAG;qBACnB,CAAC,CAAC;oBACH,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,OAAQ,CAAC,CAAC;oBACzC,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,OAAQ,EAAE,IAAI,CAAC,CAAC;gBACrD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAc,CAAC,CAAC;gBACzC,CAAC;YACH,CAAC,CAAC,EAAE,CAAC;QACP,CAAC;QAED,qCAAqC;QACrC,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAY,EAAE,IAAI,CAAC,CAAC;IACvE,CAAC;IAEQ,UAAU;QACjB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAa,QAAQ;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEQ,KAAK,CAAC,IAAI,CACjB,MAAS,EACT,MAAqD,EACrD,OAAwB;QAExB,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACnC,MAAM,IAAI,oBAAoB,CAC5B,qFAAqF,CACtF,CAAC;QACJ,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,gBAAgB,CACxB,mBAAmB,MAAM,0DAA0D,CACpF,CAAC;QACJ,CAAC;QACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;QACrD,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAC1C,sBAAsB,EACtB;YACE,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,MAAM;YACd,OAAO;SACR,EACD,OAAO,EAAE,OAAO,CACjB,CAAC;QACF,OAAO,MAAM,CAAC,MAAM,CAAC;IACvB,CAAC;IAEQ,KAAK,CAAC,MAAM;QACnB,IACE,IAAI,CAAC,WAAW,KAAK,SAAS;YAC9B,IAAI,CAAC,WAAW,CAAC,MAAM;YACvB,IAAI,CAAC,SAAS,EACd,CAAC;YACD,OAAO;QACT,CAAC;QACD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBACtD,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;aACrB,CAAC,CAAC;QACL,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO,GAAG,GAAS,EAAE;QACnB,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QAC1C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACxB,CAAC,CAAC;IAEO,EAAE;QACT,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACtC,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;IAChD,CAAC"}