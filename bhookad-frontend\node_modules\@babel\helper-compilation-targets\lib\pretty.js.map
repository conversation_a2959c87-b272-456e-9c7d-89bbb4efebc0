{"version": 3, "names": ["_semver", "require", "_targets", "prettifyVersion", "version", "major", "minor", "patch", "semver", "parse", "parts", "push", "join", "prettifyTargets", "targets", "Object", "keys", "reduce", "results", "target", "value", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["../src/pretty.ts"], "sourcesContent": ["import semver from \"semver\";\nimport { unreleasedLabels } from \"./targets.ts\";\nimport type { Targets, Target } from \"./types.ts\";\n\nexport function prettifyVersion(version: string) {\n  if (typeof version !== \"string\") {\n    return version;\n  }\n\n  const { major, minor, patch } = semver.parse(version);\n\n  const parts = [major];\n\n  if (minor || patch) {\n    parts.push(minor);\n  }\n\n  if (patch) {\n    parts.push(patch);\n  }\n\n  return parts.join(\".\");\n}\n\nexport function prettifyTargets(targets: Targets): Targets {\n  return Object.keys(targets).reduce((results, target: Target) => {\n    let value = targets[target];\n\n    const unreleasedLabel =\n      // @ts-expect-error undefined is strictly compared with string later\n      unreleasedLabels[target];\n    if (typeof value === \"string\" && unreleasedLabel !== value) {\n      value = prettifyVersion(value);\n    }\n\n    results[target] = value;\n    return results;\n  }, {} as Targets);\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAGO,SAASE,eAAeA,CAACC,OAAe,EAAE;EAC/C,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAOA,OAAO;EAChB;EAEA,MAAM;IAAEC,KAAK;IAAEC,KAAK;IAAEC;EAAM,CAAC,GAAGC,OAAM,CAACC,KAAK,CAACL,OAAO,CAAC;EAErD,MAAMM,KAAK,GAAG,CAACL,KAAK,CAAC;EAErB,IAAIC,KAAK,IAAIC,KAAK,EAAE;IAClBG,KAAK,CAACC,IAAI,CAACL,KAAK,CAAC;EACnB;EAEA,IAAIC,KAAK,EAAE;IACTG,KAAK,CAACC,IAAI,CAACJ,KAAK,CAAC;EACnB;EAEA,OAAOG,KAAK,CAACE,IAAI,CAAC,GAAG,CAAC;AACxB;AAEO,SAASC,eAAeA,CAACC,OAAgB,EAAW;EACzD,OAAOC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,MAAM,CAAC,CAACC,OAAO,EAAEC,MAAc,KAAK;IAC9D,IAAIC,KAAK,GAAGN,OAAO,CAACK,MAAM,CAAC;IAE3B,MAAME,eAAe,GAEnBC,yBAAgB,CAACH,MAAM,CAAC;IAC1B,IAAI,OAAOC,KAAK,KAAK,QAAQ,IAAIC,eAAe,KAAKD,KAAK,EAAE;MAC1DA,KAAK,GAAGjB,eAAe,CAACiB,KAAK,CAAC;IAChC;IAEAF,OAAO,CAACC,MAAM,CAAC,GAAGC,KAAK;IACvB,OAAOF,OAAO;EAChB,CAAC,EAAE,CAAC,CAAY,CAAC;AACnB", "ignoreList": []}