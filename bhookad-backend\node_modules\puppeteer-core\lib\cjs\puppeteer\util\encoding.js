"use strict";
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.stringToTypedArray = stringToTypedArray;
exports.stringToBase64 = stringToBase64;
exports.typedArrayToBase64 = typedArrayToBase64;
exports.mergeUint8Arrays = mergeUint8Arrays;
/**
 * @internal
 */
function stringToTypedArray(string, base64Encoded = false) {
    if (base64Encoded) {
        // TODO: use
        // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8Array/fromBase64
        // once available.
        if (typeof Buffer === 'function') {
            return Buffer.from(string, 'base64');
        }
        return Uint8Array.from(atob(string), m => {
            return m.codePointAt(0);
        });
    }
    return new TextEncoder().encode(string);
}
/**
 * @internal
 */
function stringToBase64(str) {
    return typedArrayToBase64(new TextEncoder().encode(str));
}
/**
 * @internal
 */
function typedArrayToBase64(typedArray) {
    // chunkSize should be less V8 limit on number of arguments!
    // https://github.com/v8/v8/blob/d3de848bea727518aee94dd2fd42ba0b62037a27/src/objects/code.h#L444
    const chunkSize = 65534;
    const chunks = [];
    for (let i = 0; i < typedArray.length; i += chunkSize) {
        const chunk = typedArray.subarray(i, i + chunkSize);
        chunks.push(String.fromCodePoint.apply(null, chunk));
    }
    const binaryString = chunks.join('');
    return btoa(binaryString);
}
/**
 * @internal
 */
function mergeUint8Arrays(items) {
    let length = 0;
    for (const item of items) {
        length += item.length;
    }
    // Create a new array with total length and merge all source arrays.
    const result = new Uint8Array(length);
    let offset = 0;
    for (const item of items) {
        result.set(item, offset);
        offset += item.length;
    }
    return result;
}
//# sourceMappingURL=encoding.js.map