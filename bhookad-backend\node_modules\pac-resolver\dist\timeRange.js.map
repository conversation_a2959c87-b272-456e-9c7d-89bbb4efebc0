{"version": 3, "file": "timeRange.js", "sourceRoot": "", "sources": ["../src/timeRange.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwCG;;AAEH,SAAwB,SAAS;IAChC,8CAA8C;IAC9C,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACnD,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC3B,MAAM,UAAU,GAAG,OAAO,KAAK,KAAK,CAAC;IACrC,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;IAE/B,IAAI,CAAC,UAAU,EAAE;QAChB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KACnB;IAED,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;IAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAErD,kBAAkB;IAClB,IAAI,QAAQ,KAAK,CAAC,EAAE;QACnB,MAAM,GAAG,cAAc,CAAC,UAAU,EAAE,WAAW,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC;QAEpE,0BAA0B;KAC1B;SAAM,IAAI,QAAQ,KAAK,CAAC,EAAE;QAC1B,MAAM,WAAW,GAAG,cAAc,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QAC5D,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,WAAW,IAAI,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QAEvE,sCAAsC;KACtC;SAAM,IAAI,QAAQ,KAAK,CAAC,EAAE;QAC1B,MAAM,GAAG,YAAY,CACpB,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EACtD,mBAAmB,CAClB,cAAc,CAAC,UAAU,EAAE,WAAW,CAAC,EACvC,gBAAgB,CAAC,UAAU,EAAE,WAAW,CAAC,EACzC,CAAC,CACD,EACD,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CACvD,CAAC;QAEF,kDAAkD;KAClD;SAAM,IAAI,QAAQ,KAAK,CAAC,EAAE;QAC1B,MAAM,GAAG,YAAY,CACpB,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EACnE,mBAAmB,CAClB,cAAc,CAAC,UAAU,EAAE,WAAW,CAAC,EACvC,gBAAgB,CAAC,UAAU,EAAE,WAAW,CAAC,EACzC,gBAAgB,CAAC,UAAU,EAAE,WAAW,CAAC,CACzC,EACD,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CACnE,CAAC;KACF;IAED,OAAO,MAAM,CAAC;AACf,CAAC;AAlDD,4BAkDC;AAED,SAAS,mBAAmB,CAAC,EAAU,EAAE,EAAU,EAAE,EAAU;IAC9D,OAAO,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACjC,CAAC;AAED,SAAS,cAAc,CAAC,GAAY,EAAE,WAAiB;IACtD,OAAO,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;AACjE,CAAC;AAED,SAAS,gBAAgB,CAAC,GAAY,EAAE,WAAiB;IACxD,OAAO,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;AACrE,CAAC;AAED,SAAS,gBAAgB,CAAC,GAAY,EAAE,WAAiB;IACxD,OAAO,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;AACrE,CAAC;AAED,2BAA2B;AAC3B,SAAS,YAAY,CAAC,KAAa,EAAE,KAAa,EAAE,MAAc;IACjE,OAAO,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC;AAC1C,CAAC"}