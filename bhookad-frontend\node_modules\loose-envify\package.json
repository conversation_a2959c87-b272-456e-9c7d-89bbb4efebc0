{"name": "loose-envify", "version": "1.4.0", "description": "Fast (and loose) selective `process.env` replacer using js-tokens instead of an AST", "keywords": ["environment", "variables", "browserify", "browserify-transform", "transform", "source", "configuration"], "homepage": "https://github.com/zertosh/loose-envify", "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>>", "main": "index.js", "bin": {"loose-envify": "cli.js"}, "repository": {"type": "git", "url": "git://github.com/zertosh/loose-envify.git"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "devDependencies": {"browserify": "^13.1.1", "envify": "^3.4.0", "tap": "^8.0.0"}}