import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from './contexts/AuthContext'

// Landing Page Components
import Header from './components/Header'
import Hero from './components/Hero'
import HowItWorks from './components/HowItWorks'
import TrendingVendors from './components/TrendingVendors'
import SuccessStories from './components/SuccessStories'
import About from './components/About'
import Contact from './components/Contact'
import Footer from './components/Footer'
import ApiTest from './components/ApiTest'

// Authentication Components
import Login from './components/auth/Login'
import Register from './components/auth/Register'
import AuthCallback from './components/auth/AuthCallback'
import RoleSelection from './components/auth/RoleSelection'
import ProtectedRoute from './components/auth/ProtectedRoute'

// Dashboard Components
import VendorDashboard from './components/dashboards/VendorDashboard'
import VloggerDashboard from './components/dashboards/VloggerDashboard'

import './App.css'

// Landing Page Component
const LandingPage = () => (
  <div className="App">
    <Header />
    <Hero />
    <HowItWorks />
    <TrendingVendors />
    <SuccessStories />
    <About />
    <Contact />
    <Footer />
    <ApiTest />
  </div>
)

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={<LandingPage />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/auth/callback" element={<AuthCallback />} />

          {/* Protected Routes */}
          <Route
            path="/select-role"
            element={
              <ProtectedRoute>
                <RoleSelection />
              </ProtectedRoute>
            }
          />

          {/* Vendor Routes */}
          <Route
            path="/vendor/dashboard"
            element={
              <ProtectedRoute requiredRole="vendor">
                <VendorDashboard />
              </ProtectedRoute>
            }
          />

          {/* Vlogger Routes */}
          <Route
            path="/vlogger/dashboard"
            element={
              <ProtectedRoute requiredRole="vlogger">
                <VloggerDashboard />
              </ProtectedRoute>
            }
          />

          {/* Catch all route - redirect to home */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Router>
    </AuthProvider>
  )
}

export default App
