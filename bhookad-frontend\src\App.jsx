import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from './contexts/AuthContext'

// Basic Landing Page Components
import Header from './components/Header'
import Hero from './components/Hero'
import Footer from './components/Footer'

// Authentication Components
import Login from './components/auth/Login'
import Register from './components/auth/Register'
import AuthCallback from './components/auth/AuthCallback'
import RoleSelection from './components/auth/RoleSelection'
import ProtectedRoute from './components/auth/ProtectedRoute'

// Dashboard Components
import VendorDashboard from './components/dashboards/VendorDashboard'
import VloggerDashboard from './components/dashboards/VloggerDashboard'

import './App.css'

// Simple Landing Page Component (step by step restore)
const LandingPage = () => (
  <div className="App">
    <Header />
    <Hero />
    <Footer />
  </div>
)

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={<LandingPage />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/auth/callback" element={<AuthCallback />} />

          {/* Protected Routes */}
          <Route
            path="/select-role"
            element={
              <ProtectedRoute>
                <RoleSelection />
              </ProtectedRoute>
            }
          />
          <Route
            path="/vendor/dashboard"
            element={
              <ProtectedRoute>
                <VendorDashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path="/vlogger/dashboard"
            element={
              <ProtectedRoute>
                <VloggerDashboard />
              </ProtectedRoute>
            }
          />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Router>
    </AuthProvider>
  )
}

export default App
