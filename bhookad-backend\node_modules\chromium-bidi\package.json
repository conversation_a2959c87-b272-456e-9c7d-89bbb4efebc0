{"name": "chromium-bidi", "version": "5.1.0", "description": "An implementation of the WebDriver BiDi protocol for Chromium implemented as a JavaScript layer translating between BiDi and CDP, running inside a Chrome tab.", "scripts": {"build": "wireit", "clean": "node tools/clean.mjs", "e2e:headful": "HEADLESS=false npm run e2e --", "e2e:headless": "npm run e2e:new-headless --", "e2e:new-headless": "HEADLESS=new npm run e2e --", "e2e:old-headless": "HEADLESS=old npm run e2e --", "e2e": "wireit", "flake8": "flake8 examples/ tests/", "format": "npm run pre-commit --", "format:eslint": "eslint --cache --fix .", "format:prettier": "prettier --cache --write .", "pre-commit": "pre-commit run --hook-stage manual --all-files", "prepare": "wireit", "rollup": "wireit", "server": "wireit", "test": "wireit", "tsc": "wireit", "unit": "wireit", "wpt": "wireit", "wpt:all": "wireit", "yapf": "yapf -i --parallel --recursive --exclude=wpt examples/ tests/"}, "exports": {"./*": {"import": "./lib/esm/*", "require": "./lib/cjs/*"}, "./lib/cjs/*": {"types": "./lib/cjs/*", "import": "./lib/cjs/*", "require": "./lib/cjs/*"}}, "wireit": {"build": {"dependencies": ["rollup", "tsc"]}, "e2e": {"command": "tools/run-e2e.mjs", "files": ["tools/run-e2e.mjs ", "pytest.ini", "tests/**/*.py"], "dependencies": ["build"]}, "prepare": {"dependencies": ["build"]}, "rollup": {"command": "rollup -c", "dependencies": ["tsc"], "files": ["lib/esm/bidiMapper/", "rollup.config.mjs"], "output": ["lib/iife/mapperTab.js"]}, "server": {"command": "tools/run-bidi-server.mjs", "files": ["tools/run-bidi-server.mjs"], "service": {"readyWhen": {"lineMatches": "(BiDi server|ChromeDriver) was started successfully"}}, "dependencies": ["rollup"]}, "test": {"dependencies": ["unit", "e2e", "wpt"]}, "tsc": {"dependencies": ["tsc:esm", "tsc:cjs"]}, "tsc:esm": {"command": "tsc --build src/tsconfig.json --pretty && node ./tools/generate-module-package-json.mjs lib/esm/package.json", "clean": "if-file-deleted", "files": ["tsconfig.base.json", "src/**/tsconfig*.json", "src/**/*.ts"], "output": ["lib/esm/**"]}, "tsc:cjs": {"command": "tsc --build src/tsconfig.cjs.json --pretty", "clean": "if-file-deleted", "files": ["tsconfig.base.json", "src/**/tsconfig*.cjs.json", "src/**/*.ts"], "output": ["lib/cjs/**"]}, "unit": {"command": "mocha", "dependencies": ["tsc"]}, "wpt": {"command": "tools/run-wpt.mjs", "files": ["tools/run-wpt.mjs", "wpt/tools/webdriver/**/*.py", "wpt/webdriver/tests/**/*.py", "wpt-metadata/**/*.ini"], "dependencies": ["rollup"]}, "wpt:all": {"command": "tools/run-wpt-all.mjs", "files": ["tools/run-wpt.mjs", "tools/run-wpt-all.mjs", "wpt/tools/webdriver/**/*.py", "wpt/webdriver/tests/**/*.py", "wpt-metadata/**/*.ini"], "dependencies": ["rollup"]}}, "files": ["lib", "!lib/**/*.spec.*", "!*.tsbuil<PERSON><PERSON>", ".browser"], "repository": {"type": "git", "url": "https://github.com/GoogleChromeLabs/chromium-bidi.git"}, "author": "The Chromium Authors", "license": "Apache-2.0", "peerDependencies": {"devtools-protocol": "*"}, "devDependencies": {"@actions/core": "1.11.1", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.25.1", "@puppeteer/browsers": "2.10.2", "@rollup/plugin-node-resolve": "16.0.1", "@rollup/wasm-node": "4.40.1", "@types/chai": "4.3.17", "@types/chai-as-promised": "7.1.8", "@types/debug": "4.1.12", "@types/mocha": "10.0.10", "@types/node": "20.17.32", "@types/sinon": "17.0.4", "@types/websocket": "1.0.10", "@types/ws": "8.18.1", "@types/yargs": "17.0.33", "@typescript-eslint/eslint-plugin": "8.31.0", "@typescript-eslint/parser": "8.31.0", "chai": "4.5.0", "chai-as-promised": "7.1.2", "debug": "4.4.0", "devtools-protocol": "0.0.1453708", "eslint": "9.12.0", "eslint-config-prettier": "10.1.2", "eslint-import-resolver-typescript": "4.3.4", "eslint-plugin-import": "2.31.0", "eslint-plugin-mocha": "11.0.0", "eslint-plugin-prettier": "5.2.6", "eslint-plugin-promise": "7.2.1", "globals": "16.0.0", "gts": "6.0.2", "mocha": "11.1.0", "pkg-dir": "8.0.0", "prettier": "3.5.3", "rimraf": "6.0.1", "rollup": "4.40.1", "rollup-plugin-cleanup": "3.2.1", "rollup-plugin-license": "3.6.0", "selenium-webdriver": "4.31.0", "semver": "7.7.1", "sinon": "20.0.0", "source-map-support": "0.5.21", "tslib": "2.8.1", "typescript": "5.8.3", "webdriverio": "9.12.7", "websocket": "1.0.35", "wireit": "0.14.12", "ws": "8.18.1", "yargs": "17.7.2"}, "dependencies": {"mitt": "^3.0.1", "zod": "^3.24.1"}}