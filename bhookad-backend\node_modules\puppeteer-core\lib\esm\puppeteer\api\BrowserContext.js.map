{"version": 3, "file": "BrowserContext.js", "sourceRoot": "", "sources": ["../../../../src/api/BrowserContext.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EACL,cAAc,EACd,IAAI,EACJ,KAAK,EACL,QAAQ,GACT,MAAM,gCAAgC,CAAC;AAExC,OAAO,EAAC,YAAY,EAAiB,MAAM,2BAA2B,CAAC;AACvE,OAAO,EACL,UAAU,EACV,gBAAgB,EAChB,WAAW,EACX,OAAO,GACR,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAC,kBAAkB,EAAE,aAAa,EAAC,MAAM,uBAAuB,CAAC;AACxE,OAAO,EAAC,KAAK,EAAC,MAAM,kBAAkB,CAAC;AAyCvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqCG;AAEH,MAAM,OAAgB,cAAe,SAAQ,YAAkC;IAC7E;;OAEG;IACH;QACE,KAAK,EAAE,CAAC;IACV,CAAC;IAQD;;OAEG;IACH,oBAAoB,CAAS;IAC7B,0BAA0B,GAAG,CAAC,CAAC;IAE/B;;OAEG;IACH,eAAe;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,KAAK,EAAE,CAAC;QACvD,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAClC,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE;YACxB,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,0BAA0B,KAAK,CAAC,EAAE,CAAC;gBAC1C,gEAAgE;gBAChE,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;YACxC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,2BAA2B;QAGzB,OAAO,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE,CAAC;IAC9C,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,KAAK,CAAC,aAAa,CACjB,SAAoD,EACpD,UAAgC,EAAE;QAElC,MAAM,EAAC,OAAO,EAAE,EAAE,GAAG,KAAK,EAAC,GAAG,OAAO,CAAC;QACtC,OAAO,MAAM,cAAc,CACzB,KAAK,CACH,gBAAgB,CAAC,IAAI,0DAAoC,EACzD,gBAAgB,CAAC,IAAI,0DAAoC,EACzD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CACrB,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CACtD,CAAC;IACJ,CAAC;IAmFD;;;OAGG;IACH,KAAK,CAAC,YAAY,CAAC,GAAG,OAAiB;QACrC,OAAO,MAAM,IAAI,CAAC,SAAS,CACzB,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACtB,OAAO;gBACL,GAAG,MAAM;gBACT,OAAO,EAAE,CAAC;aACX,CAAC;QACJ,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,IAAI,EAAE;QACJ,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,gBAAgB;IACP,CAAC,aAAa,CAAC;QACtB,OAAO,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC7C,CAAC;IAED,gBAAgB;IAChB,CAAC,kBAAkB,CAAC;QAClB,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;CACF"}