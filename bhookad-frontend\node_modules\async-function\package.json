{"name": "async-function", "version": "1.0.0", "description": "A function that returns the normally hidden `AsyncFunction` constructor", "main": "./legacy.js", "jsnext:main": "./index.mjs", "module": "./index.mjs", "exports": {".": [{"module-sync": "./require.mjs", "import": "./index.mjs", "default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run tests-only", "posttest": "npx npm@\">=10.2\" audit --production", "tests-only": "nyc tape 'test/**/*.js'", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/async-function.git"}, "keywords": ["async", "await", "function", "native"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ljharb/async-function/issues"}, "homepage": "https://github.com/ljharb/async-function#readme", "devDependencies": {"@arethetypeswrong/cli": "^0.17.3", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/semver": "^6.2.7", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "get-proto": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "semver": "^6.3.1", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "testling": {"files": "test/index.js"}}