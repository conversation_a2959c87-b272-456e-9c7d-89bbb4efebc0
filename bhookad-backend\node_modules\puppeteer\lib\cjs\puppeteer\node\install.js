"use strict";
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.downloadBrowsers = downloadBrowsers;
const browsers_1 = require("@puppeteer/browsers");
const revisions_js_1 = require("puppeteer-core/internal/revisions.js");
const getConfiguration_js_1 = require("../getConfiguration.js");
async function downloadBrowser({ browser, configuration, cacheDir, platform, }) {
    const unresolvedBuildId = configuration?.version || revisions_js_1.PUPPETEER_REVISIONS[browser] || 'latest';
    const baseUrl = configuration?.downloadBaseUrl;
    const buildId = await (0, browsers_1.resolveBuildId)(browser, platform, unresolvedBuildId);
    try {
        const result = await (0, browsers_1.install)({
            browser,
            cacheDir,
            platform,
            buildId,
            downloadProgressCallback: 'default',
            baseUrl,
            buildIdAlias: buildId !== unresolvedBuildId ? unresolvedBuildId : undefined,
        });
        logPolitely(`${browser} (${result.buildId}) downloaded to ${result.path}`);
    }
    catch (error) {
        throw new Error(`ERROR: Failed to set up ${browser} v${buildId}! Set "PUPPETEER_SKIP_DOWNLOAD" env variable to skip download.`, {
            cause: error,
        });
    }
}
/**
 * @internal
 */
async function downloadBrowsers() {
    overrideProxy();
    const configuration = (0, getConfiguration_js_1.getConfiguration)();
    if (configuration.skipDownload) {
        logPolitely('**INFO** Skipping downloading browsers as instructed.');
        return;
    }
    const platform = (0, browsers_1.detectBrowserPlatform)();
    if (!platform) {
        throw new Error('The current platform is not supported.');
    }
    const cacheDir = configuration.cacheDirectory;
    const installationJobs = [];
    if (configuration.chrome?.skipDownload) {
        logPolitely('**INFO** Skipping Chrome download as instructed.');
    }
    else {
        const browser = browsers_1.Browser.CHROME;
        installationJobs.push(downloadBrowser({
            browser,
            configuration: configuration[browser] ?? {},
            cacheDir,
            platform,
        }));
    }
    if (configuration['chrome-headless-shell']?.skipDownload) {
        logPolitely('**INFO** Skipping Chrome download as instructed.');
    }
    else {
        const browser = browsers_1.Browser.CHROMEHEADLESSSHELL;
        installationJobs.push(downloadBrowser({
            browser,
            configuration: configuration[browser] ?? {},
            cacheDir,
            platform,
        }));
    }
    if (configuration.firefox?.skipDownload) {
        logPolitely('**INFO** Skipping Firefox download as instructed.');
    }
    else {
        const browser = browsers_1.Browser.FIREFOX;
        installationJobs.push(downloadBrowser({
            browser,
            configuration: configuration[browser] ?? {},
            cacheDir,
            platform,
        }));
    }
    try {
        await Promise.all(installationJobs);
    }
    catch (error) {
        console.error(error);
        process.exit(1);
    }
}
/**
 * @internal
 */
function logPolitely(toBeLogged) {
    const logLevel = process.env['npm_config_loglevel'] || '';
    const logLevelDisplay = ['silent', 'error', 'warn'].indexOf(logLevel) > -1;
    if (!logLevelDisplay) {
        console.log(toBeLogged);
    }
}
/**
 * @internal
 */
function overrideProxy() {
    // Override current environment proxy settings with npm configuration, if any.
    const NPM_HTTPS_PROXY = process.env['npm_config_https_proxy'] || process.env['npm_config_proxy'];
    const NPM_HTTP_PROXY = process.env['npm_config_http_proxy'] || process.env['npm_config_proxy'];
    const NPM_NO_PROXY = process.env['npm_config_no_proxy'];
    if (NPM_HTTPS_PROXY) {
        process.env['HTTPS_PROXY'] = NPM_HTTPS_PROXY;
    }
    if (NPM_HTTP_PROXY) {
        process.env['HTTP_PROXY'] = NPM_HTTP_PROXY;
    }
    if (NPM_NO_PROXY) {
        process.env['NO_PROXY'] = NPM_NO_PROXY;
    }
}
//# sourceMappingURL=install.js.map