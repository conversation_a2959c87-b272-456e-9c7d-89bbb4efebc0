{"version": 3, "names": ["_checkInRHS", "require", "_setFunctionName", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "applyDecs2301Factory", "createAddInitializerMethod", "initializers", "decoratorFinishedRef", "addInitializer", "initializer", "assertNotFinished", "assertCallable", "push", "assertInstanceIfPrivate", "has", "target", "TypeError", "memberDec", "dec", "name", "desc", "kind", "isStatic", "isPrivate", "value", "hasPrivateBrand", "kindStr", "ctx", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "static", "private", "v", "get", "set", "t", "call", "bind", "access", "fnName", "Error", "fn", "hint", "assertValidReturnValue", "type", "undefined", "init", "curryThis1", "curryThis2", "applyMemberDec", "ret", "base", "decInfo", "decs", "prefix", "setFunctionName", "Object", "getOwnPropertyDescriptor", "newValue", "i", "length", "newInit", "instance", "ownInitializers", "originalInitializer", "args", "defineProperty", "applyMemberDecs", "Class", "decInfos", "instanceBrand", "protoInitializers", "staticInitializers", "staticBrand", "existingProtoNonFields", "Map", "existingStaticNonFields", "Array", "isArray", "_", "checkInRHS", "prototype", "existingNonFields", "existingKind", "pushInitializers", "applyClassDecs", "targetClass", "classDecs", "newClass", "nextNewClass", "applyDecs2301", "memberDecs", "e", "c", "exports", "default"], "sources": ["../../src/helpers/applyDecs2301.js"], "sourcesContent": ["/* @minVersion 7.21.0 */\n/* @onlyBabel7 */\n\nimport checkInRHS from \"checkInRHS\";\nimport setFunctionName from \"setFunctionName\";\nimport toPropertyKey from \"toPropertyKey\";\n\n/**\n  Enums are used in this file, but not assigned to vars to avoid non-hoistable values\n\n  CONSTRUCTOR = 0;\n  PUBLIC = 1;\n  PRIVATE = 2;\n\n  FIELD = 0;\n  ACCESSOR = 1;\n  METHOD = 2;\n  GETTER = 3;\n  SETTER = 4;\n\n  STATIC = 5;\n\n  CLASS = 10; // only used in assertValidReturnValue\n*/\n\nfunction applyDecs2301Factory() {\n  function createAddInitializerMethod(initializers, decoratorFinishedRef) {\n    return function addInitializer(initializer) {\n      assertNotFinished(decoratorFinishedRef, \"addInitializer\");\n      assertCallable(initializer, \"An initializer\");\n      initializers.push(initializer);\n    };\n  }\n\n  function assertInstanceIfPrivate(has, target) {\n    if (!has(target)) {\n      throw new TypeError(\n        \"Attempted to access private element on non-instance\",\n      );\n    }\n  }\n\n  function memberDec(\n    dec,\n    name,\n    desc,\n    initializers,\n    kind,\n    isStatic,\n    isPrivate,\n    value,\n    hasPrivateBrand,\n  ) {\n    var kindStr;\n\n    switch (kind) {\n      case 1 /* ACCESSOR */:\n        kindStr = \"accessor\";\n        break;\n      case 2 /* METHOD */:\n        kindStr = \"method\";\n        break;\n      case 3 /* GETTER */:\n        kindStr = \"getter\";\n        break;\n      case 4 /* SETTER */:\n        kindStr = \"setter\";\n        break;\n      default:\n        kindStr = \"field\";\n    }\n\n    var ctx = {\n      kind: kindStr,\n      name: isPrivate ? \"#\" + name : toPropertyKey(name),\n      static: isStatic,\n      private: isPrivate,\n    };\n\n    var decoratorFinishedRef = { v: false };\n\n    if (kind !== 0 /* FIELD */) {\n      ctx.addInitializer = createAddInitializerMethod(\n        initializers,\n        decoratorFinishedRef,\n      );\n    }\n\n    var get, set;\n    if (!isPrivate && (kind === 0 /* FIELD */ || kind === 2) /* METHOD */) {\n      get = function (target) {\n        return target[name];\n      };\n      if (kind === 0 /* FIELD */) {\n        set = function (target, v) {\n          target[name] = v;\n        };\n      }\n    } else if (kind === 2 /* METHOD */) {\n      // Assert: isPrivate is true.\n      get = function (target) {\n        assertInstanceIfPrivate(hasPrivateBrand, target);\n        return desc.value;\n      };\n    } else {\n      // Assert: If kind === 0, then isPrivate is true.\n      var t = kind === 0 /* FIELD */ || kind === 1; /* ACCESSOR */\n      if (t || kind === 3 /* GETTER */) {\n        if (isPrivate) {\n          get = function (target) {\n            assertInstanceIfPrivate(hasPrivateBrand, target);\n            return desc.get.call(target);\n          };\n        } else {\n          get = function (target) {\n            return desc.get.call(target);\n          };\n        }\n      }\n      if (t || kind === 4 /* SETTER */) {\n        if (isPrivate) {\n          set = function (target, value) {\n            assertInstanceIfPrivate(hasPrivateBrand, target);\n            desc.set.call(target, value);\n          };\n        } else {\n          set = function (target, value) {\n            desc.set.call(target, value);\n          };\n        }\n      }\n    }\n    var has = isPrivate\n      ? hasPrivateBrand.bind()\n      : function (target) {\n          return name in target;\n        };\n    ctx.access =\n      get && set\n        ? { get: get, set: set, has: has }\n        : get\n          ? { get: get, has: has }\n          : { set: set, has: has };\n\n    try {\n      return dec(value, ctx);\n    } finally {\n      decoratorFinishedRef.v = true;\n    }\n  }\n\n  function assertNotFinished(decoratorFinishedRef, fnName) {\n    if (decoratorFinishedRef.v) {\n      throw new Error(\n        \"attempted to call \" + fnName + \" after decoration was finished\",\n      );\n    }\n  }\n\n  function assertCallable(fn, hint) {\n    if (typeof fn !== \"function\") {\n      throw new TypeError(hint + \" must be a function\");\n    }\n  }\n\n  function assertValidReturnValue(kind, value) {\n    var type = typeof value;\n\n    if (kind === 1 /* ACCESSOR */) {\n      if (type !== \"object\" || value === null) {\n        throw new TypeError(\n          \"accessor decorators must return an object with get, set, or init properties or void 0\",\n        );\n      }\n      if (value.get !== undefined) {\n        assertCallable(value.get, \"accessor.get\");\n      }\n      if (value.set !== undefined) {\n        assertCallable(value.set, \"accessor.set\");\n      }\n      if (value.init !== undefined) {\n        assertCallable(value.init, \"accessor.init\");\n      }\n    } else if (type !== \"function\") {\n      var hint;\n      if (kind === 0 /* FIELD */) {\n        hint = \"field\";\n      } else if (kind === 10 /* CLASS */) {\n        hint = \"class\";\n      } else {\n        hint = \"method\";\n      }\n      throw new TypeError(\n        hint + \" decorators must return a function or void 0\",\n      );\n    }\n  }\n\n  function curryThis1(fn) {\n    return function () {\n      return fn(this);\n    };\n  }\n  function curryThis2(fn) {\n    return function (value) {\n      fn(this, value);\n    };\n  }\n\n  function applyMemberDec(\n    ret,\n    base,\n    decInfo,\n    name,\n    kind,\n    isStatic,\n    isPrivate,\n    initializers,\n    hasPrivateBrand,\n  ) {\n    var decs = decInfo[0];\n\n    var desc, init, prefix, value;\n\n    if (isPrivate) {\n      if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n        desc = {\n          get: curryThis1(decInfo[3]),\n          set: curryThis2(decInfo[4]),\n        };\n        prefix = \"get\";\n      } else {\n        if (kind === 3 /* GETTER */) {\n          desc = {\n            get: decInfo[3],\n          };\n          prefix = \"get\";\n        } else if (kind === 4 /* SETTER */) {\n          desc = {\n            set: decInfo[3],\n          };\n          prefix = \"set\";\n        } else {\n          desc = {\n            value: decInfo[3],\n          };\n        }\n      }\n      if (kind !== 0 /* FIELD */) {\n        if (kind === 1 /* ACCESSOR */) {\n          setFunctionName(desc.set, \"#\" + name, \"set\");\n        }\n        setFunctionName(desc[prefix || \"value\"], \"#\" + name, prefix);\n      }\n    } else if (kind !== 0 /* FIELD */) {\n      desc = Object.getOwnPropertyDescriptor(base, name);\n    }\n\n    if (kind === 1 /* ACCESSOR */) {\n      value = {\n        get: desc.get,\n        set: desc.set,\n      };\n    } else if (kind === 2 /* METHOD */) {\n      value = desc.value;\n    } else if (kind === 3 /* GETTER */) {\n      value = desc.get;\n    } else if (kind === 4 /* SETTER */) {\n      value = desc.set;\n    }\n\n    var newValue, get, set;\n\n    if (typeof decs === \"function\") {\n      newValue = memberDec(\n        decs,\n        name,\n        desc,\n        initializers,\n        kind,\n        isStatic,\n        isPrivate,\n        value,\n        hasPrivateBrand,\n      );\n\n      if (newValue !== void 0) {\n        assertValidReturnValue(kind, newValue);\n\n        if (kind === 0 /* FIELD */) {\n          init = newValue;\n        } else if (kind === 1 /* ACCESSOR */) {\n          init = newValue.init;\n          get = newValue.get || value.get;\n          set = newValue.set || value.set;\n\n          value = { get: get, set: set };\n        } else {\n          value = newValue;\n        }\n      }\n    } else {\n      for (var i = decs.length - 1; i >= 0; i--) {\n        var dec = decs[i];\n\n        newValue = memberDec(\n          dec,\n          name,\n          desc,\n          initializers,\n          kind,\n          isStatic,\n          isPrivate,\n          value,\n          hasPrivateBrand,\n        );\n\n        if (newValue !== void 0) {\n          assertValidReturnValue(kind, newValue);\n          var newInit;\n\n          if (kind === 0 /* FIELD */) {\n            newInit = newValue;\n          } else if (kind === 1 /* ACCESSOR */) {\n            newInit = newValue.init;\n            get = newValue.get || value.get;\n            set = newValue.set || value.set;\n\n            value = { get: get, set: set };\n          } else {\n            value = newValue;\n          }\n\n          if (newInit !== void 0) {\n            if (init === void 0) {\n              init = newInit;\n            } else if (typeof init === \"function\") {\n              init = [init, newInit];\n            } else {\n              init.push(newInit);\n            }\n          }\n        }\n      }\n    }\n\n    if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n      if (init === void 0) {\n        // If the initializer was void 0, sub in a dummy initializer\n        init = function (instance, init) {\n          return init;\n        };\n      } else if (typeof init !== \"function\") {\n        var ownInitializers = init;\n\n        init = function (instance, init) {\n          var value = init;\n\n          for (var i = 0; i < ownInitializers.length; i++) {\n            value = ownInitializers[i].call(instance, value);\n          }\n\n          return value;\n        };\n      } else {\n        var originalInitializer = init;\n\n        init = function (instance, init) {\n          return originalInitializer.call(instance, init);\n        };\n      }\n\n      ret.push(init);\n    }\n\n    if (kind !== 0 /* FIELD */) {\n      if (kind === 1 /* ACCESSOR */) {\n        desc.get = value.get;\n        desc.set = value.set;\n      } else if (kind === 2 /* METHOD */) {\n        desc.value = value;\n      } else if (kind === 3 /* GETTER */) {\n        desc.get = value;\n      } else if (kind === 4 /* SETTER */) {\n        desc.set = value;\n      }\n\n      if (isPrivate) {\n        if (kind === 1 /* ACCESSOR */) {\n          ret.push(function (instance, args) {\n            return value.get.call(instance, args);\n          });\n          ret.push(function (instance, args) {\n            return value.set.call(instance, args);\n          });\n        } else if (kind === 2 /* METHOD */) {\n          ret.push(value);\n        } else {\n          ret.push(function (instance, args) {\n            return value.call(instance, args);\n          });\n        }\n      } else {\n        Object.defineProperty(base, name, desc);\n      }\n    }\n  }\n\n  function applyMemberDecs(Class, decInfos, instanceBrand) {\n    var ret = [];\n    var protoInitializers;\n    var staticInitializers;\n    var staticBrand;\n\n    var existingProtoNonFields = new Map();\n    var existingStaticNonFields = new Map();\n\n    for (var i = 0; i < decInfos.length; i++) {\n      var decInfo = decInfos[i];\n\n      // skip computed property names\n      if (!Array.isArray(decInfo)) continue;\n\n      var kind = decInfo[1];\n      var name = decInfo[2];\n      var isPrivate = decInfo.length > 3;\n\n      var isStatic = kind >= 5; /* STATIC */\n      var base;\n      var initializers;\n      var hasPrivateBrand = instanceBrand;\n\n      if (isStatic) {\n        base = Class;\n        kind = kind - 5 /* STATIC */;\n        // initialize staticInitializers when we see a non-field static member\n        if (kind !== 0 /* FIELD */) {\n          staticInitializers = staticInitializers || [];\n          initializers = staticInitializers;\n        }\n        if (isPrivate && !staticBrand) {\n          staticBrand = function (_) {\n            return checkInRHS(_) === Class;\n          };\n        }\n        hasPrivateBrand = staticBrand;\n      } else {\n        base = Class.prototype;\n        // initialize protoInitializers when we see a non-field member\n        if (kind !== 0 /* FIELD */) {\n          protoInitializers = protoInitializers || [];\n          initializers = protoInitializers;\n        }\n      }\n\n      if (kind !== 0 /* FIELD */ && !isPrivate) {\n        var existingNonFields = isStatic\n          ? existingStaticNonFields\n          : existingProtoNonFields;\n\n        var existingKind = existingNonFields.get(name) || 0;\n\n        if (\n          existingKind === true ||\n          (existingKind === 3 /* GETTER */ && kind !== 4) /* SETTER */ ||\n          (existingKind === 4 /* SETTER */ && kind !== 3) /* GETTER */\n        ) {\n          throw new Error(\n            \"Attempted to decorate a public method/accessor that has the same name as a previously decorated public method/accessor. This is not currently supported by the decorators plugin. Property name was: \" +\n              name,\n          );\n        } else if (!existingKind && kind > 2 /* METHOD */) {\n          existingNonFields.set(name, kind);\n        } else {\n          existingNonFields.set(name, true);\n        }\n      }\n\n      applyMemberDec(\n        ret,\n        base,\n        decInfo,\n        name,\n        kind,\n        isStatic,\n        isPrivate,\n        initializers,\n        hasPrivateBrand,\n      );\n    }\n\n    pushInitializers(ret, protoInitializers);\n    pushInitializers(ret, staticInitializers);\n    return ret;\n  }\n\n  function pushInitializers(ret, initializers) {\n    if (initializers) {\n      ret.push(function (instance) {\n        for (var i = 0; i < initializers.length; i++) {\n          initializers[i].call(instance);\n        }\n        return instance;\n      });\n    }\n  }\n\n  function applyClassDecs(targetClass, classDecs) {\n    if (classDecs.length > 0) {\n      var initializers = [];\n      var newClass = targetClass;\n      var name = targetClass.name;\n\n      for (var i = classDecs.length - 1; i >= 0; i--) {\n        var decoratorFinishedRef = { v: false };\n\n        try {\n          var nextNewClass = classDecs[i](newClass, {\n            kind: \"class\",\n            name: name,\n            addInitializer: createAddInitializerMethod(\n              initializers,\n              decoratorFinishedRef,\n            ),\n          });\n        } finally {\n          decoratorFinishedRef.v = true;\n        }\n\n        if (nextNewClass !== undefined) {\n          assertValidReturnValue(10 /* CLASS */, nextNewClass);\n          newClass = nextNewClass;\n        }\n      }\n\n      return [\n        newClass,\n        function () {\n          for (var i = 0; i < initializers.length; i++) {\n            initializers[i].call(newClass);\n          }\n        },\n      ];\n    }\n    // The transformer will not emit assignment when there are no class decorators,\n    // so we don't have to return an empty array here.\n  }\n\n  /**\n    Basic usage:\n\n    applyDecs(\n      Class,\n      [\n        // member decorators\n        [\n          dec,                // dec or array of decs\n          0,                  // kind of value being decorated\n          'prop',             // name of public prop on class containing the value being decorated,\n          '#p',               // the name of the private property (if is private, void 0 otherwise),\n        ]\n      ],\n      [\n        // class decorators\n        dec1, dec2\n      ]\n    )\n    ```\n\n    Fully transpiled example:\n\n    ```js\n    @dec\n    class Class {\n      @dec\n      a = 123;\n\n      @dec\n      #a = 123;\n\n      @dec\n      @dec2\n      accessor b = 123;\n\n      @dec\n      accessor #b = 123;\n\n      @dec\n      c() { console.log('c'); }\n\n      @dec\n      #c() { console.log('privC'); }\n\n      @dec\n      get d() { console.log('d'); }\n\n      @dec\n      get #d() { console.log('privD'); }\n\n      @dec\n      set e(v) { console.log('e'); }\n\n      @dec\n      set #e(v) { console.log('privE'); }\n    }\n\n\n    // becomes\n    let initializeInstance;\n    let initializeClass;\n\n    let initA;\n    let initPrivA;\n\n    let initB;\n    let initPrivB, getPrivB, setPrivB;\n\n    let privC;\n    let privD;\n    let privE;\n\n    let Class;\n    class _Class {\n      static {\n        let ret = applyDecs(\n          this,\n          [\n            [dec, 0, 'a'],\n            [dec, 0, 'a', (i) => i.#a, (i, v) => i.#a = v],\n            [[dec, dec2], 1, 'b'],\n            [dec, 1, 'b', (i) => i.#privBData, (i, v) => i.#privBData = v],\n            [dec, 2, 'c'],\n            [dec, 2, 'c', () => console.log('privC')],\n            [dec, 3, 'd'],\n            [dec, 3, 'd', () => console.log('privD')],\n            [dec, 4, 'e'],\n            [dec, 4, 'e', () => console.log('privE')],\n          ],\n          [\n            dec\n          ]\n        )\n\n        initA = ret[0];\n\n        initPrivA = ret[1];\n\n        initB = ret[2];\n\n        initPrivB = ret[3];\n        getPrivB = ret[4];\n        setPrivB = ret[5];\n\n        privC = ret[6];\n\n        privD = ret[7];\n\n        privE = ret[8];\n\n        initializeInstance = ret[9];\n\n        Class = ret[10]\n\n        initializeClass = ret[11];\n      }\n\n      a = (initializeInstance(this), initA(this, 123));\n\n      #a = initPrivA(this, 123);\n\n      #bData = initB(this, 123);\n      get b() { return this.#bData }\n      set b(v) { this.#bData = v }\n\n      #privBData = initPrivB(this, 123);\n      get #b() { return getPrivB(this); }\n      set #b(v) { setPrivB(this, v); }\n\n      c() { console.log('c'); }\n\n      #c(...args) { return privC(this, ...args) }\n\n      get d() { console.log('d'); }\n\n      get #d() { return privD(this); }\n\n      set e(v) { console.log('e'); }\n\n      set #e(v) { privE(this, v); }\n    }\n\n    initializeClass(Class);\n  */\n  return function applyDecs2301(\n    targetClass,\n    memberDecs,\n    classDecs,\n    instanceBrand,\n  ) {\n    return {\n      e: applyMemberDecs(targetClass, memberDecs, instanceBrand),\n      // Lazily apply class decorations so that member init locals can be properly bound.\n      get c() {\n        return applyClassDecs(targetClass, classDecs);\n      },\n    };\n  };\n}\n\nexport default function applyDecs2301(\n  targetClass,\n  memberDecs,\n  classDecs,\n  instanceBrand,\n) {\n  return (applyDecs2301 = applyDecs2301Factory())(\n    targetClass,\n    memberDecs,\n    classDecs,\n    instanceBrand,\n  );\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AAoBA,SAASG,oBAAoBA,CAAA,EAAG;EAC9B,SAASC,0BAA0BA,CAACC,YAAY,EAAEC,oBAAoB,EAAE;IACtE,OAAO,SAASC,cAAcA,CAACC,WAAW,EAAE;MAC1CC,iBAAiB,CAACH,oBAAoB,EAAE,gBAAgB,CAAC;MACzDI,cAAc,CAACF,WAAW,EAAE,gBAAgB,CAAC;MAC7CH,YAAY,CAACM,IAAI,CAACH,WAAW,CAAC;IAChC,CAAC;EACH;EAEA,SAASI,uBAAuBA,CAACC,GAAG,EAAEC,MAAM,EAAE;IAC5C,IAAI,CAACD,GAAG,CAACC,MAAM,CAAC,EAAE;MAChB,MAAM,IAAIC,SAAS,CACjB,qDACF,CAAC;IACH;EACF;EAEA,SAASC,SAASA,CAChBC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJd,YAAY,EACZe,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,eAAe,EACf;IACA,IAAIC,OAAO;IAEX,QAAQL,IAAI;MACV,KAAK,CAAC;QACJK,OAAO,GAAG,UAAU;QACpB;MACF,KAAK,CAAC;QACJA,OAAO,GAAG,QAAQ;QAClB;MACF,KAAK,CAAC;QACJA,OAAO,GAAG,QAAQ;QAClB;MACF,KAAK,CAAC;QACJA,OAAO,GAAG,QAAQ;QAClB;MACF;QACEA,OAAO,GAAG,OAAO;IACrB;IAEA,IAAIC,GAAG,GAAG;MACRN,IAAI,EAAEK,OAAO;MACbP,IAAI,EAAEI,SAAS,GAAG,GAAG,GAAGJ,IAAI,GAAGS,cAAa,CAACT,IAAI,CAAC;MAClDU,MAAM,EAAEP,QAAQ;MAChBQ,OAAO,EAAEP;IACX,CAAC;IAED,IAAIhB,oBAAoB,GAAG;MAAEwB,CAAC,EAAE;IAAM,CAAC;IAEvC,IAAIV,IAAI,KAAK,CAAC,EAAc;MAC1BM,GAAG,CAACnB,cAAc,GAAGH,0BAA0B,CAC7CC,YAAY,EACZC,oBACF,CAAC;IACH;IAEA,IAAIyB,GAAG,EAAEC,GAAG;IACZ,IAAI,CAACV,SAAS,KAAKF,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,CAAC,EAAe;MACrEW,GAAG,GAAG,SAAAA,CAAUjB,MAAM,EAAE;QACtB,OAAOA,MAAM,CAACI,IAAI,CAAC;MACrB,CAAC;MACD,IAAIE,IAAI,KAAK,CAAC,EAAc;QAC1BY,GAAG,GAAG,SAAAA,CAAUlB,MAAM,EAAEgB,CAAC,EAAE;UACzBhB,MAAM,CAACI,IAAI,CAAC,GAAGY,CAAC;QAClB,CAAC;MACH;IACF,CAAC,MAAM,IAAIV,IAAI,KAAK,CAAC,EAAe;MAElCW,GAAG,GAAG,SAAAA,CAAUjB,MAAM,EAAE;QACtBF,uBAAuB,CAACY,eAAe,EAAEV,MAAM,CAAC;QAChD,OAAOK,IAAI,CAACI,KAAK;MACnB,CAAC;IACH,CAAC,MAAM;MAEL,IAAIU,CAAC,GAAGb,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC;MAC5C,IAAIa,CAAC,IAAIb,IAAI,KAAK,CAAC,EAAe;QAChC,IAAIE,SAAS,EAAE;UACbS,GAAG,GAAG,SAAAA,CAAUjB,MAAM,EAAE;YACtBF,uBAAuB,CAACY,eAAe,EAAEV,MAAM,CAAC;YAChD,OAAOK,IAAI,CAACY,GAAG,CAACG,IAAI,CAACpB,MAAM,CAAC;UAC9B,CAAC;QACH,CAAC,MAAM;UACLiB,GAAG,GAAG,SAAAA,CAAUjB,MAAM,EAAE;YACtB,OAAOK,IAAI,CAACY,GAAG,CAACG,IAAI,CAACpB,MAAM,CAAC;UAC9B,CAAC;QACH;MACF;MACA,IAAImB,CAAC,IAAIb,IAAI,KAAK,CAAC,EAAe;QAChC,IAAIE,SAAS,EAAE;UACbU,GAAG,GAAG,SAAAA,CAAUlB,MAAM,EAAES,KAAK,EAAE;YAC7BX,uBAAuB,CAACY,eAAe,EAAEV,MAAM,CAAC;YAChDK,IAAI,CAACa,GAAG,CAACE,IAAI,CAACpB,MAAM,EAAES,KAAK,CAAC;UAC9B,CAAC;QACH,CAAC,MAAM;UACLS,GAAG,GAAG,SAAAA,CAAUlB,MAAM,EAAES,KAAK,EAAE;YAC7BJ,IAAI,CAACa,GAAG,CAACE,IAAI,CAACpB,MAAM,EAAES,KAAK,CAAC;UAC9B,CAAC;QACH;MACF;IACF;IACA,IAAIV,GAAG,GAAGS,SAAS,GACfE,eAAe,CAACW,IAAI,CAAC,CAAC,GACtB,UAAUrB,MAAM,EAAE;MAChB,OAAOI,IAAI,IAAIJ,MAAM;IACvB,CAAC;IACLY,GAAG,CAACU,MAAM,GACRL,GAAG,IAAIC,GAAG,GACN;MAAED,GAAG,EAAEA,GAAG;MAAEC,GAAG,EAAEA,GAAG;MAAEnB,GAAG,EAAEA;IAAI,CAAC,GAChCkB,GAAG,GACD;MAAEA,GAAG,EAAEA,GAAG;MAAElB,GAAG,EAAEA;IAAI,CAAC,GACtB;MAAEmB,GAAG,EAAEA,GAAG;MAAEnB,GAAG,EAAEA;IAAI,CAAC;IAE9B,IAAI;MACF,OAAOI,GAAG,CAACM,KAAK,EAAEG,GAAG,CAAC;IACxB,CAAC,SAAS;MACRpB,oBAAoB,CAACwB,CAAC,GAAG,IAAI;IAC/B;EACF;EAEA,SAASrB,iBAAiBA,CAACH,oBAAoB,EAAE+B,MAAM,EAAE;IACvD,IAAI/B,oBAAoB,CAACwB,CAAC,EAAE;MAC1B,MAAM,IAAIQ,KAAK,CACb,oBAAoB,GAAGD,MAAM,GAAG,gCAClC,CAAC;IACH;EACF;EAEA,SAAS3B,cAAcA,CAAC6B,EAAE,EAAEC,IAAI,EAAE;IAChC,IAAI,OAAOD,EAAE,KAAK,UAAU,EAAE;MAC5B,MAAM,IAAIxB,SAAS,CAACyB,IAAI,GAAG,qBAAqB,CAAC;IACnD;EACF;EAEA,SAASC,sBAAsBA,CAACrB,IAAI,EAAEG,KAAK,EAAE;IAC3C,IAAImB,IAAI,GAAG,OAAOnB,KAAK;IAEvB,IAAIH,IAAI,KAAK,CAAC,EAAiB;MAC7B,IAAIsB,IAAI,KAAK,QAAQ,IAAInB,KAAK,KAAK,IAAI,EAAE;QACvC,MAAM,IAAIR,SAAS,CACjB,uFACF,CAAC;MACH;MACA,IAAIQ,KAAK,CAACQ,GAAG,KAAKY,SAAS,EAAE;QAC3BjC,cAAc,CAACa,KAAK,CAACQ,GAAG,EAAE,cAAc,CAAC;MAC3C;MACA,IAAIR,KAAK,CAACS,GAAG,KAAKW,SAAS,EAAE;QAC3BjC,cAAc,CAACa,KAAK,CAACS,GAAG,EAAE,cAAc,CAAC;MAC3C;MACA,IAAIT,KAAK,CAACqB,IAAI,KAAKD,SAAS,EAAE;QAC5BjC,cAAc,CAACa,KAAK,CAACqB,IAAI,EAAE,eAAe,CAAC;MAC7C;IACF,CAAC,MAAM,IAAIF,IAAI,KAAK,UAAU,EAAE;MAC9B,IAAIF,IAAI;MACR,IAAIpB,IAAI,KAAK,CAAC,EAAc;QAC1BoB,IAAI,GAAG,OAAO;MAChB,CAAC,MAAM,IAAIpB,IAAI,KAAK,EAAE,EAAc;QAClCoB,IAAI,GAAG,OAAO;MAChB,CAAC,MAAM;QACLA,IAAI,GAAG,QAAQ;MACjB;MACA,MAAM,IAAIzB,SAAS,CACjByB,IAAI,GAAG,8CACT,CAAC;IACH;EACF;EAEA,SAASK,UAAUA,CAACN,EAAE,EAAE;IACtB,OAAO,YAAY;MACjB,OAAOA,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC;EACH;EACA,SAASO,UAAUA,CAACP,EAAE,EAAE;IACtB,OAAO,UAAUhB,KAAK,EAAE;MACtBgB,EAAE,CAAC,IAAI,EAAEhB,KAAK,CAAC;IACjB,CAAC;EACH;EAEA,SAASwB,cAAcA,CACrBC,GAAG,EACHC,IAAI,EACJC,OAAO,EACPhC,IAAI,EACJE,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTjB,YAAY,EACZmB,eAAe,EACf;IACA,IAAI2B,IAAI,GAAGD,OAAO,CAAC,CAAC,CAAC;IAErB,IAAI/B,IAAI,EAAEyB,IAAI,EAAEQ,MAAM,EAAE7B,KAAK;IAE7B,IAAID,SAAS,EAAE;MACb,IAAIF,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,EAAiB;QACvDD,IAAI,GAAG;UACLY,GAAG,EAAEc,UAAU,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC;UAC3BlB,GAAG,EAAEc,UAAU,CAACI,OAAO,CAAC,CAAC,CAAC;QAC5B,CAAC;QACDE,MAAM,GAAG,KAAK;MAChB,CAAC,MAAM;QACL,IAAIhC,IAAI,KAAK,CAAC,EAAe;UAC3BD,IAAI,GAAG;YACLY,GAAG,EAAEmB,OAAO,CAAC,CAAC;UAChB,CAAC;UACDE,MAAM,GAAG,KAAK;QAChB,CAAC,MAAM,IAAIhC,IAAI,KAAK,CAAC,EAAe;UAClCD,IAAI,GAAG;YACLa,GAAG,EAAEkB,OAAO,CAAC,CAAC;UAChB,CAAC;UACDE,MAAM,GAAG,KAAK;QAChB,CAAC,MAAM;UACLjC,IAAI,GAAG;YACLI,KAAK,EAAE2B,OAAO,CAAC,CAAC;UAClB,CAAC;QACH;MACF;MACA,IAAI9B,IAAI,KAAK,CAAC,EAAc;QAC1B,IAAIA,IAAI,KAAK,CAAC,EAAiB;UAC7BiC,gBAAe,CAAClC,IAAI,CAACa,GAAG,EAAE,GAAG,GAAGd,IAAI,EAAE,KAAK,CAAC;QAC9C;QACAmC,gBAAe,CAAClC,IAAI,CAACiC,MAAM,IAAI,OAAO,CAAC,EAAE,GAAG,GAAGlC,IAAI,EAAEkC,MAAM,CAAC;MAC9D;IACF,CAAC,MAAM,IAAIhC,IAAI,KAAK,CAAC,EAAc;MACjCD,IAAI,GAAGmC,MAAM,CAACC,wBAAwB,CAACN,IAAI,EAAE/B,IAAI,CAAC;IACpD;IAEA,IAAIE,IAAI,KAAK,CAAC,EAAiB;MAC7BG,KAAK,GAAG;QACNQ,GAAG,EAAEZ,IAAI,CAACY,GAAG;QACbC,GAAG,EAAEb,IAAI,CAACa;MACZ,CAAC;IACH,CAAC,MAAM,IAAIZ,IAAI,KAAK,CAAC,EAAe;MAClCG,KAAK,GAAGJ,IAAI,CAACI,KAAK;IACpB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;MAClCG,KAAK,GAAGJ,IAAI,CAACY,GAAG;IAClB,CAAC,MAAM,IAAIX,IAAI,KAAK,CAAC,EAAe;MAClCG,KAAK,GAAGJ,IAAI,CAACa,GAAG;IAClB;IAEA,IAAIwB,QAAQ,EAAEzB,GAAG,EAAEC,GAAG;IAEtB,IAAI,OAAOmB,IAAI,KAAK,UAAU,EAAE;MAC9BK,QAAQ,GAAGxC,SAAS,CAClBmC,IAAI,EACJjC,IAAI,EACJC,IAAI,EACJd,YAAY,EACZe,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,eACF,CAAC;MAED,IAAIgC,QAAQ,KAAK,KAAK,CAAC,EAAE;QACvBf,sBAAsB,CAACrB,IAAI,EAAEoC,QAAQ,CAAC;QAEtC,IAAIpC,IAAI,KAAK,CAAC,EAAc;UAC1BwB,IAAI,GAAGY,QAAQ;QACjB,CAAC,MAAM,IAAIpC,IAAI,KAAK,CAAC,EAAiB;UACpCwB,IAAI,GAAGY,QAAQ,CAACZ,IAAI;UACpBb,GAAG,GAAGyB,QAAQ,CAACzB,GAAG,IAAIR,KAAK,CAACQ,GAAG;UAC/BC,GAAG,GAAGwB,QAAQ,CAACxB,GAAG,IAAIT,KAAK,CAACS,GAAG;UAE/BT,KAAK,GAAG;YAAEQ,GAAG,EAAEA,GAAG;YAAEC,GAAG,EAAEA;UAAI,CAAC;QAChC,CAAC,MAAM;UACLT,KAAK,GAAGiC,QAAQ;QAClB;MACF;IACF,CAAC,MAAM;MACL,KAAK,IAAIC,CAAC,GAAGN,IAAI,CAACO,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACzC,IAAIxC,GAAG,GAAGkC,IAAI,CAACM,CAAC,CAAC;QAEjBD,QAAQ,GAAGxC,SAAS,CAClBC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJd,YAAY,EACZe,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,eACF,CAAC;QAED,IAAIgC,QAAQ,KAAK,KAAK,CAAC,EAAE;UACvBf,sBAAsB,CAACrB,IAAI,EAAEoC,QAAQ,CAAC;UACtC,IAAIG,OAAO;UAEX,IAAIvC,IAAI,KAAK,CAAC,EAAc;YAC1BuC,OAAO,GAAGH,QAAQ;UACpB,CAAC,MAAM,IAAIpC,IAAI,KAAK,CAAC,EAAiB;YACpCuC,OAAO,GAAGH,QAAQ,CAACZ,IAAI;YACvBb,GAAG,GAAGyB,QAAQ,CAACzB,GAAG,IAAIR,KAAK,CAACQ,GAAG;YAC/BC,GAAG,GAAGwB,QAAQ,CAACxB,GAAG,IAAIT,KAAK,CAACS,GAAG;YAE/BT,KAAK,GAAG;cAAEQ,GAAG,EAAEA,GAAG;cAAEC,GAAG,EAAEA;YAAI,CAAC;UAChC,CAAC,MAAM;YACLT,KAAK,GAAGiC,QAAQ;UAClB;UAEA,IAAIG,OAAO,KAAK,KAAK,CAAC,EAAE;YACtB,IAAIf,IAAI,KAAK,KAAK,CAAC,EAAE;cACnBA,IAAI,GAAGe,OAAO;YAChB,CAAC,MAAM,IAAI,OAAOf,IAAI,KAAK,UAAU,EAAE;cACrCA,IAAI,GAAG,CAACA,IAAI,EAAEe,OAAO,CAAC;YACxB,CAAC,MAAM;cACLf,IAAI,CAACjC,IAAI,CAACgD,OAAO,CAAC;YACpB;UACF;QACF;MACF;IACF;IAEA,IAAIvC,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,EAAiB;MACvD,IAAIwB,IAAI,KAAK,KAAK,CAAC,EAAE;QAEnBA,IAAI,GAAG,SAAAA,CAAUgB,QAAQ,EAAEhB,IAAI,EAAE;UAC/B,OAAOA,IAAI;QACb,CAAC;MACH,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;QACrC,IAAIiB,eAAe,GAAGjB,IAAI;QAE1BA,IAAI,GAAG,SAAAA,CAAUgB,QAAQ,EAAEhB,IAAI,EAAE;UAC/B,IAAIrB,KAAK,GAAGqB,IAAI;UAEhB,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,eAAe,CAACH,MAAM,EAAED,CAAC,EAAE,EAAE;YAC/ClC,KAAK,GAAGsC,eAAe,CAACJ,CAAC,CAAC,CAACvB,IAAI,CAAC0B,QAAQ,EAAErC,KAAK,CAAC;UAClD;UAEA,OAAOA,KAAK;QACd,CAAC;MACH,CAAC,MAAM;QACL,IAAIuC,mBAAmB,GAAGlB,IAAI;QAE9BA,IAAI,GAAG,SAAAA,CAAUgB,QAAQ,EAAEhB,IAAI,EAAE;UAC/B,OAAOkB,mBAAmB,CAAC5B,IAAI,CAAC0B,QAAQ,EAAEhB,IAAI,CAAC;QACjD,CAAC;MACH;MAEAI,GAAG,CAACrC,IAAI,CAACiC,IAAI,CAAC;IAChB;IAEA,IAAIxB,IAAI,KAAK,CAAC,EAAc;MAC1B,IAAIA,IAAI,KAAK,CAAC,EAAiB;QAC7BD,IAAI,CAACY,GAAG,GAAGR,KAAK,CAACQ,GAAG;QACpBZ,IAAI,CAACa,GAAG,GAAGT,KAAK,CAACS,GAAG;MACtB,CAAC,MAAM,IAAIZ,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,CAACI,KAAK,GAAGA,KAAK;MACpB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,CAACY,GAAG,GAAGR,KAAK;MAClB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,CAACa,GAAG,GAAGT,KAAK;MAClB;MAEA,IAAID,SAAS,EAAE;QACb,IAAIF,IAAI,KAAK,CAAC,EAAiB;UAC7B4B,GAAG,CAACrC,IAAI,CAAC,UAAUiD,QAAQ,EAAEG,IAAI,EAAE;YACjC,OAAOxC,KAAK,CAACQ,GAAG,CAACG,IAAI,CAAC0B,QAAQ,EAAEG,IAAI,CAAC;UACvC,CAAC,CAAC;UACFf,GAAG,CAACrC,IAAI,CAAC,UAAUiD,QAAQ,EAAEG,IAAI,EAAE;YACjC,OAAOxC,KAAK,CAACS,GAAG,CAACE,IAAI,CAAC0B,QAAQ,EAAEG,IAAI,CAAC;UACvC,CAAC,CAAC;QACJ,CAAC,MAAM,IAAI3C,IAAI,KAAK,CAAC,EAAe;UAClC4B,GAAG,CAACrC,IAAI,CAACY,KAAK,CAAC;QACjB,CAAC,MAAM;UACLyB,GAAG,CAACrC,IAAI,CAAC,UAAUiD,QAAQ,EAAEG,IAAI,EAAE;YACjC,OAAOxC,KAAK,CAACW,IAAI,CAAC0B,QAAQ,EAAEG,IAAI,CAAC;UACnC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACLT,MAAM,CAACU,cAAc,CAACf,IAAI,EAAE/B,IAAI,EAAEC,IAAI,CAAC;MACzC;IACF;EACF;EAEA,SAAS8C,eAAeA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IACvD,IAAIpB,GAAG,GAAG,EAAE;IACZ,IAAIqB,iBAAiB;IACrB,IAAIC,kBAAkB;IACtB,IAAIC,WAAW;IAEf,IAAIC,sBAAsB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtC,IAAIC,uBAAuB,GAAG,IAAID,GAAG,CAAC,CAAC;IAEvC,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,QAAQ,CAACT,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,IAAIP,OAAO,GAAGiB,QAAQ,CAACV,CAAC,CAAC;MAGzB,IAAI,CAACkB,KAAK,CAACC,OAAO,CAAC1B,OAAO,CAAC,EAAE;MAE7B,IAAI9B,IAAI,GAAG8B,OAAO,CAAC,CAAC,CAAC;MACrB,IAAIhC,IAAI,GAAGgC,OAAO,CAAC,CAAC,CAAC;MACrB,IAAI5B,SAAS,GAAG4B,OAAO,CAACQ,MAAM,GAAG,CAAC;MAElC,IAAIrC,QAAQ,GAAGD,IAAI,IAAI,CAAC;MACxB,IAAI6B,IAAI;MACR,IAAI5C,YAAY;MAChB,IAAImB,eAAe,GAAG4C,aAAa;MAEnC,IAAI/C,QAAQ,EAAE;QACZ4B,IAAI,GAAGiB,KAAK;QACZ9C,IAAI,GAAGA,IAAI,GAAG,CAAC;QAEf,IAAIA,IAAI,KAAK,CAAC,EAAc;UAC1BkD,kBAAkB,GAAGA,kBAAkB,IAAI,EAAE;UAC7CjE,YAAY,GAAGiE,kBAAkB;QACnC;QACA,IAAIhD,SAAS,IAAI,CAACiD,WAAW,EAAE;UAC7BA,WAAW,GAAG,SAAAA,CAAUM,CAAC,EAAE;YACzB,OAAOC,WAAU,CAACD,CAAC,CAAC,KAAKX,KAAK;UAChC,CAAC;QACH;QACA1C,eAAe,GAAG+C,WAAW;MAC/B,CAAC,MAAM;QACLtB,IAAI,GAAGiB,KAAK,CAACa,SAAS;QAEtB,IAAI3D,IAAI,KAAK,CAAC,EAAc;UAC1BiD,iBAAiB,GAAGA,iBAAiB,IAAI,EAAE;UAC3ChE,YAAY,GAAGgE,iBAAiB;QAClC;MACF;MAEA,IAAIjD,IAAI,KAAK,CAAC,IAAgB,CAACE,SAAS,EAAE;QACxC,IAAI0D,iBAAiB,GAAG3D,QAAQ,GAC5BqD,uBAAuB,GACvBF,sBAAsB;QAE1B,IAAIS,YAAY,GAAGD,iBAAiB,CAACjD,GAAG,CAACb,IAAI,CAAC,IAAI,CAAC;QAEnD,IACE+D,YAAY,KAAK,IAAI,IACpBA,YAAY,KAAK,CAAC,IAAiB7D,IAAI,KAAK,CAAE,IAC9C6D,YAAY,KAAK,CAAC,IAAiB7D,IAAI,KAAK,CAAE,EAC/C;UACA,MAAM,IAAIkB,KAAK,CACb,uMAAuM,GACrMpB,IACJ,CAAC;QACH,CAAC,MAAM,IAAI,CAAC+D,YAAY,IAAI7D,IAAI,GAAG,CAAC,EAAe;UACjD4D,iBAAiB,CAAChD,GAAG,CAACd,IAAI,EAAEE,IAAI,CAAC;QACnC,CAAC,MAAM;UACL4D,iBAAiB,CAAChD,GAAG,CAACd,IAAI,EAAE,IAAI,CAAC;QACnC;MACF;MAEA6B,cAAc,CACZC,GAAG,EACHC,IAAI,EACJC,OAAO,EACPhC,IAAI,EACJE,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTjB,YAAY,EACZmB,eACF,CAAC;IACH;IAEA0D,gBAAgB,CAAClC,GAAG,EAAEqB,iBAAiB,CAAC;IACxCa,gBAAgB,CAAClC,GAAG,EAAEsB,kBAAkB,CAAC;IACzC,OAAOtB,GAAG;EACZ;EAEA,SAASkC,gBAAgBA,CAAClC,GAAG,EAAE3C,YAAY,EAAE;IAC3C,IAAIA,YAAY,EAAE;MAChB2C,GAAG,CAACrC,IAAI,CAAC,UAAUiD,QAAQ,EAAE;QAC3B,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpD,YAAY,CAACqD,MAAM,EAAED,CAAC,EAAE,EAAE;UAC5CpD,YAAY,CAACoD,CAAC,CAAC,CAACvB,IAAI,CAAC0B,QAAQ,CAAC;QAChC;QACA,OAAOA,QAAQ;MACjB,CAAC,CAAC;IACJ;EACF;EAEA,SAASuB,cAAcA,CAACC,WAAW,EAAEC,SAAS,EAAE;IAC9C,IAAIA,SAAS,CAAC3B,MAAM,GAAG,CAAC,EAAE;MACxB,IAAIrD,YAAY,GAAG,EAAE;MACrB,IAAIiF,QAAQ,GAAGF,WAAW;MAC1B,IAAIlE,IAAI,GAAGkE,WAAW,CAAClE,IAAI;MAE3B,KAAK,IAAIuC,CAAC,GAAG4B,SAAS,CAAC3B,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC9C,IAAInD,oBAAoB,GAAG;UAAEwB,CAAC,EAAE;QAAM,CAAC;QAEvC,IAAI;UACF,IAAIyD,YAAY,GAAGF,SAAS,CAAC5B,CAAC,CAAC,CAAC6B,QAAQ,EAAE;YACxClE,IAAI,EAAE,OAAO;YACbF,IAAI,EAAEA,IAAI;YACVX,cAAc,EAAEH,0BAA0B,CACxCC,YAAY,EACZC,oBACF;UACF,CAAC,CAAC;QACJ,CAAC,SAAS;UACRA,oBAAoB,CAACwB,CAAC,GAAG,IAAI;QAC/B;QAEA,IAAIyD,YAAY,KAAK5C,SAAS,EAAE;UAC9BF,sBAAsB,CAAC,EAAE,EAAc8C,YAAY,CAAC;UACpDD,QAAQ,GAAGC,YAAY;QACzB;MACF;MAEA,OAAO,CACLD,QAAQ,EACR,YAAY;QACV,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpD,YAAY,CAACqD,MAAM,EAAED,CAAC,EAAE,EAAE;UAC5CpD,YAAY,CAACoD,CAAC,CAAC,CAACvB,IAAI,CAACoD,QAAQ,CAAC;QAChC;MACF,CAAC,CACF;IACH;EAGF;EAmJA,OAAO,SAASE,aAAaA,CAC3BJ,WAAW,EACXK,UAAU,EACVJ,SAAS,EACTjB,aAAa,EACb;IACA,OAAO;MACLsB,CAAC,EAAEzB,eAAe,CAACmB,WAAW,EAAEK,UAAU,EAAErB,aAAa,CAAC;MAE1D,IAAIuB,CAACA,CAAA,EAAG;QACN,OAAOR,cAAc,CAACC,WAAW,EAAEC,SAAS,CAAC;MAC/C;IACF,CAAC;EACH,CAAC;AACH;AAEe,SAASG,aAAaA,CACnCJ,WAAW,EACXK,UAAU,EACVJ,SAAS,EACTjB,aAAa,EACb;EACA,OAAO,CAAAwB,OAAA,CAAAC,OAAA,GAACL,aAAa,GAAGrF,oBAAoB,CAAC,CAAC,EAC5CiF,WAAW,EACXK,UAAU,EACVJ,SAAS,EACTjB,aACF,CAAC;AACH", "ignoreList": []}