import React from 'react'
import { Camera, TrendingUp, DollarSign, Users, Play, Award } from 'lucide-react'
import { Link } from 'react-router-dom'
import './VloggersSection.css'

const VloggersSection = () => {
  const vloggerBenefits = [
    {
      icon: <DollarSign size={40} />,
      title: "Steady Income",
      description: "Monthly guaranteed earnings through vendor collaborations",
      highlight: "₹15-50K per month"
    },
    {
      icon: <Camera size={40} />,
      title: "Quality Content",
      description: "AI suggests trending food spots for better content",
      highlight: "Higher engagement"
    },
    {
      icon: <Users size={40} />,
      title: "Audience Growth",
      description: "Platform exposure helps grow your follower base",
      highlight: "Organic reach"
    },
    {
      icon: <Award size={40} />,
      title: "Brand Partnerships",
      description: "Connect with premium vendors and food brands",
      highlight: "Professional growth"
    }
  ]

  const topVloggers = [
    {
      name: "Manu Food Explorer",
      followers: "85K",
      platform: "YouTube + Instagram",
      earnings: "₹45K/month",
      specialty: "Street Food Reviews",
      rating: 4.9,
      image: "/api/placeholder/80/80",
      verified: true
    },
    {
      name: "Priya Foodie",
      followers: "62K",
      platform: "Instagram",
      earnings: "₹32K/month",
      specialty: "Local Delicacies",
      rating: 4.8,
      image: "/api/placeholder/80/80",
      verified: true
    },
    {
      name: "Rohit Khana Khazana",
      followers: "120K",
      platform: "YouTube",
      earnings: "₹65K/month",
      specialty: "Food Challenges",
      rating: 4.9,
      image: "/api/placeholder/80/80",
      verified: true
    }
  ]

  const howItWorks = [
    {
      step: "1",
      title: "Sign Up",
      description: "Register as a vlogger and complete your profile"
    },
    {
      step: "2",
      title: "Get Matched",
      description: "AI suggests best vendor collaborations for you"
    },
    {
      step: "3",
      title: "Create Content",
      description: "Visit vendors, create engaging food content"
    },
    {
      step: "4",
      title: "Earn Money",
      description: "Get paid automatically after content verification"
    }
  ]

  const features = [
    "AI-powered vendor matching",
    "Content performance analytics",
    "Automated payment system",
    "Collaboration management tools",
    "Audience insights dashboard",
    "Brand partnership opportunities"
  ]

  return (
    <section className="vloggers-section section" id="vloggers">
      <div className="container">
        <div className="section-header">
          <h2 className="section-title">Vloggers ke liye</h2>
          <p className="section-subtitle">
            Food content creation se consistent income kamaye aur apna audience badhaye
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="benefits-grid">
          {vloggerBenefits.map((benefit, index) => (
            <div key={index} className="benefit-card">
              <div className="benefit-icon">{benefit.icon}</div>
              <h3>{benefit.title}</h3>
              <p>{benefit.description}</p>
              <span className="highlight">{benefit.highlight}</span>
            </div>
          ))}
        </div>

        {/* Top Vloggers */}
        <div className="top-vloggers">
          <h3 className="subsection-title">Top Earning Vloggers</h3>
          <div className="vloggers-grid">
            {topVloggers.map((vlogger, index) => (
              <div key={index} className="vlogger-card">
                <div className="vlogger-header">
                  <img src={vlogger.image} alt={vlogger.name} className="vlogger-avatar" />
                  <div className="vlogger-info">
                    <div className="vlogger-name">
                      <h4>{vlogger.name}</h4>
                      {vlogger.verified && <div className="verified-badge">✓</div>}
                    </div>
                    <p className="vlogger-specialty">{vlogger.specialty}</p>
                    <p className="vlogger-platform">{vlogger.platform}</p>
                  </div>
                  <div className="earnings-badge">
                    {vlogger.earnings}
                  </div>
                </div>
                <div className="vlogger-stats">
                  <div className="stat">
                    <Users size={16} />
                    <span>{vlogger.followers} followers</span>
                  </div>
                  <div className="stat">
                    <Award size={16} />
                    <span>{vlogger.rating} rating</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* How It Works */}
        <div className="how-it-works">
          <h3 className="subsection-title">How It Works</h3>
          <div className="steps-grid">
            {howItWorks.map((step, index) => (
              <div key={index} className="step-card">
                <div className="step-number">{step.step}</div>
                <h4>{step.title}</h4>
                <p>{step.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Features List */}
        <div className="features-section">
          <h3 className="subsection-title">Platform Features</h3>
          <div className="features-grid">
            {features.map((feature, index) => (
              <div key={index} className="feature-item">
                <div className="feature-check">✓</div>
                <span>{feature}</span>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="vlogger-cta">
          <div className="cta-content">
            <h3>Start earning from your passion!</h3>
            <p>Join successful vloggers making consistent income through food content</p>
            <div className="cta-buttons">
              <Link to="/register" className="btn-primary">
                <Camera size={20} />
                Join as Vlogger
              </Link>
              <Link to="/login" className="btn-secondary">
                Already registered?
              </Link>
            </div>
          </div>
          <div className="cta-stats">
            <div className="stat">
              <span className="stat-number">2000+</span>
              <span className="stat-label">Active Vloggers</span>
            </div>
            <div className="stat">
              <span className="stat-number">₹35K</span>
              <span className="stat-label">Avg Monthly Earnings</span>
            </div>
            <div className="stat">
              <span className="stat-number">10M+</span>
              <span className="stat-label">Monthly Views</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default VloggersSection
