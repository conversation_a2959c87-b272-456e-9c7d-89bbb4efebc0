{"name": "puppeteer-core", "version": "24.11.2", "description": "A high-level API to control headless Chrome over the DevTools Protocol", "keywords": ["puppeteer", "chrome", "headless", "automation"], "type": "commonjs", "main": "./lib/cjs/puppeteer/puppeteer-core.js", "types": "./lib/types.d.ts", "browser": "./lib/esm/puppeteer/puppeteer-core-browser.js", "exports": {".": {"types": "./lib/types.d.ts", "import": "./lib/esm/puppeteer/puppeteer-core.js", "require": "./lib/cjs/puppeteer/puppeteer-core.js"}, "./internal/*": {"import": "./lib/esm/puppeteer/*", "require": "./lib/cjs/puppeteer/*"}, "./*": {"import": "./*", "require": "./*"}}, "repository": {"type": "git", "url": "https://github.com/puppeteer/puppeteer/tree/main/packages/puppeteer-core"}, "engines": {"node": ">=18"}, "scripts": {"build:docs": "wireit", "build": "wireit", "check": "wireit", "clean": "../../tools/clean.mjs", "prepack": "wireit", "unit": "wireit"}, "wireit": {"check": {"command": "node --experimental-strip-types tools/ensure-correct-devtools-protocol-package.mts", "dependencies": ["build"]}, "prepack": {"command": "node --experimental-strip-types ../../tools/cp.ts ../../README.md README.md", "files": ["../../README.md"], "output": ["README.md"]}, "build": {"dependencies": ["build:tsc", "build:types", "build:es5"]}, "build:docs": {"command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "files": ["api-extractor.docs.json", "lib/esm/puppeteer/puppeteer-core.d.ts", "tsconfig.json"], "dependencies": ["build:tsc"]}, "build:tsc": {"command": "hereby build", "clean": "if-file-deleted", "dependencies": ["../browsers:build"], "files": ["{src,third_party}/**", "../../versions.js", "!src/generated", "Herebyfile.mjs"], "output": ["lib/{cjs,esm}/**"]}, "build:es5": {"command": "rollup -c rollup.config.mjs && node ../../tools/patch.mjs lib/es5-iife/puppeteer-core-browser.js lib/es5-iife/puppeteer-core-browser.d.ts lib/types.d.ts", "files": ["rollup.config.mjs"], "output": ["lib/es5-iife/puppeteer-core-browser.js"], "dependencies": ["build:tsc", "build:types"]}, "build:types": {"command": "api-extractor run --local && eslint --cache-location .eslintcache --cache --no-ignore --no-config-lookup -c=../../eslint.types.config.mjs --fix lib/types.d.ts", "files": ["../../eslint.types.config.mjs", "api-extractor.json", "lib/esm/puppeteer/types.d.ts", "tsconfig.json"], "output": ["lib/types.d.ts"], "dependencies": ["build:tsc"]}, "unit": {"command": "node --test --test-reporter=spec \"lib/cjs/**/*.test.js\"", "dependencies": ["build"]}}, "files": ["lib", "src", "!*.test.ts", "!*.test.js", "!*.test.d.ts", "!*.test.js.map", "!*.test.d.ts.map", "!*.tsbuil<PERSON><PERSON>"], "author": "The Chromium Authors", "license": "Apache-2.0", "dependencies": {"@puppeteer/browsers": "2.10.5", "debug": "^4.4.1", "chromium-bidi": "5.1.0", "devtools-protocol": "0.0.1464554", "typed-query-selector": "^2.12.0", "ws": "^8.18.3"}, "devDependencies": {"@types/chrome": "0.0.326", "@types/debug": "4.1.12", "@types/node": "^18.17.15", "@types/ws": "8.18.1", "mitt": "3.0.1", "parsel-js": "1.2.1", "rxjs": "7.8.2"}}