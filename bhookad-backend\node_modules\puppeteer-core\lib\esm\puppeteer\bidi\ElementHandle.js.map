{"version": 3, "file": "ElementHandle.js", "sourceRoot": "", "sources": ["../../../../src/bidi/ElementHandle.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIH,OAAO,EACL,kBAAkB,EAClB,aAAa,GAEd,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAC,oBAAoB,EAAC,MAAM,qBAAqB,CAAC;AAEzD,OAAO,EAAC,WAAW,EAAC,MAAM,mBAAmB,CAAC;AAC9C,OAAO,EAAC,iBAAiB,EAAC,MAAM,8BAA8B,CAAC;AAC/D,OAAO,EAAC,eAAe,EAAC,MAAM,uBAAuB,CAAC;AAGtD,OAAO,EAAC,YAAY,EAAC,MAAM,eAAe,CAAC;AAG3C;;GAEG;IACU,iBAAiB;sBAEpB,aAAa;;;;iBAFV,iBAEX,SAAQ,WAA0B;;;oCA6BjC,eAAe,EAAE;wCAkBjB,eAAe,EAAE,EACjB,kBAAkB;YAlBnB,2KAAe,QAAQ,6DAYtB;YAOD,uLAAe,YAAY,6DAsB1B;;;QAtED,cAAc,GAHH,mDAAiB,CAGJ;QAExB,MAAM,CAAC,IAAI,CACT,KAA8B,EAC9B,KAAqB;YAErB,OAAO,IAAI,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC7C,CAAC;QAID,YAAY,KAA8B,EAAE,KAAqB;YAC/D,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;QACzC,CAAC;QAED,IAAa,KAAK;YAChB,iDAAiD;YACjD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAuB,CAAC;QAC7C,CAAC;QAED,IAAa,KAAK;YAChB,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;QAChC,CAAC;QAED,WAAW;YACT,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QACnC,CAAC;QAGQ,KAAK,CAAC,QAAQ,CAAC,IAAkB;YACxC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACjC,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBACrD,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;aACzB,CAAC,CAAC;YACH,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC;YAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YAC/B,MAAM,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBACpC,OAAO;gBACP,OAAO;gBACP,IAAI,EAAE,IAAI,CAAC,UAAU;aACtB,CAAC,CAAC;QACL,CAAC;QAOQ,KAAK,CAAC,YAAY;;;gBACzB,MAAM,MAAM,kCAAG,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;oBAClD,IACE,OAAO,YAAY,iBAAiB;wBACpC,OAAO,YAAY,gBAAgB,EACnC,CAAC;wBACD,OAAO,OAAO,CAAC,aAAa,CAAC;oBAC/B,CAAC;oBACD,OAAO;gBACT,CAAC,CAAC,CAAiB,QAAA,CAAC;gBACpB,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;gBACnC,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC5B,OAAO,CACL,IAAI,CAAC,KAAK;yBACP,IAAI,EAAE;yBACN,MAAM,EAAE;yBACR,IAAI,CAAC,KAAK,CAAC,EAAE;wBACZ,OAAO,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC;oBAC3C,CAAC,CAAC,IAAI,IAAI,CACb,CAAC;gBACJ,CAAC;gBACD,OAAO,IAAI,CAAC;;;;;;;;;SACb;QAEQ,KAAK,CAAC,UAAU,CAEvB,GAAG,KAAe;YAElB,gDAAgD;YAChD,MAAM,IAAI,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;YACpC,IAAI,IAAI,EAAE,CAAC;gBACT,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;oBACvB,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC/D,OAAO,IAAI,CAAC;oBACd,CAAC;yBAAM,CAAC;wBACN,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC5B,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YACD,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACzC,CAAC;QAEQ,KAAK,CAAC,CAAC,WAAW,CAEzB,IAAyB,EACzB,IAAyB;YAEzB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE;gBACjD,IAAI,EAAE,eAAe;gBACrB,KAAK,EAAE;oBACL,IAAI;oBACJ,IAAI;iBACL;aACF,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;gBAClD,6EAA6E;gBAC7E,OAAO,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC;QACL,CAAC;QAEQ,KAAK,CAAC,aAAa;YAC1B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,YAAY,EAAE,CAAC;gBAC9C,MAAM,IAAI,oBAAoB,EAAE,CAAC;YACnC,CAAC;YACD,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,OAAO,IAAI,CAAC,cAAc,CAAC;YAC7B,CAAC;YACD,MAAM,EAAC,IAAI,EAAC,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC9D,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;aACzB,CAAC,CAAC;YACH,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC;YACzC,OAAO,IAAI,CAAC,cAAc,CAAC;QAC7B,CAAC;;;SA5HU,iBAAiB"}