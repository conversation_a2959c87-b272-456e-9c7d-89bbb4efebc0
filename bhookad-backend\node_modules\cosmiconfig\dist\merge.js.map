{"version": 3, "file": "merge.js", "sourceRoot": "", "sources": ["../src/merge.ts"], "names": [], "mappings": ";;;AAAA,sDAAsD;AACzC,QAAA,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAChD,MAAM,CAAC,SAAS,CAAC,cAAc,CAChC,CAAC;AACF,MAAM,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AAE5E,qDAAqD;AAErD,SAAS,aAAa,CAAC,GAAY;IACjC,OAAO,WAAW,CAAC,GAAG,CAAC,KAAK,iBAAiB,CAAC;AAChD,CAAC;AAMD,8DAA8D;AAC9D,SAAS,KAAK,CAAC,MAAW,EAAE,MAAW,EAAE,OAAqB;IAC5D,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QACrC,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,IAAA,cAAM,EAAC,MAAM,EAAE,GAAG,CAAC,EAAE;YACvB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACzD,IAAI,OAAO,CAAC,WAAW,EAAE;oBACvB,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;oBAC9B,SAAS;iBACV;aACF;iBAAM,IAAI,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE;gBAChE,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;gBACpD,SAAS;aACV;SACF;QACD,MAAM,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;KACxB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAgB,QAAQ;AACtB,8DAA8D;AAC9D,OAA2B,EAC3B,OAAqB;IAGrB,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;AAChF,CAAC;AAPD,4BAOC"}