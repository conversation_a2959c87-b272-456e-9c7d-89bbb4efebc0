{"version": 3, "names": ["_iterableToArrayLimit", "arr", "i", "iterator", "Symbol", "_arr", "iteratorNormalCompletion", "didIteratorError", "step", "iteratorError", "next", "_return", "call", "Object", "done", "push", "value", "length", "err"], "sources": ["../../src/helpers/iterableToArrayLimit.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _iterableToArrayLimit<T>(arr: Iterable<T>, i: number) {\n  // this is an expanded form of \\`for...of\\` that properly supports abrupt completions of\n  // iterators etc.\n\n  var iterator: Iterator<T> =\n    arr == null\n      ? null\n      : (typeof Symbol !== \"undefined\" && arr[Symbol.iterator]) ||\n        (arr as any)[\"@@iterator\"];\n  if (iterator == null) return;\n\n  var _arr: T[] = [];\n  var iteratorNormalCompletion = true;\n  var didIteratorError = false;\n  var step, iteratorError, next, _return;\n  try {\n    next = (iterator = (iterator as unknown as Function).call(arr)).next;\n    if (i === 0) {\n      if (Object(iterator) !== iterator) return;\n      iteratorNormalCompletion = false;\n    } else {\n      for (\n        ;\n        !(iteratorNormalCompletion = (step = next.call(iterator)).done);\n        iteratorNormalCompletion = true\n      ) {\n        _arr.push(step.value);\n        if (_arr.length === i) break;\n      }\n    }\n  } catch (err) {\n    didIteratorError = true;\n    iteratorError = err;\n  } finally {\n    try {\n      if (!iteratorNormalCompletion && iterator[\"return\"] != null) {\n        _return = iterator[\"return\"]();\n        // eslint-disable-next-line no-unsafe-finally\n        if (Object(_return) !== _return) return;\n      }\n    } finally {\n      // eslint-disable-next-line no-unsafe-finally\n      if (didIteratorError) throw iteratorError;\n    }\n  }\n  return _arr;\n}\n"], "mappings": ";;;;;;AAEe,SAASA,qBAAqBA,CAAIC,GAAgB,EAAEC,CAAS,EAAE;EAI5E,IAAIC,QAAqB,GACvBF,GAAG,IAAI,IAAI,GACP,IAAI,GACH,OAAOG,MAAM,KAAK,WAAW,IAAIH,GAAG,CAACG,MAAM,CAACD,QAAQ,CAAC,IACrDF,GAAG,CAAS,YAAY,CAAC;EAChC,IAAIE,QAAQ,IAAI,IAAI,EAAE;EAEtB,IAAIE,IAAS,GAAG,EAAE;EAClB,IAAIC,wBAAwB,GAAG,IAAI;EACnC,IAAIC,gBAAgB,GAAG,KAAK;EAC5B,IAAIC,IAAI,EAAEC,aAAa,EAAEC,IAAI,EAAEC,OAAO;EACtC,IAAI;IACFD,IAAI,GAAG,CAACP,QAAQ,GAAIA,QAAQ,CAAyBS,IAAI,CAACX,GAAG,CAAC,EAAES,IAAI;IACpE,IAAIR,CAAC,KAAK,CAAC,EAAE;MACX,IAAIW,MAAM,CAACV,QAAQ,CAAC,KAAKA,QAAQ,EAAE;MACnCG,wBAAwB,GAAG,KAAK;IAClC,CAAC,MAAM;MACL,OAEE,EAAEA,wBAAwB,GAAG,CAACE,IAAI,GAAGE,IAAI,CAACE,IAAI,CAACT,QAAQ,CAAC,EAAEW,IAAI,CAAC,EAC/DR,wBAAwB,GAAG,IAAI,EAC/B;QACAD,IAAI,CAACU,IAAI,CAACP,IAAI,CAACQ,KAAK,CAAC;QACrB,IAAIX,IAAI,CAACY,MAAM,KAAKf,CAAC,EAAE;MACzB;IACF;EACF,CAAC,CAAC,OAAOgB,GAAG,EAAE;IACZX,gBAAgB,GAAG,IAAI;IACvBE,aAAa,GAAGS,GAAG;EACrB,CAAC,SAAS;IACR,IAAI;MACF,IAAI,CAACZ,wBAAwB,IAAIH,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;QAC3DQ,OAAO,GAAGR,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE9B,IAAIU,MAAM,CAACF,OAAO,CAAC,KAAKA,OAAO,EAAE;MACnC;IACF,CAAC,SAAS;MAER,IAAIJ,gBAAgB,EAAE,MAAME,aAAa;IAC3C;EACF;EACA,OAAOJ,IAAI;AACb", "ignoreList": []}