/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
import type { QueryHandler } from './QueryHandler.js';
import { PollingOptions } from './QueryHandler.js';
/**
 * @internal
 */
export declare function getQueryHandlerAndSelector(selector: string): {
    updatedSelector: string;
    polling: PollingOptions;
    QueryHandler: typeof QueryHandler;
};
//# sourceMappingURL=GetQueryHandler.d.ts.map