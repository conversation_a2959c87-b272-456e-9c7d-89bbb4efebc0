// API Configuration
const API_BASE_URL = 'http://localhost:3001/api';

// Generic API call function
const apiCall = async (endpoint, options = {}) => {
  try {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'API call failed');
    }

    return data;
  } catch (error) {
    console.error('API Error:', error);
    throw error;
  }
};

// API Services
export const apiService = {
  // Test API connection
  testConnection: () => apiCall('/test'),

  // Vendors
  getVendors: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiCall(`/vendors${queryString ? `?${queryString}` : ''}`);
  },

  getVendorById: (id) => apiCall(`/vendors/${id}`),

  // Contact
  submitContact: (contactData) => apiCall('/contact', {
    method: 'POST',
    body: JSON.stringify(contactData),
  }),

  // Health check
  healthCheck: () => apiCall('/health', { 
    baseURL: 'http://localhost:5000' 
  }),
};

// Export individual services for convenience
export const vendorService = {
  getAll: apiService.getVendors,
  getById: apiService.getVendorById,
};

export const contactService = {
  submit: apiService.submitContact,
};

export default apiService;
