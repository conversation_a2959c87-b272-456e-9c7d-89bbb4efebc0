import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://xktprpguxerqyqtjgfyw.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhrdHBycGd1eGVycXlxdGpnZnl3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAwOTU4NjYsImV4cCI6MjAzNTY3MTg2Nn0.Kx8zQQGWJHVL-_Qs8vGzJxEqKQGWJHVL_Qs8vGzJxEqK'

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})
