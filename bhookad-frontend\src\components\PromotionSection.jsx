import React from 'react'
import { Star, MapPin, Clock, TrendingUp, Crown, Fire } from 'lucide-react'
import { Link } from 'react-router-dom'
import './PromotionSection.css'

const PromotionSection = () => {
  const featuredVendors = [
    {
      id: 1,
      name: "Sharma Ji Ka Dosa Corner",
      location: "Connaught Place",
      cuisine: "South Indian",
      rating: 4.8,
      reviews: 1250,
      image: "/api/placeholder/300/200",
      specialOffer: "Buy 2 Get 1 Free Dosa",
      promoted: true,
      badge: "Premium",
      priceRange: "₹50-150"
    },
    {
      id: 2,
      name: "<PERSON><PERSON>'s Chinese Express",
      location: "Karol Bagh",
      cuisine: "Chinese",
      rating: 4.7,
      reviews: 980,
      image: "/api/placeholder/300/200",
      specialOffer: "20% Off on Combo Meals",
      promoted: true,
      badge: "Featured",
      priceRange: "₹80-200"
    },
    {
      id: 3,
      name: "<PERSON>riya's Rajasthani Thali",
      location: "Lajpat Nagar",
      cuisine: "Rajasthani",
      rating: 4.9,
      reviews: 1580,
      image: "/api/placeholder/300/200",
      specialOffer: "Unlimited Thali @ ₹199",
      promoted: true,
      badge: "Top Rated",
      priceRange: "₹150-300"
    },
    {
      id: 4,
      name: "Mumbai Street Chaat",
      location: "Chandni Chowk",
      cuisine: "Street Food",
      rating: 4.6,
      reviews: 750,
      image: "/api/placeholder/300/200",
      specialOffer: "Free Lassi with Every Order",
      promoted: true,
      badge: "Trending",
      priceRange: "₹30-100"
    }
  ]

  const promotionPackages = [
    {
      name: "Basic Boost",
      price: "₹2,999",
      duration: "1 Month",
      features: [
        "Featured in search results",
        "Homepage banner (2 days)",
        "Social media mentions",
        "Basic analytics"
      ],
      popular: false
    },
    {
      name: "Premium Push",
      price: "₹7,999",
      duration: "3 Months",
      features: [
        "Top search placement",
        "Homepage banner (1 week)",
        "Vlogger collaboration priority",
        "Advanced analytics",
        "Customer review highlights"
      ],
      popular: true
    },
    {
      name: "Ultimate Exposure",
      price: "₹19,999",
      duration: "6 Months",
      features: [
        "Premium placement everywhere",
        "Dedicated promotion page",
        "Guaranteed vlogger partnerships",
        "Complete analytics suite",
        "Personal account manager"
      ],
      popular: false
    }
  ]

  const benefits = [
    {
      icon: <TrendingUp size={40} />,
      title: "Increased Visibility",
      description: "Get seen by thousands of food lovers daily"
    },
    {
      icon: <Crown size={40} />,
      title: "Premium Placement",
      description: "Top position in search results and homepage"
    },
    {
      icon: <Fire size={40} />,
      title: "Viral Marketing",
      description: "Vlogger collaborations for maximum reach"
    }
  ]

  return (
    <section className="promotion-section section" id="promotion">
      <div className="container">
        <div className="section-header">
          <h2 className="section-title">Promotional Wall</h2>
          <p className="section-subtitle">
            Premium vendors jo extra investment karke apna business boost kar rahe hain
          </p>
        </div>

        {/* Featured Vendors Grid */}
        <div className="featured-vendors">
          <h3 className="subsection-title">Featured Vendors</h3>
          <div className="vendors-grid">
            {featuredVendors.map((vendor) => (
              <div key={vendor.id} className="vendor-card promoted">
                <div className="vendor-image">
                  <img src={vendor.image} alt={vendor.name} />
                  <div className={`promotion-badge ${vendor.badge.toLowerCase()}`}>
                    <Crown size={16} />
                    {vendor.badge}
                  </div>
                </div>
                <div className="vendor-info">
                  <h4>{vendor.name}</h4>
                  <div className="vendor-meta">
                    <div className="location">
                      <MapPin size={14} />
                      <span>{vendor.location}</span>
                    </div>
                    <div className="rating">
                      <Star size={14} fill="currentColor" />
                      <span>{vendor.rating} ({vendor.reviews})</span>
                    </div>
                  </div>
                  <div className="cuisine-price">
                    <span className="cuisine">{vendor.cuisine}</span>
                    <span className="price-range">{vendor.priceRange}</span>
                  </div>
                  <div className="special-offer">
                    <Fire size={16} />
                    <span>{vendor.specialOffer}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Promotion Benefits */}
        <div className="promotion-benefits">
          <h3 className="subsection-title">Why Promote Your Business?</h3>
          <div className="benefits-grid">
            {benefits.map((benefit, index) => (
              <div key={index} className="benefit-card">
                <div className="benefit-icon">{benefit.icon}</div>
                <h4>{benefit.title}</h4>
                <p>{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Promotion Packages */}
        <div className="promotion-packages">
          <h3 className="subsection-title">Promotion Packages</h3>
          <div className="packages-grid">
            {promotionPackages.map((pkg, index) => (
              <div key={index} className={`package-card ${pkg.popular ? 'popular' : ''}`}>
                {pkg.popular && <div className="popular-badge">Most Popular</div>}
                <div className="package-header">
                  <h4>{pkg.name}</h4>
                  <div className="package-price">
                    <span className="price">{pkg.price}</span>
                    <span className="duration">/{pkg.duration}</span>
                  </div>
                </div>
                <div className="package-features">
                  {pkg.features.map((feature, idx) => (
                    <div key={idx} className="feature">
                      <div className="feature-check">✓</div>
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
                <Link to="/register" className="package-btn">
                  Get Started
                </Link>
              </div>
            ))}
          </div>
        </div>

        {/* Success Stats */}
        <div className="success-stats">
          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-number">500%</div>
              <div className="stat-label">Average Growth</div>
            </div>
            <div className="stat-card">
              <div className="stat-number">10K+</div>
              <div className="stat-label">Daily Views</div>
            </div>
            <div className="stat-card">
              <div className="stat-number">85%</div>
              <div className="stat-label">Customer Retention</div>
            </div>
            <div className="stat-card">
              <div className="stat-number">24hrs</div>
              <div className="stat-label">Results Visible</div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="promotion-cta">
          <div className="cta-content">
            <h3>Ready to boost your business?</h3>
            <p>Join premium vendors and see immediate results in your sales</p>
            <div className="cta-buttons">
              <Link to="/register" className="btn-primary">
                <Crown size={20} />
                Start Promotion
              </Link>
              <Link to="/contact" className="btn-secondary">
                Contact Sales
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default PromotionSection
