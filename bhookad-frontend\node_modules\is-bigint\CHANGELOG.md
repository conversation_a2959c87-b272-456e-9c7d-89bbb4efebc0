# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.0](https://github.com/inspect-js/is-bigint/compare/v1.0.4...v1.1.0) - 2024-12-02

### Commits

- [actions] reuse common workflows [`0e63a44`](https://github.com/inspect-js/is-bigint/commit/0e63a44e5d1f26783bd5a40d7aa32c14b50fd567)
- [meta] use `npmignore` to autogenerate an npmignore file [`47584ee`](https://github.com/inspect-js/is-bigint/commit/47584ee7ac726a5c7f5f2f5f34b97529134cf602)
- [Tests] use `for-each` and `es-value-fixtures` [`f226864`](https://github.com/inspect-js/is-bigint/commit/f22686422b46334c64b3658fba1237751d24460c)
- [New] add types [`78e2c47`](https://github.com/inspect-js/is-bigint/commit/78e2c473ff73d73eef27b7eb27695393667360e1)
- [actions] split out node 10-20, and 20+ [`4395a8d`](https://github.com/inspect-js/is-bigint/commit/4395a8d340b1aa3c4e68b37092fcbcace14de41f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `has-symbols`, `object-inspect`, `tape` [`c188501`](https://github.com/inspect-js/is-bigint/commit/c188501f67f4c220fcdf8d280c99e1f6af5d217e)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `object-inspect`, `safe-publish-latest`, `tape` [`5360d32`](https://github.com/inspect-js/is-bigint/commit/5360d32eee0e29f09b02d3adb51086dc71160939)
- [actions] update rebase action to use reusable workflow [`d5c1775`](https://github.com/inspect-js/is-bigint/commit/d5c1775e437be97099a7e63b9bd68852b28e0ba7)
- [actions] update codecov uploader [`c7478c7`](https://github.com/inspect-js/is-bigint/commit/c7478c74498ab954917bd6a561d0ee7d0ba60a6a)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `npmignore`, `object-inspect`, `tape` [`6fbce66`](https://github.com/inspect-js/is-bigint/commit/6fbce66ebab2ef471bfe841c8436eba3dce118c6)
- [meta] add missing `engines.node` [`6f9ed42`](https://github.com/inspect-js/is-bigint/commit/6f9ed42ed8cb00b3fccdb62d4fcc8e7ab074e194)
- [Tests] replace `aud` with `npm audit` [`21846c3`](https://github.com/inspect-js/is-bigint/commit/21846c305e5d662ec81b4922cbc57e9324ccd222)
- [Dev Deps] remove unused `has-symbols`, add missing `has-tostringtag` [`b378d94`](https://github.com/inspect-js/is-bigint/commit/b378d942933ae97ab9610d83d69b52380d6a137c)
- [Deps] update `has-bigints` [`f46c35b`](https://github.com/inspect-js/is-bigint/commit/f46c35be813c05549865477bd771300c2595496e)
- [Dev Deps] add missing peer dep [`2b9be16`](https://github.com/inspect-js/is-bigint/commit/2b9be16ab6150d588f00d037b55050d8372953a3)

## [v1.0.4](https://github.com/inspect-js/is-bigint/compare/v1.0.3...v1.0.4) - 2021-08-11

### Commits

- [eslint] remove unnecessary eslintrc file [`7220aa5`](https://github.com/inspect-js/is-bigint/commit/7220aa515c51649b48ba57bb77f92d85e27557d8)
- [readme] add github actions/codecov badges [`053a071`](https://github.com/inspect-js/is-bigint/commit/053a07123511eef5a91fd7889ae2d8323fbcf7d7)
- [Deps] add `has-bigints` as a runtime dependency [`0fc3c9d`](https://github.com/inspect-js/is-bigint/commit/0fc3c9d5165f62500ea9c27943cb302df65432f7)
- [Dev Deps] update `tape` [`145f11d`](https://github.com/inspect-js/is-bigint/commit/145f11d1d285d92b3144f48178fe0fb3b2f828d9)

## [v1.0.3](https://github.com/inspect-js/is-bigint/compare/v1.0.2...v1.0.3) - 2021-08-06

### Commits

- [Tests] use `has-tostringtag` for easier checking of Symbol.toStringTag [`3b44080`](https://github.com/inspect-js/is-bigint/commit/3b440801b69689d907b33184134f00d7e8a35f9f)
- [Dev Deps] update `auto-changelog`, `eslint`, `object-inspect`, `tape` [`e4d4a6c`](https://github.com/inspect-js/is-bigint/commit/e4d4a6c2ab743b52eda906abd1ed4b0608952533)
- [Fix] use `has-bigints` for more robust BigInt detection [`7bb9d7a`](https://github.com/inspect-js/is-bigint/commit/7bb9d7ab42214c12ce25e9f0cfe2af769388c3bb)

## [v1.0.2](https://github.com/inspect-js/is-bigint/compare/v1.0.1...v1.0.2) - 2021-05-04

### Commits

- [meta] do not publish github action workflow files [`276d677`](https://github.com/inspect-js/is-bigint/commit/276d677d1eac61e990a2f2b523c7cdef70784865)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`cea7fb6`](https://github.com/inspect-js/is-bigint/commit/cea7fb6e882ad7e2f550de2bd9317a4409bcd735)
- [readme] fix repo URLs; remove travis badge [`c8e7c36`](https://github.com/inspect-js/is-bigint/commit/c8e7c3651f3303fddafa61cf29cfbb79ea2d5d4b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `has-symbols`, `object-inspect`, `tape` [`32f3d90`](https://github.com/inspect-js/is-bigint/commit/32f3d909363045fe5d40dde9e4db354344ab4d50)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `object-inspect`, `tape` [`c2f20f5`](https://github.com/inspect-js/is-bigint/commit/c2f20f577d84e68b4e3224abb51024fbc4b9b2ba)
- [meta] remove unneeded token; update checkout action [`94e46e9`](https://github.com/inspect-js/is-bigint/commit/94e46e92a1a85ec022c8febf8d5d3c2369b46e97)
- [meta] use `prepublishOnly` script for npm 7+ [`3e663ec`](https://github.com/inspect-js/is-bigint/commit/3e663ecb09bfdc5dbaaa37aaef4adf28b5e49035)

## [v1.0.1](https://github.com/inspect-js/is-bigint/compare/v1.0.0...v1.0.1) - 2020-11-30

### Commits

- [Tests] use shared travis-ci configs [`28f1211`](https://github.com/inspect-js/is-bigint/commit/28f1211132ad2a6495d816140680fa16c12eb6f3)
- [Tests] migrate tests to Github Actions [`0998c64`](https://github.com/inspect-js/is-bigint/commit/0998c6443d603028f8b988bcdd52d23dbf513031)
- [meta] add `auto-changelog` [`2352de6`](https://github.com/inspect-js/is-bigint/commit/2352de6df8385b256d75ce50c360947243a599f6)
- [Tests] run `nyc` on all tests [`9c16a9a`](https://github.com/inspect-js/is-bigint/commit/9c16a9a5ddaf7c2c578542ab8f00dd4e72eff541)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog`, `object-inspect`, `tape` [`4cd0edd`](https://github.com/inspect-js/is-bigint/commit/4cd0edd1917f789501291315c9eac5f895fd719e)
- [actions] add automatic rebasing / merge commit blocking [`f0f4b91`](https://github.com/inspect-js/is-bigint/commit/f0f4b91dba029dbca5b3cd27ef91c4fb8e3ec51a)
- [actions] add "Allow Edits" workflow [`7f4f46e`](https://github.com/inspect-js/is-bigint/commit/7f4f46ec07679c00e22287ec55b39ff1e4f809cf)
- [meta] create FUNDING.yml [`2d0cb9a`](https://github.com/inspect-js/is-bigint/commit/2d0cb9ae8ddf635f2c472ce49f95f717f2c432bf)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud` [`0ee110e`](https://github.com/inspect-js/is-bigint/commit/0ee110effdcaa96d90f21a0ec6e625941f77c45a)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `has-symbols`, `object-inspect`, `tape` [`5bb7f3a`](https://github.com/inspect-js/is-bigint/commit/5bb7f3a8de6ec5ee2aa573c22e41884f3d2d1cc3)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`, `tape` [`d3d67d0`](https://github.com/inspect-js/is-bigint/commit/d3d67d05f8a3f4cb07085811629f56ef872949c3)
- [Dev Deps] update `auto-changelog`, `tape` [`54e270f`](https://github.com/inspect-js/is-bigint/commit/54e270fa29856dba90f86785fa61bffc79a2825d)
- [Dev Deps] update `@ljharb/eslint-config`, `tape` [`d82bfe7`](https://github.com/inspect-js/is-bigint/commit/d82bfe75c63aeda40f7e473c57f26cea8790ba3a)
- [Dev Deps] update `auto-changelog`; add `aud` [`9c34bd1`](https://github.com/inspect-js/is-bigint/commit/9c34bd1873f08d77c82d8e54ad90c5a1a035e7ea)
- [Tests] add missing `posttest` script [`0690bd9`](https://github.com/inspect-js/is-bigint/commit/0690bd9868d4d8b43424b4224cdbb8659a1c6423)
- [meta] add `funding` field [`7ca36d0`](https://github.com/inspect-js/is-bigint/commit/7ca36d06ebcf6b01a592ecd6758bd2b39c0fb0db)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`5ffa8da`](https://github.com/inspect-js/is-bigint/commit/5ffa8dad9f634891cf739ee118f1b43142b0f01d)
- [Dev Deps] update `eslint` [`8512c2f`](https://github.com/inspect-js/is-bigint/commit/8512c2f29a9267fe8fe19209bcf77df94f127172)
- [Tests] only audit prod deps [`f2147dc`](https://github.com/inspect-js/is-bigint/commit/f2147dc54e72594985e8f6db3932a4f0819dc4db)
- [readme] fix header [`d6eff75`](https://github.com/inspect-js/is-bigint/commit/d6eff75d00471f465768deb92867e878c27733b3)

## v1.0.0 - 2018-09-20

### Commits

- [Tests] add tests [`847f12a`](https://github.com/inspect-js/is-bigint/commit/847f12af125fcefb75ed3517550feedf7dd73e88)
- Initial commit [`b53f3c6`](https://github.com/inspect-js/is-bigint/commit/b53f3c6754e7fd7a9982ff5b1466c4dc9799dad9)
- readme [`66c15fe`](https://github.com/inspect-js/is-bigint/commit/66c15fe1a0d965b8c78bf3a3bfc289dcfec53ee1)
- Implementation [`c2c0974`](https://github.com/inspect-js/is-bigint/commit/c2c0974397825a2a56e1ea8af0546171309d4805)
- package.json [`98b174c`](https://github.com/inspect-js/is-bigint/commit/98b174c24b070053f0548e58f9b87bc9dbdf922a)
- Only apps should have lockfiles [`a77c74b`](https://github.com/inspect-js/is-bigint/commit/a77c74bd8ca5f058f1e6165ae7b33bb84adef98c)
