{"version": 3, "file": "install.js", "sourceRoot": "", "sources": ["../../../../src/node/install.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;AA8DH,4CAkEC;AA7HD,kDAK6B;AAM7B,uEAAyE;AAEzE,gEAAwD;AAExD,KAAK,UAAU,eAAe,CAAC,EAC7B,OAAO,EACP,aAAa,EACb,QAAQ,EACR,QAAQ,GAST;IACC,MAAM,iBAAiB,GACrB,aAAa,EAAE,OAAO,IAAI,kCAAmB,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC;IACrE,MAAM,OAAO,GAAG,aAAa,EAAE,eAAe,CAAC;IAC/C,MAAM,OAAO,GAAG,MAAM,IAAA,yBAAc,EAAC,OAAO,EAAE,QAAQ,EAAE,iBAAiB,CAAC,CAAC;IAE3E,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,IAAA,kBAAO,EAAC;YAC3B,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,wBAAwB,EAAE,SAAS;YACnC,OAAO;YACP,YAAY,EACV,OAAO,KAAK,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS;SAChE,CAAC,CAAC;QACH,WAAW,CAAC,GAAG,OAAO,KAAK,MAAM,CAAC,OAAO,mBAAmB,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;IAC7E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CACb,2BAA2B,OAAO,KAAK,OAAO,gEAAgE,EAC9G;YACE,KAAK,EAAE,KAAK;SACb,CACF,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,gBAAgB;IACpC,aAAa,EAAE,CAAC;IAEhB,MAAM,aAAa,GAAG,IAAA,sCAAgB,GAAE,CAAC;IACzC,IAAI,aAAa,CAAC,YAAY,EAAE,CAAC;QAC/B,WAAW,CAAC,uDAAuD,CAAC,CAAC;QACrE,OAAO;IACT,CAAC;IAED,MAAM,QAAQ,GAAG,IAAA,gCAAqB,GAAE,CAAC;IACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;IAC5D,CAAC;IACD,MAAM,QAAQ,GAAG,aAAa,CAAC,cAAe,CAAC;IAE/C,MAAM,gBAAgB,GAAG,EAAE,CAAC;IAC5B,IAAI,aAAa,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC;QACvC,WAAW,CAAC,kDAAkD,CAAC,CAAC;IAClE,CAAC;SAAM,CAAC;QACN,MAAM,OAAO,GAAG,kBAAO,CAAC,MAAM,CAAC;QAC/B,gBAAgB,CAAC,IAAI,CACnB,eAAe,CAAC;YACd,OAAO;YACP,aAAa,EAAE,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE;YAC3C,QAAQ;YACR,QAAQ;SACT,CAAC,CACH,CAAC;IACJ,CAAC;IAED,IAAI,aAAa,CAAC,uBAAuB,CAAC,EAAE,YAAY,EAAE,CAAC;QACzD,WAAW,CAAC,kDAAkD,CAAC,CAAC;IAClE,CAAC;SAAM,CAAC;QACN,MAAM,OAAO,GAAG,kBAAO,CAAC,mBAAmB,CAAC;QAE5C,gBAAgB,CAAC,IAAI,CACnB,eAAe,CAAC;YACd,OAAO;YACP,aAAa,EAAE,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE;YAC3C,QAAQ;YACR,QAAQ;SACT,CAAC,CACH,CAAC;IACJ,CAAC;IAED,IAAI,aAAa,CAAC,OAAO,EAAE,YAAY,EAAE,CAAC;QACxC,WAAW,CAAC,mDAAmD,CAAC,CAAC;IACnE,CAAC;SAAM,CAAC;QACN,MAAM,OAAO,GAAG,kBAAO,CAAC,OAAO,CAAC;QAEhC,gBAAgB,CAAC,IAAI,CACnB,eAAe,CAAC;YACd,OAAO;YACP,aAAa,EAAE,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE;YAC3C,QAAQ;YACR,QAAQ;SACT,CAAC,CACH,CAAC;IACJ,CAAC;IAED,IAAI,CAAC;QACH,MAAM,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IACtC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACrB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,WAAW,CAAC,UAAmB;IACtC,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC;IAC1D,MAAM,eAAe,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IAE3E,IAAI,CAAC,eAAe,EAAE,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC1B,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,aAAa;IACpB,8EAA8E;IAC9E,MAAM,eAAe,GACnB,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAC3E,MAAM,cAAc,GAClB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAC1E,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;IAExD,IAAI,eAAe,EAAE,CAAC;QACpB,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,eAAe,CAAC;IAC/C,CAAC;IACD,IAAI,cAAc,EAAE,CAAC;QACnB,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,cAAc,CAAC;IAC7C,CAAC;IACD,IAAI,YAAY,EAAE,CAAC;QACjB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC;IACzC,CAAC;AACH,CAAC"}