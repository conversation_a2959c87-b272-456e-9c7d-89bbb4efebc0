.contact {
  background: #f8f9fa;
}

.contact-subtitle {
  text-align: center;
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 3rem;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 4rem;
}

.contact-info h3, .contact-form-section h3 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 1.5rem;
  position: relative;
}

.contact-info h3::after, .contact-form-section h3::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  border-radius: 2px;
}

.contact-info p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.contact-cards {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-card {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.contact-card:hover {
  transform: translateY(-3px);
}

.contact-icon {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  padding: 0.75rem;
  border-radius: 10px;
  flex-shrink: 0;
}

.contact-details h4 {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.contact-details p {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.contact-form {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #ff6b35;
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  width: 100%;
  padding: 1rem;
  font-size: 1.1rem;
}

.contact-cta {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  padding: 3rem;
  border-radius: 20px;
  text-align: center;
}

.contact-cta h3 {
  font-size: 1.8rem;
  margin: 1rem 0;
}

.contact-cta p {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .contact-form {
    padding: 1.5rem;
  }
  
  .contact-cta {
    padding: 2rem;
  }
}
