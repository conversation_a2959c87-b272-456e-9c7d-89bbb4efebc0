import React from 'react'
import { Search, MapPin, ShoppingBag } from 'lucide-react'
import './HowItWorks.css'

const HowItWorks = () => {
  const steps = [
    {
      icon: <Search size={40} />,
      title: "Discover",
      description: "Browse through trending street food vendors in your area"
    },
    {
      icon: <MapPin size={40} />,
      title: "Locate",
      description: "Find the exact location of your favorite food vendors"
    },
    {
      icon: <ShoppingBag size={40} />,
      title: "Enjoy",
      description: "Visit and enjoy authentic street food experiences"
    }
  ]

  return (
    <section className="how-it-works section" id="how-it-works">
      <div className="container">
        <h2 className="section-title">How Bhookad Works</h2>
        <div className="steps-grid">
          {steps.map((step, index) => (
            <div key={index} className="step-card">
              <div className="step-number">{index + 1}</div>
              <div className="step-icon">{step.icon}</div>
              <h3 className="step-title">{step.title}</h3>
              <p className="step-description">{step.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default HowItWorks
