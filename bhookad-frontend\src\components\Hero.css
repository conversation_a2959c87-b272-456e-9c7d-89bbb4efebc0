.hero {
  background: linear-gradient(135deg, #fff3e0, #ffe0b2);
  padding: 80px 0;
  min-height: 70vh;
  display: flex;
  align-items: center;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  margin-bottom: 4rem;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: bold;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  color: #333;
}

.highlight {
  color: #ff6b35;
  position: relative;
}

.highlight::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  border-radius: 2px;
}

.hero-description {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.food-card {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
  transform: rotate(-5deg);
  transition: transform 0.3s ease;
}

.food-card:hover {
  transform: rotate(0deg) scale(1.05);
}

.food-image-placeholder {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.food-card h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.star-filled {
  color: #ffc107;
  fill: #ffc107;
}

.rating span {
  margin-left: 0.5rem;
  font-weight: 600;
  color: #333;
}

.stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
}

.stat-icon {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .hero {
    padding: 40px 0;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }

  .stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }
}
