/* Authentication Components Styles */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  padding: 20px;
}

.auth-card {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-header h1 {
  color: #333;
  font-size: 2rem;
  margin-bottom: 10px;
  font-weight: 700;
}

.auth-header p {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #fcc;
  font-size: 0.9rem;
}

.auth-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 500;
  font-size: 0.9rem;
}

.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-icon svg {
  position: absolute;
  left: 12px;
  color: #999;
  z-index: 1;
}

.input-with-icon input {
  width: 100%;
  padding: 12px 12px 12px 45px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.input-with-icon input:focus {
  outline: none;
  border-color: #ff6b35;
  background: white;
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.3s ease;
}

.password-toggle:hover {
  color: #666;
}

.auth-btn {
  width: 100%;
  padding: 14px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 10px;
  transition: all 0.3s ease;
  margin-bottom: 10px;
}

.btn-google {
  width: 100%;
  padding: 14px;
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.btn-google:hover {
  border-color: #ccc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-google:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.auth-divider {
  text-align: center;
  margin: 20px 0;
  position: relative;
}

.auth-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e1e5e9;
}

.auth-divider span {
  background: white;
  padding: 0 15px;
  color: #999;
  font-size: 0.9rem;
}

.auth-footer {
  text-align: center;
  margin-top: 20px;
}

.auth-footer p {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.auth-link {
  color: #ff6b35;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.auth-link:hover {
  color: #e55a2b;
  text-decoration: underline;
}

/* Role Selection Styles */
.role-selection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.role-card {
  border: 2px solid #e1e5e9;
  border-radius: 15px;
  padding: 25px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.role-card:hover {
  border-color: #ff6b35;
  background: white;
  box-shadow: 0 5px 15px rgba(255, 107, 53, 0.1);
  transform: translateY(-2px);
}

.role-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.role-card h3 {
  color: #333;
  font-size: 1.2rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.role-card p {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 15px;
  line-height: 1.4;
}

.role-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: left;
}

.role-card li {
  color: #666;
  font-size: 0.85rem;
  margin-bottom: 5px;
  padding-left: 15px;
  position: relative;
}

.role-card li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #ff6b35;
  font-weight: bold;
}

.change-role-btn {
  background: none;
  border: none;
  color: #ff6b35;
  font-size: 0.8rem;
  cursor: pointer;
  margin-left: 10px;
  text-decoration: underline;
}

.change-role-btn:hover {
  color: #e55a2b;
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #ff6b35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Role Selection Loading */
.role-card.selecting {
  border-color: #ff6b35;
  background: white;
  box-shadow: 0 5px 15px rgba(255, 107, 53, 0.2);
}

.role-loading {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #ff6b35;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

.role-card {
  position: relative;
}

/* Error Actions */
.error-actions {
  text-align: center;
  margin-top: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-container {
    padding: 10px;
  }

  .auth-card {
    padding: 30px 20px;
  }

  .auth-header h1 {
    font-size: 1.5rem;
  }

  .role-selection {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .role-card {
    padding: 20px 15px;
  }

  .role-icon {
    font-size: 2.5rem;
  }
}
