import React from 'react'
import { Quote, <PERSON> } from 'lucide-react'
import './SuccessStories.css'

const SuccessStories = () => {
  const stories = [
    {
      name: "<PERSON><PERSON>",
      location: "Delhi",
      rating: 5,
      story: "<PERSON><PERSON><PERSON><PERSON> helped me discover the most amazing street food vendors in my area. The authentic flavors and quality recommendations are outstanding!",
      avatar: "👩"
    },
    {
      name: "<PERSON><PERSON>",
      location: "Mumbai",
      rating: 5,
      story: "As a food blogger, <PERSON><PERSON><PERSON><PERSON> has been my go-to platform for finding trending street food spots. Highly recommended for all food lovers!",
      avatar: "👨"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      location: "Bangalore",
      rating: 4,
      story: "Great platform to explore local street food culture. Found some hidden gems through <PERSON><PERSON>oka<PERSON> that I would never have discovered otherwise.",
      avatar: "👩‍💼"
    }
  ]

  return (
    <section className="success-stories section">
      <div className="container">
        <h2 className="section-title">Success Stories</h2>
        <div className="stories-grid">
          {stories.map((story, index) => (
            <div key={index} className="story-card">
              <Quote className="quote-icon" size={24} />
              <p className="story-text">"{story.story}"</p>
              
              <div className="story-rating">
                {[...Array(5)].map((_, i) => (
                  <Star 
                    key={i} 
                    size={16} 
                    className={i < story.rating ? 'star-filled' : 'star-empty'} 
                  />
                ))}
              </div>
              
              <div className="story-author">
                <div className="author-avatar">{story.avatar}</div>
                <div className="author-info">
                  <h4 className="author-name">{story.name}</h4>
                  <p className="author-location">{story.location}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default SuccessStories
