.vloggers-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #f1f2f6 0%, #ddd6fe 100%);
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3436;
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #636e72;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Benefits Grid */
.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 80px;
}

.benefit-card {
  background: white;
  padding: 40px 30px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #e9ecef;
}

.benefit-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.benefit-icon {
  color: #6c5ce7;
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.benefit-card h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2d3436;
  margin-bottom: 12px;
}

.benefit-card p {
  color: #636e72;
  line-height: 1.6;
  margin-bottom: 16px;
}

.highlight {
  display: inline-block;
  background: linear-gradient(135deg, #6c5ce7, #a29bfe);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Top Vloggers */
.top-vloggers {
  margin-bottom: 80px;
}

.subsection-title {
  font-size: 2rem;
  font-weight: 600;
  color: #2d3436;
  text-align: center;
  margin-bottom: 40px;
}

.vloggers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.vlogger-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.vlogger-card:hover {
  transform: translateY(-5px);
}

.vlogger-header {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 20px;
  position: relative;
}

.vlogger-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.vlogger-name {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.vlogger-name h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3436;
}

.verified-badge {
  background: #00b894;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
}

.vlogger-specialty {
  color: #6c5ce7;
  font-weight: 500;
  margin-bottom: 4px;
}

.vlogger-platform {
  color: #636e72;
  font-size: 0.9rem;
}

.earnings-badge {
  position: absolute;
  right: 0;
  top: 0;
  background: linear-gradient(135deg, #fdcb6e, #e17055);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

.vlogger-stats {
  display: flex;
  gap: 20px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #636e72;
  font-size: 0.9rem;
}

/* How It Works */
.how-it-works {
  margin-bottom: 80px;
}

.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.step-card {
  background: white;
  padding: 30px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  position: relative;
}

.step-number {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #6c5ce7, #a29bfe);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 auto 20px;
}

.step-card h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3436;
  margin-bottom: 12px;
}

.step-card p {
  color: #636e72;
  line-height: 1.6;
}

/* Features Section */
.features-section {
  margin-bottom: 80px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 15px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.feature-check {
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #6c5ce7, #a29bfe);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.8rem;
}

/* CTA Section */
.vlogger-cta {
  background: linear-gradient(135deg, #6c5ce7, #a29bfe);
  border-radius: 20px;
  padding: 60px 40px;
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 40px;
  align-items: center;
  color: white;
}

.cta-content h3 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 12px;
}

.cta-content p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 30px;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.cta-buttons .btn-primary {
  display: flex;
  align-items: center;
  gap: 8px;
}

.cta-stats {
  display: flex;
  gap: 40px;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #fdcb6e;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .vloggers-section {
    padding: 60px 0;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .benefits-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .vloggers-grid {
    grid-template-columns: 1fr;
  }
  
  .steps-grid {
    grid-template-columns: 1fr;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .vlogger-cta {
    grid-template-columns: 1fr;
    text-align: center;
    padding: 40px 20px;
  }
  
  .cta-stats {
    justify-content: center;
  }
  
  .cta-buttons {
    justify-content: center;
  }
}
