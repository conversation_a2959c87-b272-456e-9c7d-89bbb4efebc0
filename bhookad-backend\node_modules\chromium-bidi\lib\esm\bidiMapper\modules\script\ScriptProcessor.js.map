{"version": 3, "file": "ScriptProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/script/ScriptProcessor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAIL,YAAY,EACZ,wBAAwB,GACzB,MAAM,+BAA+B,CAAC;AAOvC,OAAO,EAAC,aAAa,EAAC,MAAM,oBAAoB,CAAC;AAKjD,MAAM,OAAO,eAAe;IACjB,aAAa,CAAe;IAC5B,uBAAuB,CAAyB;IAChD,aAAa,CAAe;IAC5B,qBAAqB,CAAC;IACtB,mBAAmB,CAAqB;IACxC,OAAO,CAAY;IAE5B,YACE,YAA0B,EAC1B,sBAA8C,EAC9C,YAA0B,EAC1B,oBAA0C,EAC1C,kBAAsC,EACtC,MAAiB;QAEjB,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;QAClD,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAC9C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CACjC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,EAC3C,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,CAC7C,CAAC;IACJ,CAAC;IAED,4BAA4B,CAC1B,SAA0C;QAE1C,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACnE,MAAM,gBAAgB,GAAG;YACvB,OAAO;YACP,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,WAAW;SAClE,CAAC;QAEF,MAAM,MAAM,GAAG,IAAI,GAAG,EAAS,CAAC;QAChC,KAAK,MAAM,aAAa,IAAI,gBAAgB,EAAE,CAAC;YAC7C,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;gBACrD,iBAAiB,EAAE,aAAa,CAAC,EAAE;aACpC,CAAC,CAAC;YACH,KAAK,MAAM,KAAK,IAAI,gBAAgB,EAAE,CAAC;gBACrC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;QAED,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,CAAC,aAAa,CAAC,aAAa,CAC9B;gBACE,IAAI,EAAE,OAAO;gBACb,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY;gBACnD,MAAM,EAAE,KAAK,CAAC,SAAS;aACxB,EACD,OAAO,CAAC,EAAE,CACX,CAAC;QACJ,CAAC;QAED,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,MAAyC;QAEzC,IAAI,MAAM,CAAC,YAAY,EAAE,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC;YAC3D,MAAM,IAAI,wBAAwB,CAChC,qDAAqD,CACtD,CAAC;QACJ,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,uBAAuB,CACzE,MAAM,CAAC,YAAY,IAAI,EAAE,CAC1B,CAAC;QAEF,MAAM,gBAAgB,GACpB,IAAI,CAAC,uBAAuB,CAAC,0BAA0B,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAE3E,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9D,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAE9C,IAAI,eAAe,GAA0B,EAAE,CAAC;QAChD,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC;YACtB,eAAe,GAAG,IAAI,CAAC,uBAAuB;iBAC3C,mBAAmB,EAAE;iBACrB,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE;gBAClB,OAAO,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;QACP,CAAC;aAAM,IAAI,gBAAgB,CAAC,IAAI,EAAE,CAAC;YACjC,eAAe,GAAG,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;QACnD,CAAC;aAAM,CAAC;YACN,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,EAAE,CAAC;QACvE,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,GAAG,CACxB,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CACpD,CAAC;QAEF,MAAM,aAAa,CAAC,aAAa,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAErD,OAAO;YACL,MAAM,EAAE,aAAa,CAAC,EAAE;SACzB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,mBAAmB,CACvB,MAA4C;QAE5C,MAAM,EAAC,MAAM,EAAE,EAAE,EAAC,GAAG,MAAM,CAAC;QAE5B,MAAM,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;QAC/D,MAAM,MAAM,CAAC,MAAM,EAAE,CAAC;QACtB,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAEtC,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,MAAqC;QAErC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAClD,OAAO,MAAM,KAAK,CAAC,YAAY,CAC7B,MAAM,CAAC,mBAAmB,EAC1B,MAAM,CAAC,YAAY,EACnB,MAAM,CAAC,IAAI,EACX,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,eAAe,EACtB,MAAM,CAAC,oBAAoB,EAC3B,MAAM,CAAC,cAAc,CACtB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,MAAiC;QAEjC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAClD,OAAO,MAAM,KAAK,CAAC,QAAQ,CACzB,MAAM,CAAC,UAAU,EACjB,MAAM,CAAC,YAAY,EACnB,MAAM,CAAC,eAAe,EACtB,MAAM,CAAC,oBAAoB,EAC3B,MAAM,CAAC,cAAc,CACtB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,MAA+B;QAC1C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAClD,MAAM,OAAO,CAAC,GAAG,CACf,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CACjE,CAAC;QACF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,SAAS,CAAC,MAAkC;QAC1C,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YACjC,kCAAkC;YAClC,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC1D,CAAC;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa;aAC9B,UAAU,CAAC;YACV,iBAAiB,EAAE,MAAM,CAAC,OAAO;YACjC,IAAI,EAAE,MAAM,CAAC,IAAI;SAClB,CAAC;aACD,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACnC,OAAO,EAAC,MAAM,EAAC,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,MAAqB;QACnC,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;YACxB,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACxE,OAAO,MAAM,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;YACjC,OAAO,EAAE,MAAM,CAAC,KAAK;SACtB,CAAC,CAAC;IACL,CAAC;CACF"}