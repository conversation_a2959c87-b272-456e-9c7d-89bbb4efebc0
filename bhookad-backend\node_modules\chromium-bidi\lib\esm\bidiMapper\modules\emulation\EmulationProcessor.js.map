{"version": 3, "file": "EmulationProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/emulation/EmulationProcessor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAC,wBAAwB,EAAC,MAAM,oCAAoC,CAAC;AAS5E,MAAM,OAAO,kBAAkB;IAC7B,mBAAmB,CAAqB;IACxC,uBAAuB,CAAyB;IAEhD,YACE,sBAA8C,EAC9C,kBAAsC;QAEtC,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAC9C,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;IACxD,CAAC;IAED,KAAK,CAAC,sBAAsB,CAC1B,MAAkD;QAElD,IAAI,aAAa,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,EAAE,CAAC;YACjD,yCAAyC;YACzC,MAAM,IAAI,wBAAwB,CAChC,sDAAsD,CACvD,CAAC;QACJ,CAAC;QAED,IAAI,WAAW,GAGJ,IAAI,CAAC;QAEhB,IAAI,aAAa,IAAI,MAAM,EAAE,CAAC;YAC5B,IACE,CAAC,MAAM,CAAC,WAAW,EAAE,QAAQ,IAAI,IAAI,CAAC,KAAK,IAAI;gBAC/C,CAAC,MAAM,CAAC,WAAW,EAAE,gBAAgB,IAAI,IAAI,CAAC,KAAK,IAAI,EACvD,CAAC;gBACD,MAAM,IAAI,wBAAwB,CAChC,4DAA4D,CAC7D,CAAC;YACJ,CAAC;YAED,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;QACnC,CAAC;aAAM,IAAI,OAAO,IAAI,MAAM,EAAE,CAAC;YAC7B,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,qBAAqB,EAAE,CAAC;gBAChD,yCAAyC;gBACzC,MAAM,IAAI,wBAAwB,CAChC,6BAA6B,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CACjD,CAAC;YACJ,CAAC;YACD,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,yCAAyC;YACzC,MAAM,IAAI,wBAAwB,CAAC,oCAAoC,CAAC,CAAC;QAC3E,CAAC;QAED,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mCAAmC,CACrE,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,YAAY,CACpB,CAAC;QAEF,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;YACtD,MAAM,iBAAiB,GACrB,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACpD,iBAAiB,CAAC,WAAW,GAAG,WAAW,CAAC;QAC9C,CAAC;QAED,MAAM,OAAO,CAAC,GAAG,CACf,gBAAgB,CAAC,GAAG,CAClB,KAAK,EAAE,OAAO,EAAE,EAAE,CAChB,MAAM,OAAO,CAAC,SAAS,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAC9D,CACF,CAAC;QACF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mCAAmC,CACvC,kBAA6B,EAC7B,cAAyB;QAEzB,IAAI,kBAAkB,KAAK,SAAS,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;YACrE,MAAM,IAAI,wBAAwB,CAChC,4DAA4D,CAC7D,CAAC;QACJ,CAAC;QAED,IAAI,kBAAkB,KAAK,SAAS,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;YACrE,MAAM,IAAI,wBAAwB,CAChC,4DAA4D,CAC7D,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAI,kBAAkB,KAAK,SAAS,EAAE,CAAC;YACrC,+BAA+B;YAC/B,IAAI,cAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,MAAM,IAAI,wBAAwB,CAAC,iCAAiC,CAAC,CAAC;YACxE,CAAC;YAED,uCAAuC;YACvC,MAAM,IAAI,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,cAAe,CAAC,CAAC;YAExE,KAAK,MAAM,aAAa,IAAI,cAAe,EAAE,CAAC;gBAC5C,MAAM,wBAAwB,GAAG,IAAI,CAAC,uBAAuB;qBAC1D,mBAAmB,EAAE;qBACrB,MAAM,CACL,CAAC,eAAe,EAAE,EAAE,CAAC,eAAe,CAAC,WAAW,KAAK,aAAa,CACnE,CAAC;gBACJ,MAAM,CAAC,IAAI,CAAC,GAAG,wBAAwB,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACpC,MAAM,IAAI,wBAAwB,CAChC,qCAAqC,CACtC,CAAC;YACJ,CAAC;YAED,KAAK,MAAM,iBAAiB,IAAI,kBAAkB,EAAE,CAAC;gBACnD,MAAM,eAAe,GACnB,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;gBAC7D,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,EAAE,CAAC;oBACzC,MAAM,IAAI,wBAAwB,CAChC,wDAAwD,CACzD,CAAC;gBACJ,CAAC;gBACD,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;QACD,oFAAoF;QACpF,sEAAsE;QACtE,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;IACvC,CAAC;CACF"}