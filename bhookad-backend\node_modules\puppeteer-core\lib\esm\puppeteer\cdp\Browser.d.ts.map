{"version": 3, "file": "Browser.d.ts", "sourceRoot": "", "sources": ["../../../../src/cdp/Browser.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,oBAAoB,CAAC;AAIrD,OAAO,KAAK,EAAC,SAAS,EAAC,MAAM,mBAAmB,CAAC;AACjD,OAAO,EACL,OAAO,IAAI,WAAW,EAEtB,KAAK,oBAAoB,EACzB,KAAK,qBAAqB,EAC1B,KAAK,oBAAoB,EACzB,KAAK,oBAAoB,EAC1B,MAAM,mBAAmB,CAAC;AAG3B,OAAO,KAAK,EAAC,IAAI,EAAC,MAAM,gBAAgB,CAAC;AAEzC,OAAO,KAAK,EAAC,gBAAgB,EAAC,MAAM,+BAA+B,CAAC;AACpE,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,uBAAuB,CAAC;AAEpD,OAAO,EAAC,iBAAiB,EAAC,MAAM,qBAAqB,CAAC;AAEtD,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAML,KAAK,SAAS,EACf,MAAM,aAAa,CAAC;AAErB,OAAO,EAAC,aAAa,EAAC,MAAM,oBAAoB,CAAC;AAEjD;;GAEG;AACH,qBAAa,UAAW,SAAQ,WAAW;;IACzC,QAAQ,CAAC,QAAQ,SAAS;WAEb,OAAO,CAClB,UAAU,EAAE,UAAU,EACtB,UAAU,EAAE,MAAM,EAAE,EACpB,mBAAmB,EAAE,OAAO,EAC5B,eAAe,CAAC,EAAE,QAAQ,GAAG,IAAI,EACjC,gBAAgB,CAAC,EAAE,gBAAgB,EACnC,OAAO,CAAC,EAAE,YAAY,EACtB,aAAa,CAAC,EAAE,oBAAoB,EACpC,oBAAoB,CAAC,EAAE,oBAAoB,EAC3C,oBAAoB,CAAC,EAAE,oBAAoB,EAC3C,iCAAiC,UAAO,GACvC,OAAO,CAAC,UAAU,CAAC;gBA8BpB,UAAU,EAAE,UAAU,EACtB,UAAU,EAAE,MAAM,EAAE,EACpB,eAAe,CAAC,EAAE,QAAQ,GAAG,IAAI,EACjC,OAAO,CAAC,EAAE,YAAY,EACtB,aAAa,CAAC,EAAE,oBAAoB,EACpC,oBAAoB,CAAC,EAAE,oBAAoB,EAC3C,oBAAoB,CAAC,EAAE,oBAAoB,EAC3C,iCAAiC,UAAO;IAgCpC,OAAO,CAAC,gBAAgB,EAAE,gBAAgB,GAAG,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC;IAwB5E,OAAO,IAAI,IAAI;IAoBN,OAAO,IAAI,YAAY,GAAG,IAAI;IAIvC,cAAc,IAAI,aAAa;IAgB/B,wBAAwB,IAAI,oBAAoB,GAAG,SAAS;IAI7C,oBAAoB,CACjC,OAAO,GAAE,qBAA0B,GAClC,OAAO,CAAC,iBAAiB,CAAC;IAsBpB,eAAe,IAAI,iBAAiB,EAAE;IAItC,qBAAqB,IAAI,iBAAiB;IAI7C,eAAe,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAsG/C,UAAU,IAAI,MAAM;IAId,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAIjC,oBAAoB,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IA0B9C,gBAAgB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAKrD,kBAAkB,CAAC,EAAE,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAI7C,OAAO,IAAI,SAAS,EAAE;IAWtB,MAAM,IAAI,SAAS;IAUb,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;IAK1B,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC;IAK5B,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAK5B,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAOpC,IAAa,SAAS,IAAI,OAAO,CAEhC;IAMD,IAAa,SAAS,IAAI,SAAS,CAIlC;CACF"}