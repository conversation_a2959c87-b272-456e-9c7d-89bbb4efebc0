export declare const cuid: RegExp;
export declare const cuid2: RegExp;
export declare const ulid: RegExp;
export declare const xid: RegExp;
export declare const ksuid: RegExp;
export declare const nanoid: RegExp;
/** ISO 8601-1 duration regex. Does not support the 8601-2 extensions like negative durations or fractional/negative components. */
export declare const duration: RegExp;
/** Implements ISO 8601-2 extensions like explicit +- prefixes, mixing weeks with other units, and fractional/negative components. */
export declare const extendedDuration: RegExp;
/** A regex for any UUID-like identifier: 8-4-4-4-12 hex pattern */
export declare const guid: RegExp;
/** Returns a regex for validating an RFC 4122 UUID.
 *
 * @param version Optionally specify a version 1-8. If no version is specified, all versions are supported. */
export declare const uuid: (version?: number | undefined) => RegExp;
export declare const uuid4: RegExp;
export declare const uuid6: RegExp;
export declare const uuid7: RegExp;
/** Practical email validation */
export declare const email: RegExp;
/** Equivalent to the HTML5 input[type=email] validation implemented by browsers. Source: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/email */
export declare const html5Email: RegExp;
/** The classic emailregex.com regex for RFC 5322-compliant emails */
export declare const rfc5322Email: RegExp;
/** A loose regex that allows Unicode characters, enforces length limits, and that's about it. */
export declare const unicodeEmail: RegExp;
export declare const browserEmail: RegExp;
export declare const _emoji = "^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";
export declare function emoji(): RegExp;
export declare const ipv4: RegExp;
export declare const ipv6: RegExp;
export declare const cidrv4: RegExp;
export declare const cidrv6: RegExp;
export declare const base64: RegExp;
export declare const base64url: RegExp;
export declare const hostname: RegExp;
export declare const domain: RegExp;
export declare const e164: RegExp;
export declare const date: RegExp;
export declare function time(args: {
    precision?: number | null;
}): RegExp;
export declare function datetime(args: {
    precision?: number | null;
    offset?: boolean;
    local?: boolean;
}): RegExp;
export declare const string: (params?: {
    minimum?: number | undefined;
    maximum?: number | undefined;
}) => RegExp;
export declare const bigint: RegExp;
export declare const integer: RegExp;
export declare const number: RegExp;
export declare const boolean: RegExp;
declare const _null: RegExp;
export { _null as null };
declare const _undefined: RegExp;
export { _undefined as undefined };
export declare const lowercase: RegExp;
export declare const uppercase: RegExp;
