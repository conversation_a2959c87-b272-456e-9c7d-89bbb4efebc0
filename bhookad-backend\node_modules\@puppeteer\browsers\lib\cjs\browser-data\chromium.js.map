{"version": 3, "file": "chromium.js", "sourceRoot": "", "sources": ["../../../src/browser-data/chromium.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;AAuCH,gDAMC;AAED,kDAKC;AAED,wDAqBC;AACD,wCAUC;AAED,0CAEC;AAxFD,0DAA6B;AAE7B,gDAAuC;AAEvC,yCAA2C;AAE3C,SAAS,OAAO,CAAC,QAAyB,EAAE,OAAe;IACzD,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,0BAAe,CAAC,SAAS,CAAC;QAC/B,KAAK,0BAAe,CAAC,KAAK;YACxB,OAAO,cAAc,CAAC;QACxB,KAAK,0BAAe,CAAC,OAAO,CAAC;QAC7B,KAAK,0BAAe,CAAC,GAAG;YACtB,OAAO,YAAY,CAAC;QACtB,KAAK,0BAAe,CAAC,KAAK,CAAC;QAC3B,KAAK,0BAAe,CAAC,KAAK;YACxB,2CAA2C;YAC3C,OAAO,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC;IAC1E,CAAC;AACH,CAAC;AAED,SAAS,MAAM,CAAC,QAAyB;IACvC,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,0BAAe,CAAC,KAAK;YACxB,OAAO,WAAW,CAAC;QACrB,KAAK,0BAAe,CAAC,OAAO;YAC1B,OAAO,SAAS,CAAC;QACnB,KAAK,0BAAe,CAAC,SAAS,CAAC;QAC/B,KAAK,0BAAe,CAAC,GAAG;YACtB,OAAO,KAAK,CAAC;QACf,KAAK,0BAAe,CAAC,KAAK;YACxB,OAAO,KAAK,CAAC;QACf,KAAK,0BAAe,CAAC,KAAK;YACxB,OAAO,SAAS,CAAC;IACrB,CAAC;AACH,CAAC;AAED,SAAgB,kBAAkB,CAChC,QAAyB,EACzB,OAAe,EACf,OAAO,GAAG,2DAA2D;IAErE,OAAO,GAAG,OAAO,IAAI,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;AAC1E,CAAC;AAED,SAAgB,mBAAmB,CACjC,QAAyB,EACzB,OAAe;IAEf,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AAC1E,CAAC;AAED,SAAgB,sBAAsB,CACpC,QAAyB,EACzB,QAAgB;IAEhB,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,0BAAe,CAAC,GAAG,CAAC;QACzB,KAAK,0BAAe,CAAC,OAAO;YAC1B,OAAO,mBAAI,CAAC,IAAI,CACd,YAAY,EACZ,cAAc,EACd,UAAU,EACV,OAAO,EACP,UAAU,CACX,CAAC;QACJ,KAAK,0BAAe,CAAC,SAAS,CAAC;QAC/B,KAAK,0BAAe,CAAC,KAAK;YACxB,OAAO,mBAAI,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAC7C,KAAK,0BAAe,CAAC,KAAK,CAAC;QAC3B,KAAK,0BAAe,CAAC,KAAK;YACxB,OAAO,mBAAI,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;IACjD,CAAC;AACH,CAAC;AACM,KAAK,UAAU,cAAc,CAClC,QAAyB;IAEzB,OAAO,MAAM,IAAA,qBAAO,EAClB,IAAI,GAAG,CACL,6DAA6D,MAAM,CACjE,QAAQ,CACT,cAAc,CAChB,CACF,CAAC;AACJ,CAAC;AAED,SAAgB,eAAe,CAAC,CAAS,EAAE,CAAS;IAClD,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B,CAAC"}