import React from 'react'
import { MapPin, Star, Clock, Users, TrendingUp, DollarSign } from 'lucide-react'
import { Link } from 'react-router-dom'
import './VendorsSection.css'

const VendorsSection = () => {
  const vendorBenefits = [
    {
      icon: <Users size={40} />,
      title: "Customer Reach",
      description: "Apne thele ko thousands of food lovers tak pahunchaye",
      highlight: "300% more customers"
    },
    {
      icon: <TrendingUp size={40} />,
      title: "Business Growth",
      description: "Vloggers ke saath collaborate karke business badhaye",
      highlight: "Digital marketing"
    },
    {
      icon: <DollarSign size={40} />,
      title: "Increased Revenue",
      description: "Monthly income mein significant increase dekhe",
      highlight: "Average 250% growth"
    },
    {
      icon: <Star size={40} />,
      title: "Brand Building",
      description: "Local area mein apna brand establish kare",
      highlight: "Social media presence"
    }
  ]

  const successStories = [
    {
      name: "<PERSON>u Uncle",
      business: "Chinese Food Corner",
      location: "Connaught Place",
      growth: "300%",
      story: "Bhookad ke baad mera business triple ho gaya. Ab daily 200+ customers aate hain!",
      rating: 4.8,
      image: "/api/placeholder/80/80"
    },
    {
      name: "<PERSON> <PERSON>",
      business: "South Indian Delights",
      location: "Karol Bagh",
      growth: "250%",
      story: "Vloggers ke videos se meri dosa famous ho gayi. Ab queue lagti hai!",
      rating: 4.9,
      image: "/api/placeholder/80/80"
    },
    {
      name: "Priya Devi",
      business: "Rajasthani Thali",
      location: "Lajpat Nagar",
      growth: "400%",
      story: "Platform join karne ke baad business boom ho gaya. Thank you Bhookad!",
      rating: 4.7,
      image: "/api/placeholder/80/80"
    }
  ]

  const features = [
    "Real-time analytics dashboard",
    "Vlogger collaboration matching",
    "Customer feedback management",
    "Payment tracking system",
    "Promotional campaign tools",
    "Business insights reports"
  ]

  return (
    <section className="vendors-section section" id="vendors">
      <div className="container">
        <div className="section-header">
          <h2 className="section-title">Vendors ke liye</h2>
          <p className="section-subtitle">
            Apne thele ko digital platform pe laye aur business ko naye heights tak pahunchaye
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="benefits-grid">
          {vendorBenefits.map((benefit, index) => (
            <div key={index} className="benefit-card">
              <div className="benefit-icon">{benefit.icon}</div>
              <h3>{benefit.title}</h3>
              <p>{benefit.description}</p>
              <span className="highlight">{benefit.highlight}</span>
            </div>
          ))}
        </div>

        {/* Success Stories */}
        <div className="success-stories">
          <h3 className="subsection-title">Success Stories</h3>
          <div className="stories-grid">
            {successStories.map((story, index) => (
              <div key={index} className="story-card">
                <div className="story-header">
                  <img src={story.image} alt={story.name} className="story-avatar" />
                  <div className="story-info">
                    <h4>{story.name}</h4>
                    <p className="business-name">{story.business}</p>
                    <div className="location">
                      <MapPin size={14} />
                      <span>{story.location}</span>
                    </div>
                  </div>
                  <div className="growth-badge">
                    +{story.growth}
                  </div>
                </div>
                <div className="story-content">
                  <p>"{story.story}"</p>
                  <div className="story-rating">
                    <Star size={16} fill="currentColor" />
                    <span>{story.rating}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Features List */}
        <div className="features-section">
          <h3 className="subsection-title">Platform Features</h3>
          <div className="features-grid">
            {features.map((feature, index) => (
              <div key={index} className="feature-item">
                <div className="feature-check">✓</div>
                <span>{feature}</span>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="vendor-cta">
          <div className="cta-content">
            <h3>Ready to grow your business?</h3>
            <p>Join thousands of successful vendors on Bhookad platform</p>
            <div className="cta-buttons">
              <Link to="/register" className="btn-primary">
                Register as Vendor
              </Link>
              <Link to="/login" className="btn-secondary">
                Already have account?
              </Link>
            </div>
          </div>
          <div className="cta-stats">
            <div className="stat">
              <span className="stat-number">5000+</span>
              <span className="stat-label">Active Vendors</span>
            </div>
            <div className="stat">
              <span className="stat-number">₹2L+</span>
              <span className="stat-label">Avg Monthly Revenue</span>
            </div>
            <div className="stat">
              <span className="stat-number">50K+</span>
              <span className="stat-label">Daily Customers</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default VendorsSection
