import React, { useState, useEffect } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { 
  Video, 
  Users, 
  Eye, 
  TrendingUp, 
  MapPin, 
  Search,
  Camera,
  Edit,
  Settings,
  LogOut,
  Star,
  Heart
} from 'lucide-react'
import './Dashboard.css'

const VloggerDashboard = () => {
  const { user, userProfile, signOut } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')
  const [stats, setStats] = useState({
    totalVideos: 24,
    totalViews: 15420,
    subscribers: 2850,
    monthlyGrowth: 18
  })

  const [nearbyVendors] = useState([
    {
      id: 1,
      name: "<PERSON>",
      cuisine: "North Indian",
      rating: 4.8,
      distance: "0.5 km",
      specialties: ["Butter Chicken", "Naan"]
    },
    {
      id: 2,
      name: "Mumbai Chaat Corner",
      cuisine: "Street Food",
      rating: 4.6,
      distance: "1.2 km",
      specialties: ["Pani Puri", "Bhel Puri"]
    },
    {
      id: 3,
      name: "Delhi Paratha Wala",
      cuisine: "North Indian",
      rating: 4.7,
      distance: "0.8 km",
      specialties: ["Aloo Paratha", "Lassi"]
    }
  ])

  const handleSignOut = async () => {
    await signOut()
  }

  const renderOverview = () => (
    <div className="dashboard-content">
      <div className="welcome-section">
        <h1>Welcome back, {userProfile?.full_name || 'Vlogger'}! 📹</h1>
        <p>Ready to discover and create amazing food content?</p>
      </div>

      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">
            <Video size={24} />
          </div>
          <div className="stat-info">
            <h3>{stats.totalVideos}</h3>
            <p>Videos Created</p>
            <span className="stat-change positive">+3 this month</span>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <Eye size={24} />
          </div>
          <div className="stat-info">
            <h3>{stats.totalViews.toLocaleString()}</h3>
            <p>Total Views</p>
            <span className="stat-change positive">+{stats.monthlyGrowth}% this month</span>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <Users size={24} />
          </div>
          <div className="stat-info">
            <h3>{stats.subscribers.toLocaleString()}</h3>
            <p>Subscribers</p>
            <span className="stat-change positive">+125 this week</span>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <Heart size={24} />
          </div>
          <div className="stat-info">
            <h3>892</h3>
            <p>Total Likes</p>
            <span className="stat-change positive">+45 today</span>
          </div>
        </div>
      </div>

      <div className="content-sections">
        <div className="section">
          <h2>Nearby Vendors to Explore</h2>
          <div className="vendor-list">
            {nearbyVendors.map(vendor => (
              <div key={vendor.id} className="vendor-card-small">
                <div className="vendor-info">
                  <h4>{vendor.name}</h4>
                  <p className="vendor-cuisine">{vendor.cuisine}</p>
                  <p className="vendor-specialties">{vendor.specialties.join(', ')}</p>
                  <div className="vendor-meta">
                    <span className="rating">
                      <Star size={14} />
                      {vendor.rating}
                    </span>
                    <span className="distance">
                      <MapPin size={14} />
                      {vendor.distance}
                    </span>
                  </div>
                </div>
                <div className="vendor-actions">
                  <button className="btn-small primary">Visit</button>
                  <button className="btn-small secondary">Save</button>
                </div>
              </div>
            ))}
          </div>
          <button className="btn-secondary">Explore More Vendors</button>
        </div>

        <div className="section">
          <h2>Content Ideas</h2>
          <div className="idea-list">
            <div className="idea-item">
              <div className="idea-icon">🌮</div>
              <div className="idea-content">
                <h4>Street Taco Challenge</h4>
                <p>Try 5 different taco vendors in one day</p>
              </div>
            </div>
            <div className="idea-item">
              <div className="idea-icon">🍜</div>
              <div className="idea-content">
                <h4>Ramen vs Street Noodles</h4>
                <p>Compare traditional ramen with local street noodles</p>
              </div>
            </div>
            <div className="idea-item">
              <div className="idea-icon">🥘</div>
              <div className="idea-content">
                <h4>Regional Curry Tour</h4>
                <p>Explore different regional curry styles</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderProfile = () => (
    <div className="dashboard-content">
      <div className="profile-section">
        <h1>Creator Profile</h1>
        <div className="profile-form">
          <div className="form-row">
            <div className="form-group">
              <label>Channel Name</label>
              <input 
                type="text" 
                value={userProfile?.full_name || ''} 
                placeholder="Enter your channel name"
              />
            </div>
            <div className="form-group">
              <label>Content Category</label>
              <select value={userProfile?.content_category || ''}>
                <option value="">Select category</option>
                <option value="food-reviews">Food Reviews</option>
                <option value="street-food">Street Food</option>
                <option value="cooking">Cooking</option>
                <option value="food-travel">Food Travel</option>
              </select>
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label>YouTube Channel</label>
              <input 
                type="url" 
                value={userProfile?.youtube_channel || ''} 
                placeholder="https://youtube.com/@yourchannel"
              />
            </div>
            <div className="form-group">
              <label>Instagram Handle</label>
              <input 
                type="text" 
                value={userProfile?.instagram_handle || ''} 
                placeholder="@yourusername"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label>Subscriber Count</label>
              <input 
                type="number" 
                value={userProfile?.subscriber_count || ''} 
                placeholder="Enter your subscriber count"
              />
            </div>
            <div className="form-group">
              <label>City</label>
              <input 
                type="text" 
                value={userProfile?.location_city || ''} 
                placeholder="Enter your city"
              />
            </div>
          </div>

          <div className="form-group">
            <label>Bio</label>
            <textarea 
              value={userProfile?.bio || ''} 
              placeholder="Tell vendors and viewers about yourself..."
              rows="4"
            />
          </div>

          <button className="btn-primary">Save Changes</button>
        </div>
      </div>
    </div>
  )

  const renderDiscover = () => (
    <div className="dashboard-content">
      <div className="discover-section">
        <h1>Discover Vendors</h1>
        <div className="search-filters">
          <div className="search-bar">
            <Search size={20} />
            <input type="text" placeholder="Search vendors, cuisine, location..." />
          </div>
          <div className="filter-buttons">
            <button className="filter-btn active">All</button>
            <button className="filter-btn">North Indian</button>
            <button className="filter-btn">Street Food</button>
            <button className="filter-btn">South Indian</button>
            <button className="filter-btn">Chinese</button>
          </div>
        </div>
        
        <div className="vendor-grid">
          {nearbyVendors.map(vendor => (
            <div key={vendor.id} className="vendor-card">
              <div className="vendor-image">
                <span className="food-emoji">🍛</span>
              </div>
              <div className="vendor-info">
                <h3>{vendor.name}</h3>
                <p className="vendor-cuisine">{vendor.cuisine}</p>
                <p className="vendor-specialties">{vendor.specialties.join(', ')}</p>
                <div className="vendor-meta">
                  <span className="rating">
                    <Star size={16} />
                    {vendor.rating}
                  </span>
                  <span className="distance">
                    <MapPin size={16} />
                    {vendor.distance}
                  </span>
                </div>
                <div className="vendor-actions">
                  <button className="btn-primary">Visit Now</button>
                  <button className="btn-secondary">Save for Later</button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  return (
    <div className="dashboard-container">
      <div className="dashboard-sidebar">
        <div className="sidebar-header">
          <div className="user-info">
            <div className="user-avatar">
              {userProfile?.full_name?.charAt(0) || 'V'}
            </div>
            <div className="user-details">
              <h3>{userProfile?.full_name || 'Vlogger'}</h3>
              <p>{userProfile?.subscriber_count || 0} subscribers</p>
              <span className="user-role">Vlogger</span>
            </div>
          </div>
        </div>

        <nav className="sidebar-nav">
          <button 
            className={`nav-item ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            <Video size={20} />
            Overview
          </button>
          <button 
            className={`nav-item ${activeTab === 'discover' ? 'active' : ''}`}
            onClick={() => setActiveTab('discover')}
          >
            <Search size={20} />
            Discover
          </button>
          <button 
            className={`nav-item ${activeTab === 'profile' ? 'active' : ''}`}
            onClick={() => setActiveTab('profile')}
          >
            <Edit size={20} />
            Profile
          </button>
          <button 
            className={`nav-item ${activeTab === 'analytics' ? 'active' : ''}`}
            onClick={() => setActiveTab('analytics')}
          >
            <TrendingUp size={20} />
            Analytics
          </button>
          <button 
            className={`nav-item ${activeTab === 'settings' ? 'active' : ''}`}
            onClick={() => setActiveTab('settings')}
          >
            <Settings size={20} />
            Settings
          </button>
        </nav>

        <div className="sidebar-footer">
          <button className="nav-item logout" onClick={handleSignOut}>
            <LogOut size={20} />
            Sign Out
          </button>
        </div>
      </div>

      <div className="dashboard-main">
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'discover' && renderDiscover()}
        {activeTab === 'profile' && renderProfile()}
        {activeTab === 'analytics' && (
          <div className="dashboard-content">
            <h1>Analytics</h1>
            <p>Analytics features coming soon...</p>
          </div>
        )}
        {activeTab === 'settings' && (
          <div className="dashboard-content">
            <h1>Settings</h1>
            <p>Settings panel coming soon...</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default VloggerDashboard
