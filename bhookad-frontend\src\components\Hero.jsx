import React from 'react'
import { Star, Users, MapPin, TrendingUp } from 'lucide-react'
import './Hero.css'

const Hero = () => {
  return (
    <section className="hero" id="home">
      <div className="container">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              Connecting you<br />
              to <span className="highlight">trending plates!</span>
            </h1>
            <p className="hero-description">
              Discover the best street food vendors in your area. From spicy chaat to delicious momos, 
              find authentic flavors that will make your taste buds dance!
            </p>
            <div className="hero-buttons">
              <button className="btn-primary">Explore Now</button>
              <button className="btn-secondary">Learn More</button>
            </div>
          </div>
          
          <div className="hero-image">
            <div className="food-card">
              <div className="food-image-placeholder">
                🍛
              </div>
              <h3>Trending Biryani</h3>
              <div className="rating">
                <Star className="star-filled" size={16} />
                <Star className="star-filled" size={16} />
                <Star className="star-filled" size={16} />
                <Star className="star-filled" size={16} />
                <Star className="star-filled" size={16} />
                <span>4.8</span>
              </div>
            </div>
          </div>
        </div>

        <div className="stats">
          <div className="stat-item">
            <div className="stat-icon">
              <Users size={24} />
            </div>
            <div className="stat-number">200+</div>
            <div className="stat-label">Happy Customers</div>
          </div>
          
          <div className="stat-item">
            <div className="stat-icon">
              <MapPin size={24} />
            </div>
            <div className="stat-number">100+</div>
            <div className="stat-label">Vendors</div>
          </div>
          
          <div className="stat-item">
            <div className="stat-icon">
              <Star size={24} />
            </div>
            <div className="stat-number">250+</div>
            <div className="stat-label">Reviews</div>
          </div>
          
          <div className="stat-item">
            <div className="stat-icon">
              <TrendingUp size={24} />
            </div>
            <div className="stat-number">500+</div>
            <div className="stat-label">Orders</div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
