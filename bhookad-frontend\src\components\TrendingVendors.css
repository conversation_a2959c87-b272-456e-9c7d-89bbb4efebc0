.trending-vendors {
  background: white;
}

.vendors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.vendor-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.vendor-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.vendor-image {
  height: 200px;
  background: linear-gradient(135deg, #fff3e0, #ffe0b2);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.food-emoji {
  font-size: 4rem;
}

.vendor-info {
  padding: 1.5rem;
}

.vendor-name {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #333;
}

.vendor-cuisine {
  color: #ff6b35;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.vendor-speciality {
  color: #666;
  margin-bottom: 1rem;
  font-style: italic;
}

.vendor-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.rating, .location, .timing {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #666;
}

.rating .star-filled {
  color: #ffc107;
  fill: #ffc107;
}

.rating span {
  font-weight: 600;
  color: #333;
}

.location svg, .timing svg {
  color: #ff6b35;
}

.vendor-btn {
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
}

@media (max-width: 768px) {
  .vendors-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .vendor-card {
    margin: 0 1rem;
  }
}
