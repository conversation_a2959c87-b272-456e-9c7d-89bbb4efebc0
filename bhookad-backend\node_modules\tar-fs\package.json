{"name": "tar-fs", "version": "3.1.0", "description": "filesystem bindings for tar-stream", "dependencies": {"pump": "^3.0.0", "tar-stream": "^3.1.5"}, "optionalDependencies": {"bare-fs": "^4.0.1", "bare-path": "^3.0.0"}, "imports": {"fs": {"bare": "bare-fs", "default": "fs"}, "path": {"bare": "bare-path", "default": "path"}}, "files": ["index.js"], "standard": {"ignore": ["test/fixtures/**"]}, "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "devDependencies": {"brittle": "^3.1.3", "rimraf": "^2.6.3", "standard": "^17.0.1"}, "scripts": {"test": "standard && brittle test/index.js"}, "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "homepage": "https://github.com/mafintosh/tar-fs", "main": "index.js", "directories": {"test": "test"}, "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/mafintosh/tar-fs.git"}}