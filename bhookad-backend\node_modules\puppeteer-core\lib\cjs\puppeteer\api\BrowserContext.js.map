{"version": 3, "file": "BrowserContext.js", "sourceRoot": "", "sources": ["../../../../src/api/BrowserContext.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,4DAKwC;AAExC,+DAAuE;AACvE,+CAK2B;AAC3B,yDAAwE;AACxE,+CAAuC;AAyCvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqCG;AAEH,MAAsB,cAAe,SAAQ,8BAAkC;IAC7E;;OAEG;IACH;QACE,KAAK,EAAE,CAAC;IACV,CAAC;IAQD;;OAEG;IACH,oBAAoB,CAAS;IAC7B,0BAA0B,GAAG,CAAC,CAAC;IAE/B;;OAEG;IACH,eAAe;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,gBAAK,EAAE,CAAC;QACvD,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAClC,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE;YACxB,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,0BAA0B,KAAK,CAAC,EAAE,CAAC;gBAC1C,gEAAgE;gBAChE,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;YACxC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,2BAA2B;QAGzB,OAAO,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE,CAAC;IAC9C,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,KAAK,CAAC,aAAa,CACjB,SAAoD,EACpD,UAAgC,EAAE;QAElC,MAAM,EAAC,OAAO,EAAE,EAAE,GAAG,KAAK,EAAC,GAAG,OAAO,CAAC;QACtC,OAAO,MAAM,IAAA,wBAAc,EACzB,IAAA,eAAK,EACH,IAAA,0BAAgB,EAAC,IAAI,0DAAoC,EACzD,IAAA,0BAAgB,EAAC,IAAI,0DAAoC,EACzD,IAAA,cAAI,EAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CACrB,CAAC,IAAI,CAAC,IAAA,qBAAW,EAAC,SAAS,CAAC,EAAE,IAAA,kBAAQ,EAAC,IAAA,iBAAO,EAAC,EAAE,CAAC,CAAC,CAAC,CACtD,CAAC;IACJ,CAAC;IAmFD;;;OAGG;IACH,KAAK,CAAC,YAAY,CAAC,GAAG,OAAiB;QACrC,OAAO,MAAM,IAAI,CAAC,SAAS,CACzB,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACtB,OAAO;gBACL,GAAG,MAAM;gBACT,OAAO,EAAE,CAAC;aACX,CAAC;QACJ,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,IAAI,EAAE;QACJ,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,gBAAgB;IACP,CAAC,6BAAa,CAAC;QACtB,OAAO,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,oBAAU,CAAC,CAAC;IAC7C,CAAC;IAED,gBAAgB;IAChB,CAAC,kCAAkB,CAAC;QAClB,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;CACF;AAjMD,wCAiMC"}