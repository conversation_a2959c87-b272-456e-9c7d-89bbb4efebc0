/* Dashboard Layout */
.dashboard-container {
  display: flex;
  min-height: 100vh;
  background: #f8f9fa;
}

.dashboard-sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e1e5e9;
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
}

.dashboard-main {
  flex: 1;
  margin-left: 280px;
  padding: 0;
  overflow-y: auto;
}

/* Sidebar Header */
.sidebar-header {
  padding: 30px 20px;
  border-bottom: 1px solid #e1e5e9;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 1.2rem;
}

.user-details h3 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.user-details p {
  margin: 2px 0;
  color: #666;
  font-size: 0.9rem;
}

.user-role {
  background: #ff6b35;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Sidebar Navigation */
.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.nav-item {
  width: 100%;
  padding: 12px 20px;
  border: none;
  background: none;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 12px;
  color: #666;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-item:hover {
  background: #f8f9fa;
  color: #333;
}

.nav-item.active {
  background: #fff5f2;
  color: #ff6b35;
  border-right: 3px solid #ff6b35;
}

.nav-item.logout {
  color: #dc3545;
  margin-top: auto;
}

.nav-item.logout:hover {
  background: #fff5f5;
}

.sidebar-footer {
  padding: 20px 0;
  border-top: 1px solid #e1e5e9;
}

/* Dashboard Content */
.dashboard-content {
  padding: 30px;
  max-width: 1200px;
}

.welcome-section {
  margin-bottom: 30px;
}

.welcome-section h1 {
  color: #333;
  font-size: 2rem;
  margin-bottom: 8px;
  font-weight: 700;
}

.welcome-section p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-info h3 {
  margin: 0;
  color: #333;
  font-size: 1.8rem;
  font-weight: 700;
}

.stat-info p {
  margin: 5px 0;
  color: #666;
  font-size: 0.9rem;
}

.stat-change {
  font-size: 0.8rem;
  font-weight: 500;
}

.stat-change.positive {
  color: #28a745;
}

.stat-change.negative {
  color: #dc3545;
}

/* Quick Actions */
.quick-actions {
  margin-bottom: 40px;
}

.quick-actions h2 {
  color: #333;
  font-size: 1.3rem;
  margin-bottom: 20px;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 10px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: #ff6b35;
  color: white;
}

.action-btn.primary:hover {
  background: #e55a2b;
}

.action-btn.secondary {
  background: white;
  color: #666;
  border: 2px solid #e1e5e9;
}

.action-btn.secondary:hover {
  border-color: #ff6b35;
  color: #ff6b35;
}

/* Recent Activity */
.recent-activity h2 {
  color: #333;
  font-size: 1.3rem;
  margin-bottom: 20px;
  font-weight: 600;
}

.activity-list {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.activity-content p {
  margin: 0;
  color: #333;
  font-size: 0.9rem;
}

.activity-time {
  color: #999;
  font-size: 0.8rem;
}

/* Profile Form */
.profile-section h1 {
  color: #333;
  font-size: 1.8rem;
  margin-bottom: 30px;
  font-weight: 700;
}

.profile-form {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 500;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 0.95rem;
  transition: border-color 0.3s ease;
  background: #f8f9fa;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #ff6b35;
  background: white;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-sidebar {
    width: 250px;
  }
  
  .dashboard-main {
    margin-left: 250px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    flex-direction: column;
  }
  
  .dashboard-sidebar {
    width: 100%;
    height: auto;
    position: relative;
  }
  
  .dashboard-main {
    margin-left: 0;
  }
  
  .dashboard-content {
    padding: 20px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Vendor List Styles */
.vendor-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.vendor-card-small {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.vendor-card-small .vendor-info h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.vendor-cuisine {
  color: #ff6b35;
  font-size: 0.85rem;
  font-weight: 500;
  margin: 0 0 5px 0;
}

.vendor-specialties {
  color: #666;
  font-size: 0.8rem;
  margin: 0 0 8px 0;
}

.vendor-meta {
  display: flex;
  gap: 15px;
  align-items: center;
}

.vendor-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  color: #666;
}

.rating {
  color: #ffc107 !important;
}

.vendor-actions {
  display: flex;
  gap: 10px;
}

.btn-small {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-small.primary {
  background: #ff6b35;
  color: white;
}

.btn-small.secondary {
  background: white;
  color: #666;
  border: 1px solid #e1e5e9;
}

/* Content Sections */
.content-sections {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
}

.section {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section h2 {
  color: #333;
  font-size: 1.2rem;
  margin-bottom: 20px;
  font-weight: 600;
}

/* Idea List */
.idea-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.idea-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
  transition: background 0.3s ease;
}

.idea-item:hover {
  background: #fff5f2;
}

.idea-icon {
  font-size: 1.5rem;
}

.idea-content h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 0.95rem;
  font-weight: 600;
}

.idea-content p {
  margin: 0;
  color: #666;
  font-size: 0.85rem;
}

/* Discover Section */
.discover-section h1 {
  color: #333;
  font-size: 1.8rem;
  margin-bottom: 30px;
  font-weight: 700;
}

.search-filters {
  margin-bottom: 30px;
}

.search-bar {
  position: relative;
  margin-bottom: 20px;
}

.search-bar svg {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.search-bar input {
  width: 100%;
  padding: 15px 15px 15px 45px;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  font-size: 1rem;
  background: white;
}

.filter-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 8px 16px;
  border: 2px solid #e1e5e9;
  background: white;
  border-radius: 20px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn.active,
.filter-btn:hover {
  border-color: #ff6b35;
  background: #ff6b35;
  color: white;
}

.vendor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.vendor-card {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.vendor-card:hover {
  transform: translateY(-2px);
}

.vendor-image {
  text-align: center;
  margin-bottom: 15px;
}

.food-emoji {
  font-size: 3rem;
}

.vendor-card .vendor-info h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.vendor-card .vendor-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.vendor-card .vendor-actions button {
  flex: 1;
  padding: 10px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}
