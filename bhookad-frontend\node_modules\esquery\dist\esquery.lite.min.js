!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("estraverse")):"function"==typeof define&&define.amd?define(["estraverse"],e):(t=t||self).esquery=e(t.estraverse)}(this,(function(t){"use strict";function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,u,o,a,s=[],c=!0,i=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){i=!0,u=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(i)throw u}}return s}}(t,e)||u(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(t){return function(t){if(Array.isArray(t))return o(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||u(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,e){if(t){if("string"==typeof t)return o(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(t,e):void 0}}function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}t=t&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t;"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;var a=function(t,e){return t(e={exports:{}},e.exports),e.exports}((function(t){t.exports&&(t.exports=function(){function t(e,r,n,u){this.message=e,this.expected=r,this.found=n,this.location=u,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,t)}return function(t,e){function r(){this.constructor=t}r.prototype=e.prototype,t.prototype=new r}(t,Error),t.buildMessage=function(t,e){var r={literal:function(t){return'"'+u(t.text)+'"'},class:function(t){var e,r="";for(e=0;e<t.parts.length;e++)r+=t.parts[e]instanceof Array?o(t.parts[e][0])+"-"+o(t.parts[e][1]):o(t.parts[e]);return"["+(t.inverted?"^":"")+r+"]"},any:function(t){return"any character"},end:function(t){return"end of input"},other:function(t){return t.description}};function n(t){return t.charCodeAt(0).toString(16).toUpperCase()}function u(t){return t.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}function o(t){return t.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}return"Expected "+function(t){var e,n,u,o=new Array(t.length);for(e=0;e<t.length;e++)o[e]=(u=t[e],r[u.type](u));if(o.sort(),o.length>0){for(e=1,n=1;e<o.length;e++)o[e-1]!==o[e]&&(o[n]=o[e],n++);o.length=n}switch(o.length){case 1:return o[0];case 2:return o[0]+" or "+o[1];default:return o.slice(0,-1).join(", ")+", or "+o[o.length-1]}}(t)+" but "+function(t){return t?'"'+u(t)+'"':"end of input"}(e)+" found."},{SyntaxError:t,parse:function(e,r){r=void 0!==r?r:{};var n,u,o,a,s={},c={start:dt},i=dt,l=ft(" ",!1),f=/^[^ [\],():#!=><~+.]/,h=ht([" ","[","]",",","(",")",":","#","!","=",">","<","~","+","."],!0,!1),p=ft(">",!1),v=ft("~",!1),y=ft("+",!1),d=ft(",",!1),A=function(t,e){return[t].concat(e.map((function(t){return t[3]})))},x=ft("!",!1),g=ft("*",!1),m=ft("#",!1),P=ft("[",!1),b=ft("]",!1),C=/^[><!]/,w=ht([">","<","!"],!1,!1),j=ft("=",!1),E=function(t){return(t||"")+"="},S=/^[><]/,k=ht([">","<"],!1,!1),I=ft(".",!1),T=function(t,e,r){return{type:"attribute",name:t,operator:e,value:r}},F=ft('"',!1),K=/^[^\\"]/,O=ht(["\\",'"'],!0,!1),D=ft("\\",!1),L={type:"any"},R=function(t,e){return t+e},M=function(t){return{type:"literal",value:(e=t.join(""),e.replace(/\\(.)/g,(function(t,e){switch(e){case"b":return"\b";case"f":return"\f";case"n":return"\n";case"r":return"\r";case"t":return"\t";case"v":return"\v";default:return e}})))};var e},U=ft("'",!1),_=/^[^\\']/,q=ht(["\\","'"],!0,!1),G=/^[0-9]/,H=ht([["0","9"]],!1,!1),N=ft("type(",!1),V=/^[^ )]/,W=ht([" ",")"],!0,!1),$=ft(")",!1),z=/^[imsu]/,B=ht(["i","m","s","u"],!1,!1),J=ft("/",!1),Q=/^[^\/]/,X=ht(["/"],!0,!1),Y=ft(":not(",!1),Z=ft(":matches(",!1),tt=ft(":has(",!1),et=ft(":first-child",!1),rt=ft(":last-child",!1),nt=ft(":nth-child(",!1),ut=ft(":nth-last-child(",!1),ot=ft(":",!1),at=0,st=[{line:1,column:1}],ct=0,it=[],lt={};if("startRule"in r){if(!(r.startRule in c))throw new Error("Can't start parsing from rule \""+r.startRule+'".');i=c[r.startRule]}function ft(t,e){return{type:"literal",text:t,ignoreCase:e}}function ht(t,e,r){return{type:"class",parts:t,inverted:e,ignoreCase:r}}function pt(t){var r,n=st[t];if(n)return n;for(r=t-1;!st[r];)r--;for(n={line:(n=st[r]).line,column:n.column};r<t;)10===e.charCodeAt(r)?(n.line++,n.column=1):n.column++,r++;return st[t]=n,n}function vt(t,e){var r=pt(t),n=pt(e);return{start:{offset:t,line:r.line,column:r.column},end:{offset:e,line:n.line,column:n.column}}}function yt(t){at<ct||(at>ct&&(ct=at,it=[]),it.push(t))}function dt(){var t,e,r,n,u=32*at+0,o=lt[u];return o?(at=o.nextPos,o.result):(t=at,(e=At())!==s&&(r=mt())!==s&&At()!==s?t=e=1===(n=r).length?n[0]:{type:"matches",selectors:n}:(at=t,t=s),t===s&&(t=at,(e=At())!==s&&(e=void 0),t=e),lt[u]={nextPos:at,result:t},t)}function At(){var t,r,n=32*at+1,u=lt[n];if(u)return at=u.nextPos,u.result;for(t=[],32===e.charCodeAt(at)?(r=" ",at++):(r=s,yt(l));r!==s;)t.push(r),32===e.charCodeAt(at)?(r=" ",at++):(r=s,yt(l));return lt[n]={nextPos:at,result:t},t}function xt(){var t,r,n,u=32*at+2,o=lt[u];if(o)return at=o.nextPos,o.result;if(r=[],f.test(e.charAt(at))?(n=e.charAt(at),at++):(n=s,yt(h)),n!==s)for(;n!==s;)r.push(n),f.test(e.charAt(at))?(n=e.charAt(at),at++):(n=s,yt(h));else r=s;return r!==s&&(r=r.join("")),t=r,lt[u]={nextPos:at,result:t},t}function gt(){var t,r,n,u=32*at+3,o=lt[u];return o?(at=o.nextPos,o.result):(t=at,(r=At())!==s?(62===e.charCodeAt(at)?(n=">",at++):(n=s,yt(p)),n!==s&&At()!==s?t=r="child":(at=t,t=s)):(at=t,t=s),t===s&&(t=at,(r=At())!==s?(126===e.charCodeAt(at)?(n="~",at++):(n=s,yt(v)),n!==s&&At()!==s?t=r="sibling":(at=t,t=s)):(at=t,t=s),t===s&&(t=at,(r=At())!==s?(43===e.charCodeAt(at)?(n="+",at++):(n=s,yt(y)),n!==s&&At()!==s?t=r="adjacent":(at=t,t=s)):(at=t,t=s),t===s&&(t=at,32===e.charCodeAt(at)?(r=" ",at++):(r=s,yt(l)),r!==s&&(n=At())!==s?t=r="descendant":(at=t,t=s)))),lt[u]={nextPos:at,result:t},t)}function mt(){var t,r,n,u,o,a,c,i,l=32*at+5,f=lt[l];if(f)return at=f.nextPos,f.result;if(t=at,(r=bt())!==s){for(n=[],u=at,(o=At())!==s?(44===e.charCodeAt(at)?(a=",",at++):(a=s,yt(d)),a!==s&&(c=At())!==s&&(i=bt())!==s?u=o=[o,a,c,i]:(at=u,u=s)):(at=u,u=s);u!==s;)n.push(u),u=at,(o=At())!==s?(44===e.charCodeAt(at)?(a=",",at++):(a=s,yt(d)),a!==s&&(c=At())!==s&&(i=bt())!==s?u=o=[o,a,c,i]:(at=u,u=s)):(at=u,u=s);n!==s?t=r=A(r,n):(at=t,t=s)}else at=t,t=s;return lt[l]={nextPos:at,result:t},t}function Pt(){var t,e,r,n,u,o=32*at+6,a=lt[o];return a?(at=a.nextPos,a.result):(t=at,(e=gt())===s&&(e=null),e!==s&&(r=bt())!==s?(u=r,t=e=(n=e)?{type:n,left:{type:"exactNode"},right:u}:u):(at=t,t=s),lt[o]={nextPos:at,result:t},t)}function bt(){var t,e,r,n,u,o,a,c=32*at+7,i=lt[c];if(i)return at=i.nextPos,i.result;if(t=at,(e=Ct())!==s){for(r=[],n=at,(u=gt())!==s&&(o=Ct())!==s?n=u=[u,o]:(at=n,n=s);n!==s;)r.push(n),n=at,(u=gt())!==s&&(o=Ct())!==s?n=u=[u,o]:(at=n,n=s);r!==s?(a=e,t=e=r.reduce((function(t,e){return{type:e[0],left:t,right:e[1]}}),a)):(at=t,t=s)}else at=t,t=s;return lt[c]={nextPos:at,result:t},t}function Ct(){var t,r,n,u,o,a,c,i=32*at+8,l=lt[i];if(l)return at=l.nextPos,l.result;if(t=at,33===e.charCodeAt(at)?(r="!",at++):(r=s,yt(x)),r===s&&(r=null),r!==s){if(n=[],(u=wt())!==s)for(;u!==s;)n.push(u),u=wt();else n=s;n!==s?(o=r,c=1===(a=n).length?a[0]:{type:"compound",selectors:a},o&&(c.subject=!0),t=r=c):(at=t,t=s)}else at=t,t=s;return lt[i]={nextPos:at,result:t},t}function wt(){var t,r=32*at+9,n=lt[r];return n?(at=n.nextPos,n.result):((t=function(){var t,r,n=32*at+10,u=lt[n];return u?(at=u.nextPos,u.result):(42===e.charCodeAt(at)?(r="*",at++):(r=s,yt(g)),r!==s&&(r={type:"wildcard",value:r}),t=r,lt[n]={nextPos:at,result:t},t)}())===s&&(t=function(){var t,r,n,u=32*at+11,o=lt[u];return o?(at=o.nextPos,o.result):(t=at,35===e.charCodeAt(at)?(r="#",at++):(r=s,yt(m)),r===s&&(r=null),r!==s&&(n=xt())!==s?t=r={type:"identifier",value:n}:(at=t,t=s),lt[u]={nextPos:at,result:t},t)}())===s&&(t=function(){var t,r,n,u,o=32*at+12,a=lt[o];return a?(at=a.nextPos,a.result):(t=at,91===e.charCodeAt(at)?(r="[",at++):(r=s,yt(P)),r!==s&&At()!==s&&(n=function(){var t,r,n,u,o=32*at+16,a=lt[o];return a?(at=a.nextPos,a.result):(t=at,(r=jt())!==s&&At()!==s&&(n=function(){var t,r,n,u=32*at+14,o=lt[u];return o?(at=o.nextPos,o.result):(t=at,33===e.charCodeAt(at)?(r="!",at++):(r=s,yt(x)),r===s&&(r=null),r!==s?(61===e.charCodeAt(at)?(n="=",at++):(n=s,yt(j)),n!==s?(r=E(r),t=r):(at=t,t=s)):(at=t,t=s),lt[u]={nextPos:at,result:t},t)}())!==s&&At()!==s?((u=function(){var t,r,n,u,o,a=32*at+20,c=lt[a];if(c)return at=c.nextPos,c.result;if(t=at,"type("===e.substr(at,5)?(r="type(",at+=5):(r=s,yt(N)),r!==s)if(At()!==s){if(n=[],V.test(e.charAt(at))?(u=e.charAt(at),at++):(u=s,yt(W)),u!==s)for(;u!==s;)n.push(u),V.test(e.charAt(at))?(u=e.charAt(at),at++):(u=s,yt(W));else n=s;n!==s&&(u=At())!==s?(41===e.charCodeAt(at)?(o=")",at++):(o=s,yt($)),o!==s?(r={type:"type",value:n.join("")},t=r):(at=t,t=s)):(at=t,t=s)}else at=t,t=s;else at=t,t=s;return lt[a]={nextPos:at,result:t},t}())===s&&(u=function(){var t,r,n,u,o,a,c=32*at+22,i=lt[c];if(i)return at=i.nextPos,i.result;if(t=at,47===e.charCodeAt(at)?(r="/",at++):(r=s,yt(J)),r!==s){if(n=[],Q.test(e.charAt(at))?(u=e.charAt(at),at++):(u=s,yt(X)),u!==s)for(;u!==s;)n.push(u),Q.test(e.charAt(at))?(u=e.charAt(at),at++):(u=s,yt(X));else n=s;n!==s?(47===e.charCodeAt(at)?(u="/",at++):(u=s,yt(J)),u!==s?((o=function(){var t,r,n=32*at+21,u=lt[n];if(u)return at=u.nextPos,u.result;if(t=[],z.test(e.charAt(at))?(r=e.charAt(at),at++):(r=s,yt(B)),r!==s)for(;r!==s;)t.push(r),z.test(e.charAt(at))?(r=e.charAt(at),at++):(r=s,yt(B));else t=s;return lt[n]={nextPos:at,result:t},t}())===s&&(o=null),o!==s?(a=o,r={type:"regexp",value:new RegExp(n.join(""),a?a.join(""):"")},t=r):(at=t,t=s)):(at=t,t=s)):(at=t,t=s)}else at=t,t=s;return lt[c]={nextPos:at,result:t},t}()),u!==s?(r=T(r,n,u),t=r):(at=t,t=s)):(at=t,t=s),t===s&&(t=at,(r=jt())!==s&&At()!==s&&(n=function(){var t,r,n,u=32*at+13,o=lt[u];return o?(at=o.nextPos,o.result):(t=at,C.test(e.charAt(at))?(r=e.charAt(at),at++):(r=s,yt(w)),r===s&&(r=null),r!==s?(61===e.charCodeAt(at)?(n="=",at++):(n=s,yt(j)),n!==s?(r=E(r),t=r):(at=t,t=s)):(at=t,t=s),t===s&&(S.test(e.charAt(at))?(t=e.charAt(at),at++):(t=s,yt(k))),lt[u]={nextPos:at,result:t},t)}())!==s&&At()!==s?((u=function(){var t,r,n,u,o,a,c=32*at+17,i=lt[c];if(i)return at=i.nextPos,i.result;if(t=at,34===e.charCodeAt(at)?(r='"',at++):(r=s,yt(F)),r!==s){for(n=[],K.test(e.charAt(at))?(u=e.charAt(at),at++):(u=s,yt(O)),u===s&&(u=at,92===e.charCodeAt(at)?(o="\\",at++):(o=s,yt(D)),o!==s?(e.length>at?(a=e.charAt(at),at++):(a=s,yt(L)),a!==s?(o=R(o,a),u=o):(at=u,u=s)):(at=u,u=s));u!==s;)n.push(u),K.test(e.charAt(at))?(u=e.charAt(at),at++):(u=s,yt(O)),u===s&&(u=at,92===e.charCodeAt(at)?(o="\\",at++):(o=s,yt(D)),o!==s?(e.length>at?(a=e.charAt(at),at++):(a=s,yt(L)),a!==s?(o=R(o,a),u=o):(at=u,u=s)):(at=u,u=s));n!==s?(34===e.charCodeAt(at)?(u='"',at++):(u=s,yt(F)),u!==s?(r=M(n),t=r):(at=t,t=s)):(at=t,t=s)}else at=t,t=s;if(t===s)if(t=at,39===e.charCodeAt(at)?(r="'",at++):(r=s,yt(U)),r!==s){for(n=[],_.test(e.charAt(at))?(u=e.charAt(at),at++):(u=s,yt(q)),u===s&&(u=at,92===e.charCodeAt(at)?(o="\\",at++):(o=s,yt(D)),o!==s?(e.length>at?(a=e.charAt(at),at++):(a=s,yt(L)),a!==s?(o=R(o,a),u=o):(at=u,u=s)):(at=u,u=s));u!==s;)n.push(u),_.test(e.charAt(at))?(u=e.charAt(at),at++):(u=s,yt(q)),u===s&&(u=at,92===e.charCodeAt(at)?(o="\\",at++):(o=s,yt(D)),o!==s?(e.length>at?(a=e.charAt(at),at++):(a=s,yt(L)),a!==s?(o=R(o,a),u=o):(at=u,u=s)):(at=u,u=s));n!==s?(39===e.charCodeAt(at)?(u="'",at++):(u=s,yt(U)),u!==s?(r=M(n),t=r):(at=t,t=s)):(at=t,t=s)}else at=t,t=s;return lt[c]={nextPos:at,result:t},t}())===s&&(u=function(){var t,r,n,u,o,a,c,i=32*at+18,l=lt[i];if(l)return at=l.nextPos,l.result;for(t=at,r=at,n=[],G.test(e.charAt(at))?(u=e.charAt(at),at++):(u=s,yt(H));u!==s;)n.push(u),G.test(e.charAt(at))?(u=e.charAt(at),at++):(u=s,yt(H));if(n!==s?(46===e.charCodeAt(at)?(u=".",at++):(u=s,yt(I)),u!==s?r=n=[n,u]:(at=r,r=s)):(at=r,r=s),r===s&&(r=null),r!==s){if(n=[],G.test(e.charAt(at))?(u=e.charAt(at),at++):(u=s,yt(H)),u!==s)for(;u!==s;)n.push(u),G.test(e.charAt(at))?(u=e.charAt(at),at++):(u=s,yt(H));else n=s;n!==s?(a=n,c=(o=r)?[].concat.apply([],o).join(""):"",r={type:"literal",value:parseFloat(c+a.join(""))},t=r):(at=t,t=s)}else at=t,t=s;return lt[i]={nextPos:at,result:t},t}())===s&&(u=function(){var t,e,r=32*at+19,n=lt[r];return n?(at=n.nextPos,n.result):((e=xt())!==s&&(e={type:"literal",value:e}),t=e,lt[r]={nextPos:at,result:t},t)}()),u!==s?(r=T(r,n,u),t=r):(at=t,t=s)):(at=t,t=s),t===s&&(t=at,(r=jt())!==s&&(r={type:"attribute",name:r}),t=r)),lt[o]={nextPos:at,result:t},t)}())!==s&&At()!==s?(93===e.charCodeAt(at)?(u="]",at++):(u=s,yt(b)),u!==s?t=r=n:(at=t,t=s)):(at=t,t=s),lt[o]={nextPos:at,result:t},t)}())===s&&(t=function(){var t,r,n,u,o,a,c,i,l=32*at+23,f=lt[l];if(f)return at=f.nextPos,f.result;if(t=at,46===e.charCodeAt(at)?(r=".",at++):(r=s,yt(I)),r!==s)if((n=xt())!==s){for(u=[],o=at,46===e.charCodeAt(at)?(a=".",at++):(a=s,yt(I)),a!==s&&(c=xt())!==s?o=a=[a,c]:(at=o,o=s);o!==s;)u.push(o),o=at,46===e.charCodeAt(at)?(a=".",at++):(a=s,yt(I)),a!==s&&(c=xt())!==s?o=a=[a,c]:(at=o,o=s);u!==s?(i=n,r={type:"field",name:u.reduce((function(t,e){return t+e[0]+e[1]}),i)},t=r):(at=t,t=s)}else at=t,t=s;else at=t,t=s;return lt[l]={nextPos:at,result:t},t}())===s&&(t=function(){var t,r,n,u,o=32*at+24,a=lt[o];return a?(at=a.nextPos,a.result):(t=at,":not("===e.substr(at,5)?(r=":not(",at+=5):(r=s,yt(Y)),r!==s&&At()!==s&&(n=mt())!==s&&At()!==s?(41===e.charCodeAt(at)?(u=")",at++):(u=s,yt($)),u!==s?t=r={type:"not",selectors:n}:(at=t,t=s)):(at=t,t=s),lt[o]={nextPos:at,result:t},t)}())===s&&(t=function(){var t,r,n,u,o=32*at+25,a=lt[o];return a?(at=a.nextPos,a.result):(t=at,":matches("===e.substr(at,9)?(r=":matches(",at+=9):(r=s,yt(Z)),r!==s&&At()!==s&&(n=mt())!==s&&At()!==s?(41===e.charCodeAt(at)?(u=")",at++):(u=s,yt($)),u!==s?t=r={type:"matches",selectors:n}:(at=t,t=s)):(at=t,t=s),lt[o]={nextPos:at,result:t},t)}())===s&&(t=function(){var t,r,n,u,o=32*at+26,a=lt[o];return a?(at=a.nextPos,a.result):(t=at,":has("===e.substr(at,5)?(r=":has(",at+=5):(r=s,yt(tt)),r!==s&&At()!==s&&(n=function(){var t,r,n,u,o,a,c,i,l=32*at+4,f=lt[l];if(f)return at=f.nextPos,f.result;if(t=at,(r=Pt())!==s){for(n=[],u=at,(o=At())!==s?(44===e.charCodeAt(at)?(a=",",at++):(a=s,yt(d)),a!==s&&(c=At())!==s&&(i=Pt())!==s?u=o=[o,a,c,i]:(at=u,u=s)):(at=u,u=s);u!==s;)n.push(u),u=at,(o=At())!==s?(44===e.charCodeAt(at)?(a=",",at++):(a=s,yt(d)),a!==s&&(c=At())!==s&&(i=Pt())!==s?u=o=[o,a,c,i]:(at=u,u=s)):(at=u,u=s);n!==s?t=r=A(r,n):(at=t,t=s)}else at=t,t=s;return lt[l]={nextPos:at,result:t},t}())!==s&&At()!==s?(41===e.charCodeAt(at)?(u=")",at++):(u=s,yt($)),u!==s?t=r={type:"has",selectors:n}:(at=t,t=s)):(at=t,t=s),lt[o]={nextPos:at,result:t},t)}())===s&&(t=function(){var t,r,n=32*at+27,u=lt[n];return u?(at=u.nextPos,u.result):(":first-child"===e.substr(at,12)?(r=":first-child",at+=12):(r=s,yt(et)),r!==s&&(r=Et(1)),t=r,lt[n]={nextPos:at,result:t},t)}())===s&&(t=function(){var t,r,n=32*at+28,u=lt[n];return u?(at=u.nextPos,u.result):(":last-child"===e.substr(at,11)?(r=":last-child",at+=11):(r=s,yt(rt)),r!==s&&(r=St(1)),t=r,lt[n]={nextPos:at,result:t},t)}())===s&&(t=function(){var t,r,n,u,o,a=32*at+29,c=lt[a];if(c)return at=c.nextPos,c.result;if(t=at,":nth-child("===e.substr(at,11)?(r=":nth-child(",at+=11):(r=s,yt(nt)),r!==s)if(At()!==s){if(n=[],G.test(e.charAt(at))?(u=e.charAt(at),at++):(u=s,yt(H)),u!==s)for(;u!==s;)n.push(u),G.test(e.charAt(at))?(u=e.charAt(at),at++):(u=s,yt(H));else n=s;n!==s&&(u=At())!==s?(41===e.charCodeAt(at)?(o=")",at++):(o=s,yt($)),o!==s?(r=Et(parseInt(n.join(""),10)),t=r):(at=t,t=s)):(at=t,t=s)}else at=t,t=s;else at=t,t=s;return lt[a]={nextPos:at,result:t},t}())===s&&(t=function(){var t,r,n,u,o,a=32*at+30,c=lt[a];if(c)return at=c.nextPos,c.result;if(t=at,":nth-last-child("===e.substr(at,16)?(r=":nth-last-child(",at+=16):(r=s,yt(ut)),r!==s)if(At()!==s){if(n=[],G.test(e.charAt(at))?(u=e.charAt(at),at++):(u=s,yt(H)),u!==s)for(;u!==s;)n.push(u),G.test(e.charAt(at))?(u=e.charAt(at),at++):(u=s,yt(H));else n=s;n!==s&&(u=At())!==s?(41===e.charCodeAt(at)?(o=")",at++):(o=s,yt($)),o!==s?(r=St(parseInt(n.join(""),10)),t=r):(at=t,t=s)):(at=t,t=s)}else at=t,t=s;else at=t,t=s;return lt[a]={nextPos:at,result:t},t}())===s&&(t=function(){var t,r,n,u=32*at+31,o=lt[u];return o?(at=o.nextPos,o.result):(t=at,58===e.charCodeAt(at)?(r=":",at++):(r=s,yt(ot)),r!==s&&(n=xt())!==s?t=r={type:"class",name:n}:(at=t,t=s),lt[u]={nextPos:at,result:t},t)}()),lt[r]={nextPos:at,result:t},t)}function jt(){var t,r,n,u,o,a,c,i,l=32*at+15,f=lt[l];if(f)return at=f.nextPos,f.result;if(t=at,(r=xt())!==s){for(n=[],u=at,46===e.charCodeAt(at)?(o=".",at++):(o=s,yt(I)),o!==s&&(a=xt())!==s?u=o=[o,a]:(at=u,u=s);u!==s;)n.push(u),u=at,46===e.charCodeAt(at)?(o=".",at++):(o=s,yt(I)),o!==s&&(a=xt())!==s?u=o=[o,a]:(at=u,u=s);n!==s?(c=r,i=n,t=r=[].concat.apply([c],i).join("")):(at=t,t=s)}else at=t,t=s;return lt[l]={nextPos:at,result:t},t}function Et(t){return{type:"nth-child",index:{type:"literal",value:t}}}function St(t){return{type:"nth-last-child",index:{type:"literal",value:t}}}if((n=i())!==s&&at===e.length)return n;throw n!==s&&at<e.length&&yt({type:"end"}),u=it,o=ct<e.length?e.charAt(ct):null,a=ct<e.length?vt(ct,ct+1):vt(ct,ct),new t(t.buildMessage(u,o),u,o,a)}}}())}));function s(t,e){for(var r=0;r<e.length;++r){if(null==t)return t;t=t[e[r]]}return t}var c="function"==typeof WeakMap?new WeakMap:null;function i(t){if(null==t)return function(){return!0};if(null!=c){var e=c.get(t);return null!=e||(e=l(t),c.set(t,e)),e}return l(t)}function l(r){switch(r.type){case"wildcard":return function(){return!0};case"identifier":var n=r.value.toLowerCase();return function(t,e,r){var u=r&&r.nodeTypeKey||"type";return n===t[u].toLowerCase()};case"exactNode":return function(t,e){return 0===e.length};case"field":var u=r.name.split(".");return function(t,e){return function t(e,r,n,u){for(var o=r,a=u;a<n.length;++a){if(null==o)return!1;var s=o[n[a]];if(Array.isArray(s)){for(var c=0;c<s.length;++c)if(t(e,s[c],n,a+1))return!0;return!1}o=s}return e===o}(t,e[u.length-1],u,0)};case"matches":var o=r.selectors.map(i);return function(t,e,r){for(var n=0;n<o.length;++n)if(o[n](t,e,r))return!0;return!1};case"compound":var a=r.selectors.map(i);return function(t,e,r){for(var n=0;n<a.length;++n)if(!a[n](t,e,r))return!1;return!0};case"not":var c=r.selectors.map(i);return function(t,e,r){for(var n=0;n<c.length;++n)if(c[n](t,e,r))return!1;return!0};case"has":var l=r.selectors.map(i);return function(e,r,n){var u=!1,o=[];return t.traverse(e,{enter:function(t,e){null!=e&&o.unshift(e);for(var r=0;r<l.length;++r)if(l[r](t,o,n))return u=!0,void this.break()},leave:function(){o.shift()},keys:n&&n.visitorKeys,fallback:n&&n.fallback||"iteration"}),u};case"child":var f=i(r.left),h=i(r.right);return function(t,e,r){return!!(e.length>0&&h(t,e,r))&&f(e[0],e.slice(1),r)};case"descendant":var d=i(r.left),A=i(r.right);return function(t,e,r){if(A(t,e,r))for(var n=0,u=e.length;n<u;++n)if(d(e[n],e.slice(n+1),r))return!0;return!1};case"attribute":var x=r.name.split(".");switch(r.operator){case void 0:return function(t){return null!=s(t,x)};case"=":switch(r.value.type){case"regexp":return function(t){var e=s(t,x);return"string"==typeof e&&r.value.value.test(e)};case"literal":var g="".concat(r.value.value);return function(t){return g==="".concat(s(t,x))};case"type":return function(t){return r.value.value===e(s(t,x))}}throw new Error("Unknown selector value type: ".concat(r.value.type));case"!=":switch(r.value.type){case"regexp":return function(t){return!r.value.value.test(s(t,x))};case"literal":var m="".concat(r.value.value);return function(t){return m!=="".concat(s(t,x))};case"type":return function(t){return r.value.value!==e(s(t,x))}}throw new Error("Unknown selector value type: ".concat(r.value.type));case"<=":return function(t){return s(t,x)<=r.value.value};case"<":return function(t){return s(t,x)<r.value.value};case">":return function(t){return s(t,x)>r.value.value};case">=":return function(t){return s(t,x)>=r.value.value}}throw new Error("Unknown operator: ".concat(r.operator));case"sibling":var P=i(r.left),b=i(r.right);return function(t,e,n){return b(t,e,n)&&p(t,P,e,"LEFT_SIDE",n)||r.left.subject&&P(t,e,n)&&p(t,b,e,"RIGHT_SIDE",n)};case"adjacent":var C=i(r.left),w=i(r.right);return function(t,e,n){return w(t,e,n)&&v(t,C,e,"LEFT_SIDE",n)||r.right.subject&&C(t,e,n)&&v(t,w,e,"RIGHT_SIDE",n)};case"nth-child":var j=r.index.value,E=i(r.right);return function(t,e,r){return E(t,e,r)&&y(t,e,j,r)};case"nth-last-child":var S=-r.index.value,k=i(r.right);return function(t,e,r){return k(t,e,r)&&y(t,e,S,r)};case"class":var I=r.name.toLowerCase();return function(t,e,n){if(n&&n.matchClass)return n.matchClass(r.name,t,e);if(n&&n.nodeTypeKey)return!1;switch(I){case"statement":if("Statement"===t.type.slice(-9))return!0;case"declaration":return"Declaration"===t.type.slice(-11);case"pattern":if("Pattern"===t.type.slice(-7))return!0;case"expression":return"Expression"===t.type.slice(-10)||"Literal"===t.type.slice(-7)||"Identifier"===t.type&&(0===e.length||"MetaProperty"!==e[0].type)||"MetaProperty"===t.type;case"function":return"FunctionDeclaration"===t.type||"FunctionExpression"===t.type||"ArrowFunctionExpression"===t.type}throw new Error("Unknown class name: ".concat(r.name))}}throw new Error("Unknown selector type: ".concat(r.type))}function f(e,r){var n=r&&r.nodeTypeKey||"type",u=e[n];return r&&r.visitorKeys&&r.visitorKeys[u]?r.visitorKeys[u]:t.VisitorKeys[u]?t.VisitorKeys[u]:r&&"function"==typeof r.fallback?r.fallback(e):Object.keys(e).filter((function(t){return t!==n}))}function h(t,r){var n=r&&r.nodeTypeKey||"type";return null!==t&&"object"===e(t)&&"string"==typeof t[n]}function p(t,e,n,u,o){var a=r(n,1)[0];if(!a)return!1;for(var s=f(a,o),c=0;c<s.length;++c){var i=a[s[c]];if(Array.isArray(i)){var l=i.indexOf(t);if(l<0)continue;var p=void 0,v=void 0;"LEFT_SIDE"===u?(p=0,v=l):(p=l+1,v=i.length);for(var y=p;y<v;++y)if(h(i[y],o)&&e(i[y],n,o))return!0}}return!1}function v(t,e,n,u,o){var a=r(n,1)[0];if(!a)return!1;for(var s=f(a,o),c=0;c<s.length;++c){var i=a[s[c]];if(Array.isArray(i)){var l=i.indexOf(t);if(l<0)continue;if("LEFT_SIDE"===u&&l>0&&h(i[l-1],o)&&e(i[l-1],n,o))return!0;if("RIGHT_SIDE"===u&&l<i.length-1&&h(i[l+1],o)&&e(i[l+1],n,o))return!0}}return!1}function y(t,e,n,u){if(0===n)return!1;var o=r(e,1)[0];if(!o)return!1;for(var a=f(o,u),s=0;s<a.length;++s){var c=o[a[s]];if(Array.isArray(c)){var i=n<0?c.length+n:n-1;if(i>=0&&i<c.length&&c[i]===t)return!0}}return!1}function d(r,u,o,a){if(u){var s=[],c=i(u),l=function t(r,u){if(null==r||"object"!=e(r))return[];null==u&&(u=r);for(var o=r.subject?[u]:[],a=Object.keys(r),s=0;s<a.length;++s){var c=a[s],i=r[c];o.push.apply(o,n(t(i,"left"===c?i:u)))}return o}(u).map(i);t.traverse(r,{enter:function(t,e){if(null!=e&&s.unshift(e),c(t,s,a))if(l.length)for(var r=0,n=l.length;r<n;++r){l[r](t,s,a)&&o(t,e,s);for(var u=0,i=s.length;u<i;++u){var f=s.slice(u+1);l[r](s[u],f,a)&&o(s[u],e,f)}}else o(t,e,s)},leave:function(){s.shift()},keys:a&&a.visitorKeys,fallback:a&&a.fallback||"iteration"})}}function A(t,e,r){var n=[];return d(t,e,(function(t){n.push(t)}),r),n}function x(t){return a.parse(t)}function g(t,e,r){return A(t,x(e),r)}return g.parse=x,g.match=A,g.traverse=d,g.matches=function(t,e,r,n){return!e||!!t&&(r||(r=[]),i(e)(t,r,n))},g.query=g,g}));
//# sourceMappingURL=esquery.lite.min.js.map
