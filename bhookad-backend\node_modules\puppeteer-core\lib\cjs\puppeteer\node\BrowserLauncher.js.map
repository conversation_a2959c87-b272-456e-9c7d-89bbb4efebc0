{"version": 3, "file": "BrowserLauncher.js", "sourceRoot": "", "sources": ["../../../../src/node/BrowserLauncher.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;GAIG;AACH,qCAAmC;AACnC,qCAA+B;AAC/B,yCAA+B;AAE/B,kDAO6B;AAE7B,4DAMwC;AAExC,kDAA6C;AAC7C,wDAAgD;AAChD,mDAAiD;AAEjD,+CAA+D;AAI/D,2EAAyF;AACzF,yDAAiD;AAajD;;;;GAIG;AACH,MAAsB,eAAe;IACnC,QAAQ,CAAmB;IAE3B;;OAEG;IACH,SAAS,CAAgB;IAEzB;;OAEG;IACH,YAAY,SAAwB,EAAE,OAAyB;QAC7D,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC1B,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,UAAyB,EAAE;QACtC,MAAM,EACJ,MAAM,GAAG,KAAK,EACd,gBAAgB,GAAG,KAAK,EACxB,GAAG,GAAG,OAAO,CAAC,GAAG,EACjB,YAAY,GAAG,IAAI,EACnB,aAAa,GAAG,IAAI,EACpB,YAAY,GAAG,IAAI,EACnB,mBAAmB,GAAG,KAAK,EAC3B,eAAe,GAAG,0BAAgB,EAClC,gBAAgB,EAChB,MAAM,GAAG,CAAC,EACV,OAAO,GAAG,KAAK,EACf,kBAAkB,GAAG,IAAI,EACzB,eAAe,GAChB,GAAG,OAAO,CAAC;QAEZ,IAAI,EAAC,QAAQ,EAAC,GAAG,OAAO,CAAC;QAEzB,0CAA0C;QAC1C,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC1D,QAAQ,GAAG,eAAe,CAAC;QAC7B,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,KAAK,EAAE,CAAC;YACtD,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;QAC5E,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC;YACnD,GAAG,OAAO;YACV,QAAQ;SACT,CAAC,CAAC;QAEH,IAAI,CAAC,IAAA,oBAAU,EAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;YAC3C,MAAM,IAAI,KAAK,CACb,2DAA2D,UAAU,CAAC,cAAc,GAAG,CACxF,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC;QAEpE,MAAM,aAAa,GAAG,KAAK,IAAI,EAAE;YAC/B,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,WAAW,EAAE;gBAClD,MAAM,EAAE,UAAU,CAAC,iBAAiB;aACrC,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,IACE,IAAI,CAAC,QAAQ,KAAK,SAAS;YAC3B,QAAQ,KAAK,eAAe;YAC5B,OAAO,EACP,CAAC;YACD,MAAM,IAAI,KAAK,CACb,oEAAoE,CACrE,CAAC;QACJ,CAAC;QAED,MAAM,cAAc,GAAG,IAAA,iBAAM,EAAC;YAC5B,cAAc,EAAE,UAAU,CAAC,cAAc;YACzC,IAAI,EAAE,UAAU,CAAC,IAAI;YACrB,YAAY;YACZ,aAAa;YACb,YAAY;YACZ,MAAM;YACN,GAAG;YACH,IAAI,EAAE,OAAO;YACb,MAAM,EAAE,aAAa;SACtB,CAAC,CAAC;QAEH,IAAI,OAAgB,CAAC;QACrB,IAAI,aAAyB,CAAC;QAC9B,IAAI,OAAO,GAAG,KAAK,CAAC;QAEpB,MAAM,oBAAoB,GAAyB,KAAK,IAAI,EAAE;YAC5D,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO;YACT,CAAC;YACD,OAAO,GAAG,IAAI,CAAC;YACf,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;QACzD,CAAC,CAAC;QAEF,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,eAAe,EAAE,CAAC;gBAChE,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CACpC,cAAc,EACd,oBAAoB,EACpB;oBACE,OAAO;oBACP,eAAe;oBACf,MAAM;oBACN,eAAe;oBACf,mBAAmB;iBACpB,CACF,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,IAAI,OAAO,EAAE,CAAC;oBACZ,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE;wBACjE,OAAO;wBACP,eAAe;wBACf,MAAM;qBACP,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,aAAa,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,cAAc,EAAE;wBACnE,OAAO;wBACP,eAAe;wBACf,MAAM;qBACP,CAAC,CAAC;gBACL,CAAC;gBACD,IAAI,QAAQ,KAAK,eAAe,EAAE,CAAC;oBACjC,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAC3C,cAAc,EACd,aAAa,EACb,oBAAoB,EACpB;wBACE,eAAe;wBACf,mBAAmB;qBACpB,CACF,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,OAAO,GAAG,MAAM,uBAAU,CAAC,OAAO,CAChC,aAAa,EACb,EAAE,EACF,mBAAmB,EACnB,eAAe,EACf,gBAAgB,EAChB,cAAc,CAAC,WAAW,EAC1B,oBAAoB,EACpB,OAAO,CAAC,YAAY,CACrB,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,KAAK,oBAAoB,EAAE,CAAC;YAC5B,IAAI,KAAK,YAAY,uBAAoB,EAAE,CAAC;gBAC1C,MAAM,IAAI,wBAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxC,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACpC,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC3C,MAAM,IAAI,KAAK,CACb,gIAAgI,CACjI,CAAC;YACJ,CAAC;YAED,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;oBAC1B,OAAO,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBACxC,CAAC,CAAC;aACH,CAAC,CAAC;QACL,CAAC;QAED,IAAI,kBAAkB,EAAE,CAAC;YACvB,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACjD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAwBD;;OAEG;IACO,KAAK,CAAC,YAAY,CAC1B,cAAyC,EACzC,aAA0B;QAE1B,IAAI,aAAa,EAAE,CAAC;YAClB,0CAA0C;YAC1C,IAAI,CAAC;gBACH,MAAM,aAAa,CAAC,YAAY,EAAE,CAAC;gBACnC,MAAM,cAAc,CAAC,SAAS,EAAE,CAAC;YACnC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;gBAClB,MAAM,cAAc,CAAC,KAAK,EAAE,CAAC;YAC/B,CAAC;QACH,CAAC;aAAM,CAAC;YACN,yCAAyC;YACzC,MAAM,IAAA,wBAAc,EAClB,IAAA,cAAI,EACF,IAAA,cAAI,EAAC,cAAc,CAAC,SAAS,EAAE,CAAC,EAChC,IAAA,eAAK,EAAC,IAAI,CAAC,CAAC,IAAI,CACd,IAAA,aAAG,EAAC,GAAG,EAAE;gBACP,OAAO,IAAA,cAAI,EAAC,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC;YACtC,CAAC,CAAC,CACH,CACF,CACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,iBAAiB,CAC/B,OAAgB,EAChB,OAAe;QAEf,IAAI,CAAC;YACH,MAAM,OAAO,CAAC,aAAa,CACzB,CAAC,CAAC,EAAE;gBACF,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,MAAM,CAAC;YAC7B,CAAC,EACD,EAAC,OAAO,EAAC,CACV,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,OAAO,CAAC,KAAK,EAAE,CAAC;YACtB,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,yBAAyB,CACvC,cAAyC,EACzC,IAIC;QAED,MAAM,iBAAiB,GAAG,MAAM,cAAc,CAAC,iBAAiB,CAC9D,uCAA4B,EAC5B,IAAI,CAAC,OAAO,CACb,CAAC;QACF,MAAM,SAAS,GAAG,MAAM,kDAAkB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QACrE,OAAO,IAAI,0BAAU,CACnB,iBAAiB,EACjB,SAAS,EACT,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,eAAe,CACrB,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,uBAAuB,CACrC,cAAyC,EACzC,IAIC;QAED,0EAA0E;QAC1E,mCAAmC;QACnC,MAAM,EAAC,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAC,GAAG,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC;QACrE,MAAM,SAAS,GAAG,IAAI,gCAAa,CACjC,SAAkC,EAClC,QAAiC,CAClC,CAAC;QACF,OAAO,IAAI,0BAAU,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,wBAAwB,CACtC,cAAyC,EACzC,UAAsB,EACtB,aAAmC,EACnC,IAGC;QAED,MAAM,IAAI,GAAG,wDAAa,yBAAyB,CAAC,iBAAiB,GAAC,CAAC;QACvE,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QACjE,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACnC,UAAU,EAAE,cAAc;YAC1B,aAAa,EAAE,UAAU;YACzB,aAAa;YACb,OAAO,EAAE,cAAc,CAAC,WAAW;YACnC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;SAC9C,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,iBAAiB,CAC/B,cAAyC,EACzC,aAAmC,EACnC,IAMC;QAED,MAAM,iBAAiB,GACrB,CAAC,MAAM,cAAc,CAAC,iBAAiB,CACrC,kDAAuC,EACvC,IAAI,CAAC,OAAO,CACb,CAAC,GAAG,UAAU,CAAC;QAClB,MAAM,SAAS,GAAG,MAAM,kDAAkB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QACrE,MAAM,IAAI,GAAG,wDAAa,yBAAyB,CAAC,iBAAiB,GAAC,CAAC;QACvE,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,cAAc,CAC5C,iBAAiB,EACjB,SAAS,EACT,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,eAAe,CACrB,CAAC;QACF,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACnC,UAAU,EAAE,cAAc;YAC1B,aAAa;YACb,OAAO,EAAE,cAAc,CAAC,WAAW;YACnC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;SAC9C,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,cAAc;QACtB,OAAO,IAAA,gBAAI,EACT,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,kBAAkB,IAAI,IAAA,gBAAM,GAAE,EAC3D,iBAAiB,IAAI,CAAC,OAAO,WAAW,CACzC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,qBAAqB,CACnB,QAA4B,EAC5B,YAAY,GAAG,IAAI;QAEnB,IAAI,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,cAAc,CAAC;QACjE,IAAI,cAAc,EAAE,CAAC;YACnB,IAAI,YAAY,IAAI,CAAC,IAAA,oBAAU,EAAC,cAAc,CAAC,EAAE,CAAC;gBAChD,MAAM,IAAI,KAAK,CACb,qDAAqD,cAAc,iCAAiC,CACrG,CAAC;YACJ,CAAC;YACD,OAAO,cAAc,CAAC;QACxB,CAAC;QAED,SAAS,kCAAkC,CACzC,OAA0B,EAC1B,QAA4B;YAE5B,QAAQ,OAAO,EAAE,CAAC;gBAChB,KAAK,QAAQ;oBACX,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;wBACzB,OAAO,kBAAgB,CAAC,mBAAmB,CAAC;oBAC9C,CAAC;oBACD,OAAO,kBAAgB,CAAC,MAAM,CAAC;gBACjC,KAAK,SAAS;oBACZ,OAAO,kBAAgB,CAAC,OAAO,CAAC;YACpC,CAAC;YACD,OAAO,kBAAgB,CAAC,MAAM,CAAC;QACjC,CAAC;QAED,MAAM,WAAW,GAAG,kCAAkC,CACpD,IAAI,CAAC,OAAO,EACZ,QAAQ,CACT,CAAC;QAEF,cAAc,GAAG,IAAA,gCAAqB,EAAC;YACrC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,mBAAoB;YAC7C,OAAO,EAAE,WAAW;YACpB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc;SACvC,CAAC,CAAC;QAEH,IAAI,YAAY,IAAI,CAAC,IAAA,oBAAU,EAAC,cAAc,CAAC,EAAE,CAAC;YAChD,MAAM,aAAa,GACjB,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;YACxD,IAAI,aAAa,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CACb,qDAAqD,cAAc,iBAAiB,aAAa,gCAAgC,CAClI,CAAC;YACJ,CAAC;YACD,QAAQ,IAAI,CAAC,OAAO,EAAE,CAAC;gBACrB,KAAK,QAAQ;oBACX,MAAM,IAAI,KAAK,CACb,+BAA+B,IAAI,CAAC,SAAS,CAAC,cAAc,+BAA+B;wBACzF,4GAA4G,WAAW,UAAU;wBACjI,4DAA4D,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,cAAc,MAAM;wBAC7G,iGAAiG,CACpG,CAAC;gBACJ,KAAK,SAAS;oBACZ,MAAM,IAAI,KAAK,CACb,gCAAgC,IAAI,CAAC,SAAS,CAAC,cAAc,+BAA+B;wBAC1F,oIAAoI;wBACpI,4DAA4D,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,cAAc,MAAM;wBAC7G,iGAAiG,CACpG,CAAC;YACN,CAAC;QACH,CAAC;QACD,OAAO,cAAc,CAAC;IACxB,CAAC;CACF;AAvbD,0CAubC"}