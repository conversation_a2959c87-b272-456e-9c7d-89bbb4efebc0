{"version": 3, "file": "StorageProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/storage/StorageProcessor.ts"], "names": [], "mappings": ";;;AAiBA,+DAGuC;AAEvC,wDAAgD;AAEhD,kDAA8C;AAE9C,wEAAgE;AAChE,gEAIoC;AAEpC;;GAEG;AACH,MAAa,gBAAgB;IAClB,iBAAiB,CAAY;IAC7B,uBAAuB,CAAyB;IAChD,OAAO,CAAuB;IAEvC,YACE,gBAA2B,EAC3B,sBAA8C,EAC9C,MAA4B;QAE5B,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,aAAa,CACjB,MAAuC;QAEvC,MAAM,YAAY,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAExE,IAAI,WAAW,CAAC;QAChB,IAAI,CAAC;YACH,WAAW,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CACpD,oBAAoB,EACpB;gBACE,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC;aAC7D,CACF,CAAC;QACJ,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxC,6DAA6D;gBAC7D,MAAM,IAAI,wCAA0B,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACpD,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;QAED,MAAM,kBAAkB,GAAG,WAAW,CAAC,OAAO;aAC3C,MAAM;QACL,yEAAyE;QACzE,8EAA8E;QAC9E,gBAAgB;QAChB,CAAC,CAAC,EAAE,EAAE,CACJ,YAAY,CAAC,YAAY,KAAK,SAAS;YACvC,CAAC,CAAC,YAAY,EAAE,YAAY,KAAK,YAAY,CAAC,YAAY,CAC7D;aACA,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE;YACpB,MAAM,UAAU,GAAG,IAAA,iCAAe,EAAC,SAAS,CAAC,CAAC;YAC9C,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QACtD,CAAC,CAAC;aACD,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YAChB,GAAG,MAAM;YACT,gDAAgD;YAChD,OAAO,EAAE,CAAC;SACX,CAAC,CAAC,CAAC;QAEN,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,oBAAoB,EAAE;YAC7D,OAAO,EAAE,kBAAkB;YAC3B,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC;SAC7D,CAAC,CAAC;QACH,OAAO;YACL,YAAY;SACb,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,UAAU,CACd,MAAoC;QAEpC,MAAM,YAAY,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAExE,IAAI,WAAW,CAAC;QAChB,IAAI,CAAC;YACH,WAAW,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CACpD,oBAAoB,EACpB;gBACE,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC;aAC7D,CACF,CAAC;QACJ,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxC,6DAA6D;gBAC7D,MAAM,IAAI,wCAA0B,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACpD,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;QAED,MAAM,mBAAmB,GAAG,WAAW,CAAC,OAAO;aAC5C,MAAM;QACL,yEAAyE;QACzE,8EAA8E;QAC9E,gBAAgB;QAChB,CAAC,CAAC,EAAE,EAAE,CACJ,YAAY,CAAC,YAAY,KAAK,SAAS;YACvC,CAAC,CAAC,YAAY,EAAE,YAAY,KAAK,YAAY,CAAC,YAAY,CAC7D;aACA,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,iCAAe,EAAC,CAAC,CAAC,CAAC;aAC9B,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QAEtD,OAAO;YACL,OAAO,EAAE,mBAAmB;YAC5B,YAAY;SACb,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,SAAS,CACb,MAAmC;QAEnC,MAAM,YAAY,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACxE,MAAM,SAAS,GAAG,IAAA,iCAAe,EAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAExD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,oBAAoB,EAAE;gBAC7D,OAAO,EAAE,CAAC,SAAS,CAAC;gBACpB,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC;aAC7D,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,IAAI,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxC,6DAA6D;gBAC7D,MAAM,IAAI,wCAA0B,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACpD,CAAC;YAED,IAAI,CAAC,OAAO,EAAE,CAAC,gBAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YACxC,MAAM,IAAI,wCAA0B,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvD,CAAC;QACD,OAAO;YACL,YAAY;SACb,CAAC;IACJ,CAAC;IAED,yBAAyB,CAAC,GAAU;QAClC,wDAAwD;QACxD,uKAAuK;QACvK,OAAO,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,uCAAuC,CAAC,CAAC;IAC1E,CAAC;IAED,uBAAuB,CACrB,YAAkC;QAElC,OAAO,YAAY,CAAC,WAAW,KAAK,SAAS;YAC3C,CAAC,CAAC,SAAS;YACX,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC;IAC/B,CAAC;IAED,4CAA4C,CAC1C,UAAsD;QAEtD,MAAM,iBAAiB,GAAW,UAAU,CAAC,OAAO,CAAC;QACrD,MAAM,eAAe,GACnB,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC7D,sEAAsE;QACtE,+EAA+E;QAC/E,iFAAiF;QACjF,oCAAoC;QACpC,OAAO;YACL,WAAW,EAAE,eAAe,CAAC,WAAW;SACzC,CAAC;IACJ,CAAC;IAED,uCAAuC,CACrC,UAAiD;QAEjD,MAAM,wBAAwB,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC3D,IAAI,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;QAC3C,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC/B,MAAM,GAAG,GAAG,sCAAgB,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC1D,IAAI,GAAG,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC1B,mDAAmD;gBACnD,YAAY,GAAG,GAAG,CAAC,MAAM,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACN,iFAAiF;gBACjF,oCAAoC;gBACpC,YAAY,GAAG,GAAG,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,EAAE,CAAC;YACpD,CAAC;QACH,CAAC;QAED,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YACtD,IACE,GAAG,KAAK,SAAS;gBACjB,KAAK,KAAK,SAAS;gBACnB,CAAC,CAAC,MAAM,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EACtD,CAAC;gBACD,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,IAAI,wBAAwB,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,OAAO,EAAE,CACZ,gBAAO,CAAC,SAAS,EACjB,+BAA+B,IAAI,CAAC,SAAS,CAC3C,MAAM,CAAC,WAAW,CAAC,wBAAwB,CAAC,CAC7C,EAAE,CACJ,CAAC;QACJ,CAAC;QAED,gFAAgF;QAChF,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW,IAAI,SAAS,CAAC;QAExD,OAAO;YACL,WAAW;YACX,GAAG,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC,YAAY,EAAC,CAAC;SACtD,CAAC;IACJ,CAAC;IAED,2BAA2B,CACzB,aAAsD;QAEtD,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;YAChC,yCAAyC;YACzC,OAAO,EAAC,WAAW,EAAE,SAAS,EAAC,CAAC;QAClC,CAAC;QACD,IAAI,aAAa,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,4CAA4C,CAAC,aAAa,CAAC,CAAC;QAC1E,CAAC;QACD,IAAA,kBAAM,EAAC,aAAa,CAAC,IAAI,KAAK,YAAY,EAAE,wBAAwB,CAAC,CAAC;QACtE,yCAAyC;QACzC,uCAAuC;QACvC,OAAO,IAAI,CAAC,uCAAuC,CAAC,aAAa,CAAC,CAAC;IACrE,CAAC;IAED,YAAY,CAAC,MAAsB,EAAE,MAA6B;QAChE,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,CACL,CAAC,MAAM,CAAC,MAAM,KAAK,SAAS,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC;YAChE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC;YAC1D,8CAA8C;YAC9C,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS;gBACzB,IAAA,sCAAoB,EAAC,MAAM,CAAC,KAAK,CAAC;oBAChC,IAAA,sCAAoB,EAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACvC,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC;YAC1D,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC;YAC1D,CAAC,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,CAAC;YACtE,CAAC,MAAM,CAAC,MAAM,KAAK,SAAS,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC;YAChE,CAAC,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,CAAC;YACtE,CAAC,MAAM,CAAC,MAAM,KAAK,SAAS,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CACjE,CAAC;IACJ,CAAC;CACF;AA7OD,4CA6OC"}