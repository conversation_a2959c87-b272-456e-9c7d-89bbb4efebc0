{"name": "vite", "version": "4.5.14", "type": "module", "license": "MIT", "author": "<PERSON>", "description": "Native-ESM powered web dev build tool", "bin": {"vite": "bin/vite.js"}, "keywords": ["frontend", "framework", "hmr", "dev-server", "build-tool", "vite"], "main": "./dist/node/index.js", "types": "./dist/node/index.d.ts", "exports": {".": {"types": "./dist/node/index.d.ts", "import": "./dist/node/index.js", "require": "./index.cjs"}, "./client": {"types": "./client.d.ts"}, "./dist/client/*": "./dist/client/*", "./types/*": {"types": "./types/*"}, "./package.json": "./package.json"}, "files": ["bin", "dist", "client.d.ts", "index.cjs", "types"], "engines": {"node": "^14.18.0 || >=16.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite.git", "directory": "packages/vite"}, "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "homepage": "https://github.com/vitejs/vite/tree/main/#readme", "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "rimraf dist && pnpm run build-bundle -w", "build": "rimraf dist && run-s build-bundle build-types", "build-bundle": "rollup --config rollup.config.ts --configPlugin typescript", "build-types": "run-s build-types-temp build-types-pre-patch build-types-roll build-types-post-patch build-types-check", "build-types-temp": "tsc --emitDeclarationOnly --outDir temp/node -p src/node", "build-types-pre-patch": "tsx scripts/prePatchTypes.ts", "build-types-roll": "tsx scripts/api-extractor.ts run && rimraf temp", "build-types-post-patch": "tsx scripts/postPatchTypes.ts", "build-types-check": "tsx scripts/checkBuiltTypes.ts && tsc --project tsconfig.check.json", "typecheck": "tsc --noEmit", "lint": "eslint --cache --ext .ts src/**", "format": "prettier --write --cache --parser typescript \"src/**/*.ts\"", "prepublishOnly": "npm run build"}, "//": "READ CONTRIBUTING.md to understand what to put under deps vs. devDeps!", "dependencies": {"esbuild": "^0.18.10", "postcss": "^8.4.27", "rollup": "^3.27.1"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "devDependencies": {"@ampproject/remapping": "^2.2.1", "@babel/parser": "^7.22.7", "@babel/types": "^7.22.5", "@jridgewell/trace-mapping": "^0.3.18", "@rollup/plugin-alias": "^4.0.4", "@rollup/plugin-commonjs": "^25.0.3", "@rollup/plugin-dynamic-import-vars": "^2.0.4", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "15.1.0", "@rollup/plugin-typescript": "^11.1.2", "@rollup/pluginutils": "^5.0.2", "@types/escape-html": "^1.0.2", "@types/pnpapi": "^0.0.2", "acorn": "^8.10.0", "acorn-walk": "^8.2.0", "cac": "^6.7.14", "chokidar": "^3.5.3", "connect": "^3.7.0", "connect-history-api-fallback": "^2.0.0", "convert-source-map": "^2.0.0", "cors": "^2.8.5", "cross-spawn": "^7.0.3", "debug": "^4.3.4", "dep-types": "link:./src/types", "dotenv": "^16.3.1", "dotenv-expand": "^9.0.0", "es-module-lexer": "^1.3.0", "escape-html": "^1.0.3", "estree-walker": "^3.0.3", "etag": "^1.8.1", "fast-glob": "^3.3.1", "http-proxy": "^1.18.1", "json-stable-stringify": "^1.0.2", "launch-editor-middleware": "^2.6.0", "lightningcss": "^1.21.5", "magic-string": "^0.30.2", "micromatch": "^4.0.5", "mlly": "^1.4.0", "mrmime": "^1.0.1", "okie": "^1.0.1", "open": "^8.4.2", "parse5": "^7.1.2", "periscopic": "^3.1.0", "picocolors": "^1.0.0", "picomatch": "^2.3.1", "postcss-import": "^15.1.0", "postcss-load-config": "^4.0.1", "postcss-modules": "^6.0.0", "resolve.exports": "^2.0.2", "rollup-plugin-license": "^3.0.1", "sirv": "^2.0.3", "source-map-support": "^0.5.21", "strip-ansi": "^7.1.0", "strip-literal": "^1.3.0", "tsconfck": "^2.1.2", "tslib": "^2.6.1", "types": "link:./types", "ufo": "^1.2.0", "ws": "^8.13.0"}, "peerDependencies": {"@types/node": ">= 14", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "stylus": "*", "sugarss": "*", "terser": "^5.4.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "sass": {"optional": true}, "stylus": {"optional": true}, "less": {"optional": true}, "sugarss": {"optional": true}, "lightningcss": {"optional": true}, "terser": {"optional": true}}}