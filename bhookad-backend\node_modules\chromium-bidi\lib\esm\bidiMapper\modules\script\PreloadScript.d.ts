import type { Protocol } from 'devtools-protocol';
import type { <PERSON><PERSON><PERSON>, <PERSON>rowsing<PERSON>ontex<PERSON>, <PERSON><PERSON><PERSON> } from '../../../protocol/protocol.js';
import type { LoggerFn } from '../../../utils/log.js';
import type { CdpTarget } from '../cdp/CdpTarget.js';
import { ChannelProxy } from './ChannelProxy.js';
/**
 * BiDi IDs are generated by the server and are unique within contexts.
 *
 * CDP preload script IDs are generated by the client and are unique
 * within sessions.
 *
 * The mapping between BiDi and CDP preload script IDs is 1:many.
 * BiDi IDs are needed by the mapper to keep track of potential multiple CDP IDs
 * in the client.
 */
export declare class PreloadScript {
    #private;
    get id(): string;
    get targetIds(): Set<Protocol.Target.TargetID>;
    constructor(params: Script.AddPreloadScriptParameters, logger?: LoggerFn);
    /** Channels of the preload script. */
    get channels(): ChannelProxy[];
    /** Contexts of the preload script, if any */
    get contexts(): BrowsingContext.BrowsingContext[] | undefined;
    /** UserContexts of the preload script, if any */
    get userContexts(): Browser.UserContext[] | undefined;
    /**
     * Adds the script to the given CDP targets by calling the
     * `Page.addScriptToEvaluateOnNewDocument` command.
     */
    initInTargets(cdpTargets: Iterable<CdpTarget>, runImmediately: boolean): Promise<void>;
    /**
     * Adds the script to the given CDP target by calling the
     * `Page.addScriptToEvaluateOnNewDocument` command.
     */
    initInTarget(cdpTarget: CdpTarget, runImmediately: boolean): Promise<void>;
    /**
     * Removes this script from all CDP targets.
     */
    remove(): Promise<void>;
    /** Removes the provided cdp target from the list of cdp preload scripts. */
    dispose(cdpTargetId: Protocol.Target.TargetID): void;
}
