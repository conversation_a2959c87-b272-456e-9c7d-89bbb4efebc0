{"version": 3, "file": "HandleIterator.js", "sourceRoot": "", "sources": ["../../../../src/common/HandleIterator.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DH,0DASC;AApED,yDAAqE;AAIrE,MAAM,kBAAkB,GAAG,EAAE,CAAC;AAE9B;;;;;;GAMG;AACH,KAAK,SAAS,CAAC,CAAC,2BAA2B,CACzC,QAAwC,EACxC,IAAY;;;QAEZ,MAAM,KAAK,kCAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;YACnE,MAAM,OAAO,GAAG,EAAE,CAAC;YACnB,OAAO,OAAO,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;gBAC7B,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrC,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;oBAChB,MAAM;gBACR,CAAC;gBACD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC7B,CAAC;YACD,OAAO,OAAO,CAAC;QACjB,CAAC,EAAE,IAAI,CAAC,QAAA,CAAC;QACT,MAAM,UAAU,GAAG,CAAC,MAAM,KAAK,CAAC,aAAa,EAAE,CAA8B,CAAC;QAC9E,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;QACpC,MAAM,KAAK,kCAAG,IAAI,+BAAe,EAAE,QAAA,CAAC;QACpC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE;YACf,uBAAqB,OAAO,EAAE,CAAC;;;0BAApB,MAAM,kDAAA;oBACf,MAAM,CAAC,6BAAa,CAAC,EAAE,CAAC;;;;;;;;;aACzB;QACH,CAAC,CAAC,CAAC;QACH,KAAK,CAAC,CAAC,OAAO,CAAC;QACf,OAAO,UAAU,CAAC,IAAI,KAAK,CAAC,CAAC;;;;;;;;;CAC9B;AAED;;;GAGG;AAEH,KAAK,SAAS,CAAC,CAAC,uBAAuB,CACrC,QAAwC;IAExC,IAAI,IAAI,GAAG,kBAAkB,CAAC;IAC9B,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,2BAA2B,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;QAC7D,IAAI,KAAK,CAAC,CAAC;IACb,CAAC;AACH,CAAC;AAID;;GAEG;AACI,KAAK,SAAS,CAAC,CAAC,uBAAuB,CAC5C,MAAsC;;;QAEtC,MAAM,eAAe,kCAAG,MAAM,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;YAC7D,OAAO,CAAC,KAAK,SAAS,CAAC;gBACrB,KAAK,CAAC,CAAC,QAAQ,CAAC;YAClB,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CAAC,QAAA,CAAC;QACH,KAAK,CAAC,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC;;;;;;;;;CACjD"}