import React from 'react'
import { Star, MapPin, Clock } from 'lucide-react'
import './TrendingVendors.css'

const TrendingVendors = () => {
  const vendors = [
    {
      name: "<PERSON>",
      cuisine: "North Indian",
      rating: 4.8,
      location: "Connaught Place",
      time: "10 AM - 11 PM",
      image: "🍛",
      speciality: "Butter Chicken & Naan"
    },
    {
      name: "Mumbai Chaat Corner",
      cuisine: "Street Food",
      rating: 4.6,
      location: "Khan Market",
      time: "4 PM - 12 AM",
      image: "🥘",
      speciality: "Pani Puri & Bhel"
    },
    {
      name: "Delhi Momos Hub",
      cuisine: "Tibetan",
      rating: 4.7,
      location: "Lajpat Nagar",
      time: "12 PM - 10 PM",
      image: "🥟",
      speciality: "Steamed & Fried Momos"
    }
  ]

  return (
    <section className="trending-vendors section" id="vendors">
      <div className="container">
        <h2 className="section-title">Trending Street Food Vendors</h2>
        <div className="vendors-grid">
          {vendors.map((vendor, index) => (
            <div key={index} className="vendor-card">
              <div className="vendor-image">
                <span className="food-emoji">{vendor.image}</span>
              </div>
              <div className="vendor-info">
                <h3 className="vendor-name">{vendor.name}</h3>
                <p className="vendor-cuisine">{vendor.cuisine}</p>
                <p className="vendor-speciality">{vendor.speciality}</p>
                
                <div className="vendor-details">
                  <div className="rating">
                    <Star className="star-filled" size={16} />
                    <span>{vendor.rating}</span>
                  </div>
                  
                  <div className="location">
                    <MapPin size={16} />
                    <span>{vendor.location}</span>
                  </div>
                  
                  <div className="timing">
                    <Clock size={16} />
                    <span>{vendor.time}</span>
                  </div>
                </div>
                
                <button className="btn-primary vendor-btn">Visit Now</button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default TrendingVendors
