import React, { useState, useEffect } from 'react'
import { Star, MapPin, Clock } from 'lucide-react'
import { vendorService } from '../services/api'
import './TrendingVendors.css'

const TrendingVendors = () => {
  const [vendors, setVendors] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Fetch vendors from backend
  useEffect(() => {
    const fetchVendors = async () => {
      try {
        setLoading(true)
        const response = await vendorService.getAll({ limit: 6 })
        
        if (response.success) {
          setVendors(response.vendors)
        } else {
          setError('Failed to load vendors')
        }
      } catch (err) {
        console.error('Error fetching vendors:', err)
        setError('Failed to connect to server')
        // Fallback to static data
        setVendors([
          {
            id: 1,
            name: "<PERSON> Dhab<PERSON>",
            cuisine_type: "North Indian",
            rating: 4.8,
            address: "Connaught Place",
            specialties: ["Butter Chicken", "Naan"]
          },
          {
            id: 2,
            name: "Mumbai Chaat Corner",
            cuisine_type: "Street Food",
            rating: 4.6,
            address: "Khan Market",
            specialties: ["Pani Puri", "Bhel Puri"]
          },
          {
            id: 3,
            name: "Delhi Paratha Wala",
            cuisine_type: "North Indian",
            rating: 4.7,
            address: "Chandni Chowk",
            specialties: ["Aloo Paratha", "Lassi"]
          },
          {
            id: 4,
            name: "South Indian Express",
            cuisine_type: "South Indian",
            rating: 4.5,
            address: "CP Metro Station",
            specialties: ["Dosa", "Idli", "Sambhar"]
          },
          {
            id: 5,
            name: "Rajasthani Thali House",
            cuisine_type: "Rajasthani",
            rating: 4.9,
            address: "Karol Bagh",
            specialties: ["Dal Baati", "Gatte Ki Sabzi"]
          },
          {
            id: 6,
            name: "Bengali Sweet Corner",
            cuisine_type: "Bengali",
            rating: 4.4,
            address: "CR Park",
            specialties: ["Rasgulla", "Sandesh"]
          }
        ])
      } finally {
        setLoading(false)
      }
    }

    fetchVendors()
  }, [])

  if (loading) {
    return (
      <section className="trending-vendors section" id="vendors">
        <div className="container">
          <h2 className="section-title">Trending Street Food Vendors</h2>
          <div className="loading">Loading vendors...</div>
        </div>
      </section>
    )
  }

  if (error) {
    return (
      <section className="trending-vendors section" id="vendors">
        <div className="container">
          <h2 className="section-title">Trending Street Food Vendors</h2>
          <div className="error">Error: {error}</div>
        </div>
      </section>
    )
  }

  return (
    <section className="trending-vendors section" id="vendors">
      <div className="container">
        <h2 className="section-title">Trending Street Food Vendors</h2>
        <div className="vendors-grid">
          {vendors.map((vendor, index) => (
            <div key={vendor.id || index} className="vendor-card">
              <div className="vendor-image">
                <span className="food-emoji">🍛</span>
              </div>
              <div className="vendor-info">
                <h3 className="vendor-name">{vendor.name}</h3>
                <p className="vendor-cuisine">{vendor.cuisine_type}</p>
                <p className="vendor-speciality">
                  {vendor.specialties ? vendor.specialties.join(', ') : 'Delicious food'}
                </p>
                
                <div className="vendor-details">
                  <div className="rating">
                    <Star className="star-filled" size={16} />
                    <span>{vendor.rating}</span>
                  </div>
                  
                  <div className="location">
                    <MapPin size={16} />
                    <span>{vendor.address || vendor.city}</span>
                  </div>
                  
                  <div className="timing">
                    <Clock size={16} />
                    <span>10 AM - 10 PM</span>
                  </div>
                </div>
                
                <button className="btn-primary vendor-btn">Visit Now</button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default TrendingVendors
