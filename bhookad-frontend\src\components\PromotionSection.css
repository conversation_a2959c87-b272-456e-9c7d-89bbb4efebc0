.promotion-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3436;
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #636e72;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Featured Vendors */
.featured-vendors {
  margin-bottom: 80px;
}

.subsection-title {
  font-size: 2rem;
  font-weight: 600;
  color: #2d3436;
  text-align: center;
  margin-bottom: 40px;
}

.vendors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
}

.vendor-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.vendor-card.promoted {
  border: 2px solid #fdcb6e;
  box-shadow: 0 15px 40px rgba(253, 203, 110, 0.3);
}

.vendor-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.vendor-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.vendor-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.vendor-card:hover .vendor-image img {
  transform: scale(1.05);
}

.promotion-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 6px 12px;
  border-radius: 20px;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.promotion-badge.premium {
  background: linear-gradient(135deg, #6c5ce7, #a29bfe);
}

.promotion-badge.featured {
  background: linear-gradient(135deg, #fd79a8, #e84393);
}

.promotion-badge.top {
  background: linear-gradient(135deg, #00b894, #00a085);
}

.promotion-badge.trending {
  background: linear-gradient(135deg, #fdcb6e, #e17055);
}

.vendor-info {
  padding: 24px;
}

.vendor-info h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3436;
  margin-bottom: 12px;
}

.vendor-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.location {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #636e72;
  font-size: 0.9rem;
}

.rating {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #fdcb6e;
  font-size: 0.9rem;
}

.cuisine-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.cuisine {
  background: #f8f9fa;
  color: #6c5ce7;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.price-range {
  color: #00b894;
  font-weight: 600;
  font-size: 0.9rem;
}

.special-offer {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #ff7675, #fd79a8);
  color: white;
  padding: 12px;
  border-radius: 12px;
  font-weight: 500;
  font-size: 0.9rem;
}

/* Promotion Benefits */
.promotion-benefits {
  margin-bottom: 80px;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.benefit-card {
  background: white;
  padding: 40px 30px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.benefit-card:hover {
  transform: translateY(-5px);
}

.benefit-icon {
  color: #fdcb6e;
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.benefit-card h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3436;
  margin-bottom: 12px;
}

.benefit-card p {
  color: #636e72;
  line-height: 1.6;
}

/* Promotion Packages */
.promotion-packages {
  margin-bottom: 80px;
}

.packages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.package-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  position: relative;
}

.package-card.popular {
  border: 2px solid #fdcb6e;
  transform: scale(1.05);
}

.package-card:hover {
  transform: translateY(-5px);
}

.package-card.popular:hover {
  transform: scale(1.05) translateY(-5px);
}

.popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #fdcb6e, #e17055);
  color: white;
  padding: 6px 20px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.package-header {
  text-align: center;
  margin-bottom: 30px;
}

.package-header h4 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3436;
  margin-bottom: 12px;
}

.package-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
}

.price {
  font-size: 2.5rem;
  font-weight: 700;
  color: #e17055;
}

.duration {
  color: #636e72;
  font-size: 1rem;
}

.package-features {
  margin-bottom: 30px;
}

.feature {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.feature-check {
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #00b894, #00a085);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
}

.package-btn {
  width: 100%;
  padding: 12px;
  background: linear-gradient(135deg, #fdcb6e, #e17055);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  transition: transform 0.3s ease;
}

.package-btn:hover {
  transform: translateY(-2px);
}

/* Success Stats */
.success-stats {
  margin-bottom: 80px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
}

.stat-card {
  background: white;
  padding: 30px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #e17055;
  display: block;
  margin-bottom: 8px;
}

.stat-label {
  color: #636e72;
  font-weight: 500;
}

/* CTA Section */
.promotion-cta {
  background: linear-gradient(135deg, #2d3436, #636e72);
  border-radius: 20px;
  padding: 60px 40px;
  text-align: center;
  color: white;
}

.cta-content h3 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 12px;
}

.cta-content p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 30px;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-buttons .btn-primary {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .promotion-section {
    padding: 60px 0;
  }

  .section-title {
    font-size: 2rem;
  }

  .vendors-grid {
    grid-template-columns: 1fr;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
  }

  .packages-grid {
    grid-template-columns: 1fr;
  }

  .package-card.popular {
    transform: none;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .promotion-cta {
    padding: 40px 20px;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
}
