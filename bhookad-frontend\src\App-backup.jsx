import React from 'react'

function App() {
  return (
    <div style={{
      padding: '50px', 
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 
      color: 'white', 
      textAlign: 'center',
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <h1 style={{fontSize: '3rem', marginBottom: '20px'}}>🔥 BHOOKAD 🔥</h1>
      <h2 style={{fontSize: '2rem', marginBottom: '20px'}}>Site is Working!</h2>
      <p style={{fontSize: '1.2rem', marginBottom: '10px'}}>React app is running successfully.</p>
      <p style={{fontSize: '1rem', opacity: 0.8}}>Server: localhost:5173</p>
      <div style={{marginTop: '30px', padding: '20px', background: 'rgba(255,255,255,0.1)', borderRadius: '10px'}}>
        <p>✅ React: Working</p>
        <p>✅ Vite: Working</p>
        <p>✅ Server: Running</p>
      </div>
    </div>
  )
}

export default App
