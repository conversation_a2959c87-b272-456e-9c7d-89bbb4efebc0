{"author": "<PERSON> <<EMAIL>>", "name": "ast-types", "version": "0.13.4", "description": "Esprima-compatible implementation of the Mozilla JS Parser API", "keywords": ["ast", "abstract syntax tree", "hierarchy", "mozilla", "spidermonkey", "parser api", "esprima", "types", "type system", "type checking", "dynamic types", "parsing", "transformation", "syntax"], "homepage": "http://github.com/benjamn/ast-types", "repository": {"type": "git", "url": "git://github.com/benjamn/ast-types.git"}, "license": "MIT", "main": "main.js", "types": "main.d.ts", "scripts": {"gen": "ts-node --transpile-only script/gen-types.ts", "mocha": "test/run.sh", "test": "npm run gen && npm run build && npm run mocha", "clean": "ts-emit-clean", "build": "tsc && ts-add-module-exports", "prepack": "npm run clean && npm run gen && npm run build", "postpack": "npm run clean"}, "dependencies": {"tslib": "^2.0.1"}, "devDependencies": {"@babel/parser": "7.7.7", "@babel/types": "7.4.4", "@types/esprima": "4.0.2", "@types/glob": "7.1.1", "@types/mocha": "5.2.6", "@types/node": "12.0.0", "espree": "7.3.0", "esprima": "4.0.1", "esprima-fb": "15001.1001.0-dev-harmony-fb", "flow-parser": "0.132.0", "glob": "7.1.6", "mocha": "6.1.4", "recast": "0.20.0", "reify": "0.20.12", "ts-add-module-exports": "1.0.0", "ts-emit-clean": "1.0.0", "ts-node": "7.0.1", "typescript": "3.9.7"}, "engines": {"node": ">=4"}}