# Google OAuth Setup Guide for Bhookad

## Problem
When clicking "Continue with Google", you get this error:
```
{"code":400,"error_code":"validation_failed","msg":"Unsupported provider: provider is not enabled"}
```

## Solution: Enable Google OAuth in Supabase

### Step 1: Setup Google Cloud Console

1. **Go to Google Cloud Console:**
   - Visit: https://console.cloud.google.com
   - Sign in with your Google account

2. **Create or Select Project:**
   - Create a new project or select existing one
   - Name it "Bhookad" or similar

3. **Enable Google+ API:**
   - Go to "APIs & Services" > "Library"
   - Search for "Google+ API" 
   - Click and Enable it

4. **Create OAuth 2.0 Credentials:**
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Web application"
   - Name: "Bhookad App"

5. **Configure Authorized Redirect URIs:**
   Add this exact URL:
   ```
   https://xktprpguxerqyqtjgfyw.supabase.co/auth/v1/callback
   ```

6. **Save and Copy Credentials:**
   - Copy the Client ID
   - Copy the Client Secret

### Step 2: Configure Supabase

1. **Go to Supabase Dashboard:**
   - Visit: https://supabase.com/dashboard
   - Select your Bhookad project

2. **Navigate to Authentication:**
   - Click "Authentication" in left sidebar
   - Click "Providers" tab

3. **Enable Google Provider:**
   - Find "Google" in the list
   - Toggle "Enable" to ON

4. **Add Google Credentials:**
   - Paste Client ID from Google Cloud Console
   - Paste Client Secret from Google Cloud Console
   - Click "Save"

### Step 3: Test the Integration

1. **Restart your frontend:**
   ```bash
   cd bhookad-frontend
   npm run dev
   ```

2. **Test Google Login:**
   - Go to http://localhost:5174/login
   - Click "Continue with Google"
   - Should redirect to Google OAuth flow

### Step 4: Configure Site URL (Important!)

In Supabase Authentication > URL Configuration:
- Site URL: `http://localhost:5174`
- Redirect URLs: 
  - `http://localhost:5174/auth/callback`
  - `http://localhost:5174`

### Troubleshooting

**Error: "redirect_uri_mismatch"**
- Make sure the redirect URI in Google Cloud Console exactly matches:
  `https://xktprpguxerqyqtjgfyw.supabase.co/auth/v1/callback`

**Error: "unauthorized_client"**
- Check that Google+ API is enabled
- Verify Client ID and Secret are correct

**Error: "access_denied"**
- User cancelled the OAuth flow
- This is normal user behavior

### Alternative: Use Email/Password for Now

If you want to test the app immediately without setting up Google OAuth:
1. Go to http://localhost:5174/register
2. Choose your role (Vendor or Vlogger)
3. Fill the registration form with email/password
4. Use email/password login

The Google OAuth can be set up later when you're ready to deploy to production.

### Production Setup

For production deployment, you'll need to:
1. Update redirect URIs to your production domain
2. Update Site URL in Supabase to your production URL
3. Add production domain to Google Cloud Console authorized origins
