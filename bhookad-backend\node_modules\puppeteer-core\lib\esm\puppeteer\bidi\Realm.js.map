{"version": 3, "file": "Realm.js", "sourceRoot": "", "sources": ["../../../../src/bidi/Realm.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,OAAO,EAAC,KAAK,EAAC,MAAM,iBAAiB,CAAC;AACtC,OAAO,EAAC,gBAAgB,EAAC,MAAM,4BAA4B,CAAC;AAC5D,OAAO,EAAC,OAAO,EAAC,MAAM,sBAAsB,CAAC;AAC7C,OAAO,EAAC,cAAc,EAAC,MAAM,6BAA6B,CAAC;AAG3D,OAAO,EACL,UAAU,EACV,gCAAgC,EAChC,mBAAmB,EACnB,QAAQ,EACR,YAAY,EACZ,gBAAgB,GACjB,MAAM,mBAAmB,CAAC;AAE3B,OAAO,EAAC,iBAAiB,EAAC,MAAM,8BAA8B,CAAC;AAC/D,OAAO,EAAC,iBAAiB,EAAC,MAAM,qBAAqB,CAAC;AAQtD,OAAO,EAAC,gBAAgB,EAAC,MAAM,mBAAmB,CAAC;AACnD,OAAO,EAAC,iBAAiB,EAAC,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAC,iBAAiB,EAAC,MAAM,sBAAsB,CAAC;AAEvD,OAAO,EAAC,YAAY,EAAC,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAC,cAAc,EAAC,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAC,qBAAqB,EAAC,MAAM,WAAW,CAAC;AAGhD;;GAEG;AACH,MAAM,OAAgB,SAAU,SAAQ,KAAK;IAClC,KAAK,CAAgB;IAE9B,YAAY,KAAoB,EAAE,eAAgC;QAChE,KAAK,CAAC,eAAe,CAAC,CAAC;QACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAES,UAAU;QAClB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,EAAC,MAAM,EAAC,EAAE,EAAE;YACtC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;YACjD,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;YAC5B,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;YACvC,KAAK,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC;IAES,qBAAqB,CAAwC;IACvE,IAAI,aAAa;QACf,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,EAAsB,CAAC;QACtD,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC7B,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC/B,KAAK,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC5C,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC;gBACxB,CAAC,CAAC,CAAC;YACL,CAAC;YACD,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC7C,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAEhC,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAChC,OAAO,IAAI,CAAC,qBAA6D,CAAC;IAC5E,CAAC;IAEQ,KAAK,CAAC,cAAc,CAI3B,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC5D,CAAC;IAEQ,KAAK,CAAC,QAAQ,CAIrB,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC3D,CAAC;IAkBD,KAAK,CAAC,SAAS,CAIb,aAAsB,EACtB,YAA2B,EAC3B,GAAG,IAAY;QAEf,MAAM,gBAAgB,GAAG,mBAAmB,CAC1C,gCAAgC,CAAC,YAAY,CAAC,EAAE,QAAQ,EAAE;YACxD,YAAY,CAAC,YAAY,CAC5B,CAAC;QAEF,IAAI,eAAe,CAAC;QACpB,MAAM,eAAe,GAAG,aAAa;YACnC,CAAC;YACD,CAAC,8CAAiC,CAAC;QACrC,MAAM,oBAAoB,GAAqC,aAAa;YAC1E,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC;gBACE,cAAc,EAAE,CAAC;gBACjB,WAAW,EAAE,CAAC;aACf,CAAC;QACN,IAAI,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC;gBACpD,CAAC,CAAC,YAAY;gBACd,CAAC,CAAC,GAAG,YAAY,KAAK,gBAAgB,IAAI,CAAC;YAE7C,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE;gBACtD,eAAe;gBACf,cAAc,EAAE,IAAI;gBACpB,oBAAoB;aACrB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,mBAAmB,GAAG,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAC1D,mBAAmB,GAAG,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC;gBAC9D,CAAC,CAAC,mBAAmB;gBACrB,CAAC,CAAC,GAAG,mBAAmB,KAAK,gBAAgB,IAAI,CAAC;YACpD,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CACvC,mBAAmB;YACnB,mBAAmB,CAAC,IAAI,EACxB;gBACE,oEAAoE;gBACpE,sCAAsC;gBACtC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;oBACzB,OAAO,GAAG,YAAY,OAAO,CAAC;gBAChC,CAAC,CAAC;oBACA,CAAC,CAAC,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;wBACb,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;oBAClC,CAAC,CAAC,CACH;oBACH,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;wBACb,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;oBAC7B,CAAC,CAAC;gBACN,eAAe;gBACf,cAAc,EAAE,IAAI;gBACpB,oBAAoB;aACrB,CACF,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,eAAe,CAAC;QAErC,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;YACpD,MAAM,qBAAqB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,gBAAgB,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAErC,CAAC;IACJ,CAAC;IAED,YAAY,CACV,MAA+B;QAE/B,IACE,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,CAAC;YACpD,IAAI,YAAY,cAAc,EAC9B,CAAC;YACD,OAAO,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC9C,CAAC;QACD,OAAO,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,GAAY;QAC/B,IAAI,GAAG,YAAY,OAAO,EAAE,CAAC;YAC3B,GAAG,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;IAED,SAAS,CAAC,GAAY;QACpB,IAAI,GAAG,YAAY,YAAY,IAAI,GAAG,YAAY,iBAAiB,EAAE,CAAC;YACpE,IAAI,GAAG,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;gBACvB,IACE,CAAC,CAAC,GAAG,CAAC,KAAK,YAAY,cAAc,CAAC;oBACtC,CAAC,CAAC,IAAI,YAAY,cAAc,CAAC,EACjC,CAAC;oBACD,MAAM,IAAI,KAAK,CACb,0IAA0I,CAC3I,CAAC;gBACJ,CAAC;gBACD,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;oBAC/C,MAAM,IAAI,KAAK,CACb,8HAA8H,CAC/H,CAAC;gBACJ,CAAC;YACH,CAAC;YACD,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAC3C,CAAC;YACD,OAAO,GAAG,CAAC,WAAW,EAAiC,CAAC;QAC1D,CAAC;QAED,OAAO,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAAqC;QACxD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,OAAO;QACT,CAAC;QAED,MAAM,SAAS,GAAG,OAAO;aACtB,GAAG,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAE;YACZ,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC;aACD,MAAM,CAAC,CAAC,EAAE,EAAgB,EAAE;YAC3B,OAAO,EAAE,KAAK,SAAS,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEL,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,OAAO;QACT,CAAC;QAED,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YAC/C,sEAAsE;YACtE,iFAAiF;YACjF,UAAU,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC;IACL,CAAC;IAEQ,KAAK,CAAC,WAAW,CAA2B,MAAS;QAC5D,OAAO,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACvC,OAAO,IAAI,CAAC;QACd,CAAC,EAAE,MAAM,CAAC,CAAiB,CAAC;IAC9B,CAAC;IAEQ,KAAK,CAAC,cAAc,CAC3B,MAAS;QAET,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;YAC1B,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACnD,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;QACvB,OAAO,MAAM,iBAAiB,CAAC;IACjC,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,SAAS;IAC3C,MAAM,CAAC,IAAI,CAAC,KAAkB,EAAE,KAAgB;QAC9C,MAAM,UAAU,GAAG,IAAI,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACpD,UAAU,CAAC,WAAW,EAAE,CAAC;QACzB,OAAO,UAAU,CAAC;IACpB,CAAC;IAGQ,MAAM,CAAY;IAE3B,YAAoB,KAAkB,EAAE,KAAgB;QACtD,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;QACpC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAED,WAAW;QACT,KAAK,CAAC,UAAU,EAAE,CAAC;QAEnB,yBAAyB;QACzB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;YAC5B,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC;YACvC,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,kBAAkB,GAAG,KAAK,CAAC;IAC3B,IAAa,aAAa;QACxB,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,EAAsB,CAAC;QACpD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC;gBACpB,iBAAiB,CAAC,IAAI,CACpB,IAAI,CAAC,WAAwB,EAC7B,qBAAqB,EACrB,gBAAgB,CAAC,QAAQ,EACzB,CAAC,CAAC,IAAI,CAAC,OAAO,CACf;gBACD,iBAAiB,CAAC,IAAI,CACpB,IAAI,CAAC,WAAwB,EAC7B,wBAAwB,EACxB,KAAK,EACH,OAAgC,EAChC,QAAgB,EACW,EAAE;oBAC7B,MAAM,OAAO,GAAG,gBAAgB,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;oBAC7D,OAAO,MAAM,OAAO,CAAC,KAAK,CAAC,cAAc,CACvC,CAAC,GAAG,QAAQ,EAAE,EAAE;wBACd,OAAO,QAAQ,CAAC;oBAClB,CAAC,EACD,GAAG,CAAC,MAAM,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAC9C,CAAC;gBACJ,CAAC,EACD,CAAC,CAAC,IAAI,CAAC,OAAO,CACf;aACF,CAAC,CAAC;YACH,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACjC,CAAC;QACD,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE;YACvB,OAAO,KAAK,CAAC,aAAa,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAC5B,CAAC;IAED,IAAa,WAAW;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEQ,KAAK,CAAC,gBAAgB,CAC7B,aAAkC;;;YAElC,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBAChE,aAAa;gBACb,kBAAkB,EAAE,MAAM,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE;aACjE,CAAC,CAAC;YACH,MAAM,MAAM,kCAAG,iBAAiB,CAAC,IAAI,CACnC;gBACE,MAAM,EAAE,MAAM,CAAC,QAAQ;gBACvB,IAAI,EAAE,MAAM;aACb,EACD,IAAI,CACL,QAAA,CAAC;YACF,kEAAkE;YAClE,OAAO,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;gBAC3C,OAAO,OAAO,CAAC;YACjB,CAAC,CAAC,CAAC;;;;;;;;;KACJ;CACF;AAED;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,SAAS;IAC5C,MAAM,CAAC,IAAI,CACT,KAA+C,EAC/C,MAAqB;QAErB,MAAM,WAAW,GAAG,IAAI,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACvD,WAAW,CAAC,UAAU,EAAE,CAAC;QACzB,OAAO,WAAW,CAAC;IACrB,CAAC;IAGQ,OAAO,CAAgB;IAEhC,YACE,KAA+C,EAC/C,KAAoB;QAEpB,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC;QACpC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACvB,CAAC;IAED,IAAa,WAAW;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEQ,KAAK,CAAC,gBAAgB;QAC7B,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;IAC3D,CAAC;CACF"}