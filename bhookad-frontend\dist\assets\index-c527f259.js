(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();var Ie=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function zf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Df(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var s=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,s.get?s:{enumerable:!0,get:function(){return e[r]}})}),n}var Lc={exports:{}},ki={},$c={exports:{}},A={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var es=Symbol.for("react.element"),Uf=Symbol.for("react.portal"),Mf=Symbol.for("react.fragment"),Ff=Symbol.for("react.strict_mode"),Bf=Symbol.for("react.profiler"),Vf=Symbol.for("react.provider"),Hf=Symbol.for("react.context"),Wf=Symbol.for("react.forward_ref"),qf=Symbol.for("react.suspense"),Kf=Symbol.for("react.memo"),Gf=Symbol.for("react.lazy"),Yl=Symbol.iterator;function Qf(e){return e===null||typeof e!="object"?null:(e=Yl&&e[Yl]||e["@@iterator"],typeof e=="function"?e:null)}var Ac={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Ic=Object.assign,zc={};function qn(e,t,n){this.props=e,this.context=t,this.refs=zc,this.updater=n||Ac}qn.prototype.isReactComponent={};qn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};qn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Dc(){}Dc.prototype=qn.prototype;function Fa(e,t,n){this.props=e,this.context=t,this.refs=zc,this.updater=n||Ac}var Ba=Fa.prototype=new Dc;Ba.constructor=Fa;Ic(Ba,qn.prototype);Ba.isPureReactComponent=!0;var Xl=Array.isArray,Uc=Object.prototype.hasOwnProperty,Va={current:null},Mc={key:!0,ref:!0,__self:!0,__source:!0};function Fc(e,t,n){var r,s={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)Uc.call(t,r)&&!Mc.hasOwnProperty(r)&&(s[r]=t[r]);var l=arguments.length-2;if(l===1)s.children=n;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];s.children=u}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)s[r]===void 0&&(s[r]=l[r]);return{$$typeof:es,type:e,key:i,ref:o,props:s,_owner:Va.current}}function Jf(e,t){return{$$typeof:es,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ha(e){return typeof e=="object"&&e!==null&&e.$$typeof===es}function Yf(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Zl=/\/+/g;function Xi(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Yf(""+e.key):t.toString(36)}function $s(e,t,n,r,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case es:case Uf:o=!0}}if(o)return o=e,s=s(o),e=r===""?"."+Xi(o,0):r,Xl(s)?(n="",e!=null&&(n=e.replace(Zl,"$&/")+"/"),$s(s,t,n,"",function(c){return c})):s!=null&&(Ha(s)&&(s=Jf(s,n+(!s.key||o&&o.key===s.key?"":(""+s.key).replace(Zl,"$&/")+"/")+e)),t.push(s)),1;if(o=0,r=r===""?".":r+":",Xl(e))for(var l=0;l<e.length;l++){i=e[l];var u=r+Xi(i,l);o+=$s(i,t,n,u,s)}else if(u=Qf(e),typeof u=="function")for(e=u.call(e),l=0;!(i=e.next()).done;)i=i.value,u=r+Xi(i,l++),o+=$s(i,t,n,u,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function ds(e,t,n){if(e==null)return e;var r=[],s=0;return $s(e,r,"","",function(i){return t.call(n,i,s++)}),r}function Xf(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var me={current:null},As={transition:null},Zf={ReactCurrentDispatcher:me,ReactCurrentBatchConfig:As,ReactCurrentOwner:Va};function Bc(){throw Error("act(...) is not supported in production builds of React.")}A.Children={map:ds,forEach:function(e,t,n){ds(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ds(e,function(){t++}),t},toArray:function(e){return ds(e,function(t){return t})||[]},only:function(e){if(!Ha(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};A.Component=qn;A.Fragment=Mf;A.Profiler=Bf;A.PureComponent=Fa;A.StrictMode=Ff;A.Suspense=qf;A.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Zf;A.act=Bc;A.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Ic({},e.props),s=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=Va.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)Uc.call(t,u)&&!Mc.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&l!==void 0?l[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){l=Array(u);for(var c=0;c<u;c++)l[c]=arguments[c+2];r.children=l}return{$$typeof:es,type:e.type,key:s,ref:i,props:r,_owner:o}};A.createContext=function(e){return e={$$typeof:Hf,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Vf,_context:e},e.Consumer=e};A.createElement=Fc;A.createFactory=function(e){var t=Fc.bind(null,e);return t.type=e,t};A.createRef=function(){return{current:null}};A.forwardRef=function(e){return{$$typeof:Wf,render:e}};A.isValidElement=Ha;A.lazy=function(e){return{$$typeof:Gf,_payload:{_status:-1,_result:e},_init:Xf}};A.memo=function(e,t){return{$$typeof:Kf,type:e,compare:t===void 0?null:t}};A.startTransition=function(e){var t=As.transition;As.transition={};try{e()}finally{As.transition=t}};A.unstable_act=Bc;A.useCallback=function(e,t){return me.current.useCallback(e,t)};A.useContext=function(e){return me.current.useContext(e)};A.useDebugValue=function(){};A.useDeferredValue=function(e){return me.current.useDeferredValue(e)};A.useEffect=function(e,t){return me.current.useEffect(e,t)};A.useId=function(){return me.current.useId()};A.useImperativeHandle=function(e,t,n){return me.current.useImperativeHandle(e,t,n)};A.useInsertionEffect=function(e,t){return me.current.useInsertionEffect(e,t)};A.useLayoutEffect=function(e,t){return me.current.useLayoutEffect(e,t)};A.useMemo=function(e,t){return me.current.useMemo(e,t)};A.useReducer=function(e,t,n){return me.current.useReducer(e,t,n)};A.useRef=function(e){return me.current.useRef(e)};A.useState=function(e){return me.current.useState(e)};A.useSyncExternalStore=function(e,t,n){return me.current.useSyncExternalStore(e,t,n)};A.useTransition=function(){return me.current.useTransition()};A.version="18.3.1";$c.exports=A;var x=$c.exports;const Vc=zf(x);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ep=x,tp=Symbol.for("react.element"),np=Symbol.for("react.fragment"),rp=Object.prototype.hasOwnProperty,sp=ep.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ip={key:!0,ref:!0,__self:!0,__source:!0};function Hc(e,t,n){var r,s={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)rp.call(t,r)&&!ip.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:tp,type:e,key:i,ref:o,props:s,_owner:sp.current}}ki.Fragment=np;ki.jsx=Hc;ki.jsxs=Hc;Lc.exports=ki;var a=Lc.exports,$o={},Wc={exports:{}},Te={},qc={exports:{}},Kc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(P,O){var L=P.length;P.push(O);e:for(;0<L;){var J=L-1>>>1,te=P[J];if(0<s(te,O))P[J]=O,P[L]=te,L=J;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var O=P[0],L=P.pop();if(L!==O){P[0]=L;e:for(var J=0,te=P.length,us=te>>>1;J<us;){var qt=2*(J+1)-1,Yi=P[qt],Kt=qt+1,cs=P[Kt];if(0>s(Yi,L))Kt<te&&0>s(cs,Yi)?(P[J]=cs,P[Kt]=L,J=Kt):(P[J]=Yi,P[qt]=L,J=qt);else if(Kt<te&&0>s(cs,L))P[J]=cs,P[Kt]=L,J=Kt;else break e}}return O}function s(P,O){var L=P.sortIndex-O.sortIndex;return L!==0?L:P.id-O.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,l=o.now();e.unstable_now=function(){return o.now()-l}}var u=[],c=[],d=1,h=null,p=3,g=!1,w=!1,y=!1,k=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,f=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(P){for(var O=n(c);O!==null;){if(O.callback===null)r(c);else if(O.startTime<=P)r(c),O.sortIndex=O.expirationTime,t(u,O);else break;O=n(c)}}function _(P){if(y=!1,v(P),!w)if(n(u)!==null)w=!0,Qi(j);else{var O=n(c);O!==null&&Ji(_,O.startTime-P)}}function j(P,O){w=!1,y&&(y=!1,m(T),T=-1),g=!0;var L=p;try{for(v(O),h=n(u);h!==null&&(!(h.expirationTime>O)||P&&!ge());){var J=h.callback;if(typeof J=="function"){h.callback=null,p=h.priorityLevel;var te=J(h.expirationTime<=O);O=e.unstable_now(),typeof te=="function"?h.callback=te:h===n(u)&&r(u),v(O)}else r(u);h=n(u)}if(h!==null)var us=!0;else{var qt=n(c);qt!==null&&Ji(_,qt.startTime-O),us=!1}return us}finally{h=null,p=L,g=!1}}var E=!1,N=null,T=-1,I=5,b=-1;function ge(){return!(e.unstable_now()-b<I)}function tr(){if(N!==null){var P=e.unstable_now();b=P;var O=!0;try{O=N(!0,P)}finally{O?nr():(E=!1,N=null)}}else E=!1}var nr;if(typeof f=="function")nr=function(){f(tr)};else if(typeof MessageChannel<"u"){var Jl=new MessageChannel,If=Jl.port2;Jl.port1.onmessage=tr,nr=function(){If.postMessage(null)}}else nr=function(){k(tr,0)};function Qi(P){N=P,E||(E=!0,nr())}function Ji(P,O){T=k(function(){P(e.unstable_now())},O)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(P){P.callback=null},e.unstable_continueExecution=function(){w||g||(w=!0,Qi(j))},e.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):I=0<P?Math.floor(1e3/P):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(P){switch(p){case 1:case 2:case 3:var O=3;break;default:O=p}var L=p;p=O;try{return P()}finally{p=L}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(P,O){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var L=p;p=P;try{return O()}finally{p=L}},e.unstable_scheduleCallback=function(P,O,L){var J=e.unstable_now();switch(typeof L=="object"&&L!==null?(L=L.delay,L=typeof L=="number"&&0<L?J+L:J):L=J,P){case 1:var te=-1;break;case 2:te=250;break;case 5:te=**********;break;case 4:te=1e4;break;default:te=5e3}return te=L+te,P={id:d++,callback:O,priorityLevel:P,startTime:L,expirationTime:te,sortIndex:-1},L>J?(P.sortIndex=L,t(c,P),n(u)===null&&P===n(c)&&(y?(m(T),T=-1):y=!0,Ji(_,L-J))):(P.sortIndex=te,t(u,P),w||g||(w=!0,Qi(j))),P},e.unstable_shouldYield=ge,e.unstable_wrapCallback=function(P){var O=p;return function(){var L=p;p=O;try{return P.apply(this,arguments)}finally{p=L}}}})(Kc);qc.exports=Kc;var op=qc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ap=x,Pe=op;function S(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Gc=new Set,Lr={};function cn(e,t){Dn(e,t),Dn(e+"Capture",t)}function Dn(e,t){for(Lr[e]=t,e=0;e<t.length;e++)Gc.add(t[e])}var ft=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ao=Object.prototype.hasOwnProperty,lp=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,eu={},tu={};function up(e){return Ao.call(tu,e)?!0:Ao.call(eu,e)?!1:lp.test(e)?tu[e]=!0:(eu[e]=!0,!1)}function cp(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function dp(e,t,n,r){if(t===null||typeof t>"u"||cp(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ve(e,t,n,r,s,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var ae={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ae[e]=new ve(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ae[t]=new ve(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ae[e]=new ve(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ae[e]=new ve(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ae[e]=new ve(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ae[e]=new ve(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ae[e]=new ve(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ae[e]=new ve(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ae[e]=new ve(e,5,!1,e.toLowerCase(),null,!1,!1)});var Wa=/[\-:]([a-z])/g;function qa(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Wa,qa);ae[t]=new ve(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Wa,qa);ae[t]=new ve(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Wa,qa);ae[t]=new ve(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ae[e]=new ve(e,1,!1,e.toLowerCase(),null,!1,!1)});ae.xlinkHref=new ve("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ae[e]=new ve(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ka(e,t,n,r){var s=ae.hasOwnProperty(t)?ae[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(dp(t,n,s,r)&&(n=null),r||s===null?up(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var yt=ap.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,hs=Symbol.for("react.element"),xn=Symbol.for("react.portal"),_n=Symbol.for("react.fragment"),Ga=Symbol.for("react.strict_mode"),Io=Symbol.for("react.profiler"),Qc=Symbol.for("react.provider"),Jc=Symbol.for("react.context"),Qa=Symbol.for("react.forward_ref"),zo=Symbol.for("react.suspense"),Do=Symbol.for("react.suspense_list"),Ja=Symbol.for("react.memo"),kt=Symbol.for("react.lazy"),Yc=Symbol.for("react.offscreen"),nu=Symbol.iterator;function rr(e){return e===null||typeof e!="object"?null:(e=nu&&e[nu]||e["@@iterator"],typeof e=="function"?e:null)}var G=Object.assign,Zi;function pr(e){if(Zi===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Zi=t&&t[1]||""}return`
`+Zi+e}var eo=!1;function to(e,t){if(!e||eo)return"";eo=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var s=c.stack.split(`
`),i=r.stack.split(`
`),o=s.length-1,l=i.length-1;1<=o&&0<=l&&s[o]!==i[l];)l--;for(;1<=o&&0<=l;o--,l--)if(s[o]!==i[l]){if(o!==1||l!==1)do if(o--,l--,0>l||s[o]!==i[l]){var u=`
`+s[o].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=o&&0<=l);break}}}finally{eo=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?pr(e):""}function hp(e){switch(e.tag){case 5:return pr(e.type);case 16:return pr("Lazy");case 13:return pr("Suspense");case 19:return pr("SuspenseList");case 0:case 2:case 15:return e=to(e.type,!1),e;case 11:return e=to(e.type.render,!1),e;case 1:return e=to(e.type,!0),e;default:return""}}function Uo(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case _n:return"Fragment";case xn:return"Portal";case Io:return"Profiler";case Ga:return"StrictMode";case zo:return"Suspense";case Do:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Jc:return(e.displayName||"Context")+".Consumer";case Qc:return(e._context.displayName||"Context")+".Provider";case Qa:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ja:return t=e.displayName||null,t!==null?t:Uo(e.type)||"Memo";case kt:t=e._payload,e=e._init;try{return Uo(e(t))}catch{}}return null}function fp(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Uo(t);case 8:return t===Ga?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Dt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Xc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function pp(e){var t=Xc(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function fs(e){e._valueTracker||(e._valueTracker=pp(e))}function Zc(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Xc(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Gs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Mo(e,t){var n=t.checked;return G({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function ru(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Dt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function ed(e,t){t=t.checked,t!=null&&Ka(e,"checked",t,!1)}function Fo(e,t){ed(e,t);var n=Dt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Bo(e,t.type,n):t.hasOwnProperty("defaultValue")&&Bo(e,t.type,Dt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function su(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Bo(e,t,n){(t!=="number"||Gs(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var mr=Array.isArray;function On(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Dt(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Vo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(S(91));return G({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function iu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(S(92));if(mr(n)){if(1<n.length)throw Error(S(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Dt(n)}}function td(e,t){var n=Dt(t.value),r=Dt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function ou(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function nd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ho(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?nd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ps,rd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ps=ps||document.createElement("div"),ps.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ps.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function $r(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var xr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},mp=["Webkit","ms","Moz","O"];Object.keys(xr).forEach(function(e){mp.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),xr[t]=xr[e]})});function sd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||xr.hasOwnProperty(e)&&xr[e]?(""+t).trim():t+"px"}function id(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=sd(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var vp=G({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Wo(e,t){if(t){if(vp[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(S(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(S(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(S(61))}if(t.style!=null&&typeof t.style!="object")throw Error(S(62))}}function qo(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ko=null;function Ya(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Go=null,Ln=null,$n=null;function au(e){if(e=rs(e)){if(typeof Go!="function")throw Error(S(280));var t=e.stateNode;t&&(t=Ci(t),Go(e.stateNode,e.type,t))}}function od(e){Ln?$n?$n.push(e):$n=[e]:Ln=e}function ad(){if(Ln){var e=Ln,t=$n;if($n=Ln=null,au(e),t)for(e=0;e<t.length;e++)au(t[e])}}function ld(e,t){return e(t)}function ud(){}var no=!1;function cd(e,t,n){if(no)return e(t,n);no=!0;try{return ld(e,t,n)}finally{no=!1,(Ln!==null||$n!==null)&&(ud(),ad())}}function Ar(e,t){var n=e.stateNode;if(n===null)return null;var r=Ci(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(S(231,t,typeof n));return n}var Qo=!1;if(ft)try{var sr={};Object.defineProperty(sr,"passive",{get:function(){Qo=!0}}),window.addEventListener("test",sr,sr),window.removeEventListener("test",sr,sr)}catch{Qo=!1}function gp(e,t,n,r,s,i,o,l,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(d){this.onError(d)}}var _r=!1,Qs=null,Js=!1,Jo=null,yp={onError:function(e){_r=!0,Qs=e}};function wp(e,t,n,r,s,i,o,l,u){_r=!1,Qs=null,gp.apply(yp,arguments)}function xp(e,t,n,r,s,i,o,l,u){if(wp.apply(this,arguments),_r){if(_r){var c=Qs;_r=!1,Qs=null}else throw Error(S(198));Js||(Js=!0,Jo=c)}}function dn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function dd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function lu(e){if(dn(e)!==e)throw Error(S(188))}function _p(e){var t=e.alternate;if(!t){if(t=dn(e),t===null)throw Error(S(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var i=s.alternate;if(i===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===n)return lu(s),e;if(i===r)return lu(s),t;i=i.sibling}throw Error(S(188))}if(n.return!==r.return)n=s,r=i;else{for(var o=!1,l=s.child;l;){if(l===n){o=!0,n=s,r=i;break}if(l===r){o=!0,r=s,n=i;break}l=l.sibling}if(!o){for(l=i.child;l;){if(l===n){o=!0,n=i,r=s;break}if(l===r){o=!0,r=i,n=s;break}l=l.sibling}if(!o)throw Error(S(189))}}if(n.alternate!==r)throw Error(S(190))}if(n.tag!==3)throw Error(S(188));return n.stateNode.current===n?e:t}function hd(e){return e=_p(e),e!==null?fd(e):null}function fd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=fd(e);if(t!==null)return t;e=e.sibling}return null}var pd=Pe.unstable_scheduleCallback,uu=Pe.unstable_cancelCallback,kp=Pe.unstable_shouldYield,jp=Pe.unstable_requestPaint,Y=Pe.unstable_now,Sp=Pe.unstable_getCurrentPriorityLevel,Xa=Pe.unstable_ImmediatePriority,md=Pe.unstable_UserBlockingPriority,Ys=Pe.unstable_NormalPriority,Ep=Pe.unstable_LowPriority,vd=Pe.unstable_IdlePriority,ji=null,tt=null;function Np(e){if(tt&&typeof tt.onCommitFiberRoot=="function")try{tt.onCommitFiberRoot(ji,e,void 0,(e.current.flags&128)===128)}catch{}}var qe=Math.clz32?Math.clz32:Tp,Cp=Math.log,Pp=Math.LN2;function Tp(e){return e>>>=0,e===0?32:31-(Cp(e)/Pp|0)|0}var ms=64,vs=4194304;function vr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Xs(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var l=o&~s;l!==0?r=vr(l):(i&=o,i!==0&&(r=vr(i)))}else o=n&~s,o!==0?r=vr(o):i!==0&&(r=vr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-qe(t),s=1<<n,r|=e[n],t&=~s;return r}function bp(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Rp(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-qe(i),l=1<<o,u=s[o];u===-1?(!(l&n)||l&r)&&(s[o]=bp(l,t)):u<=t&&(e.expiredLanes|=l),i&=~l}}function Yo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function gd(){var e=ms;return ms<<=1,!(ms&4194240)&&(ms=64),e}function ro(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ts(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-qe(t),e[t]=n}function Op(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-qe(n),i=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~i}}function Za(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-qe(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var U=0;function yd(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var wd,el,xd,_d,kd,Xo=!1,gs=[],bt=null,Rt=null,Ot=null,Ir=new Map,zr=new Map,Et=[],Lp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function cu(e,t){switch(e){case"focusin":case"focusout":bt=null;break;case"dragenter":case"dragleave":Rt=null;break;case"mouseover":case"mouseout":Ot=null;break;case"pointerover":case"pointerout":Ir.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":zr.delete(t.pointerId)}}function ir(e,t,n,r,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[s]},t!==null&&(t=rs(t),t!==null&&el(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function $p(e,t,n,r,s){switch(t){case"focusin":return bt=ir(bt,e,t,n,r,s),!0;case"dragenter":return Rt=ir(Rt,e,t,n,r,s),!0;case"mouseover":return Ot=ir(Ot,e,t,n,r,s),!0;case"pointerover":var i=s.pointerId;return Ir.set(i,ir(Ir.get(i)||null,e,t,n,r,s)),!0;case"gotpointercapture":return i=s.pointerId,zr.set(i,ir(zr.get(i)||null,e,t,n,r,s)),!0}return!1}function jd(e){var t=Zt(e.target);if(t!==null){var n=dn(t);if(n!==null){if(t=n.tag,t===13){if(t=dd(n),t!==null){e.blockedOn=t,kd(e.priority,function(){xd(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Is(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Zo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ko=r,n.target.dispatchEvent(r),Ko=null}else return t=rs(n),t!==null&&el(t),e.blockedOn=n,!1;t.shift()}return!0}function du(e,t,n){Is(e)&&n.delete(t)}function Ap(){Xo=!1,bt!==null&&Is(bt)&&(bt=null),Rt!==null&&Is(Rt)&&(Rt=null),Ot!==null&&Is(Ot)&&(Ot=null),Ir.forEach(du),zr.forEach(du)}function or(e,t){e.blockedOn===t&&(e.blockedOn=null,Xo||(Xo=!0,Pe.unstable_scheduleCallback(Pe.unstable_NormalPriority,Ap)))}function Dr(e){function t(s){return or(s,e)}if(0<gs.length){or(gs[0],e);for(var n=1;n<gs.length;n++){var r=gs[n];r.blockedOn===e&&(r.blockedOn=null)}}for(bt!==null&&or(bt,e),Rt!==null&&or(Rt,e),Ot!==null&&or(Ot,e),Ir.forEach(t),zr.forEach(t),n=0;n<Et.length;n++)r=Et[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Et.length&&(n=Et[0],n.blockedOn===null);)jd(n),n.blockedOn===null&&Et.shift()}var An=yt.ReactCurrentBatchConfig,Zs=!0;function Ip(e,t,n,r){var s=U,i=An.transition;An.transition=null;try{U=1,tl(e,t,n,r)}finally{U=s,An.transition=i}}function zp(e,t,n,r){var s=U,i=An.transition;An.transition=null;try{U=4,tl(e,t,n,r)}finally{U=s,An.transition=i}}function tl(e,t,n,r){if(Zs){var s=Zo(e,t,n,r);if(s===null)po(e,t,r,ei,n),cu(e,r);else if($p(s,e,t,n,r))r.stopPropagation();else if(cu(e,r),t&4&&-1<Lp.indexOf(e)){for(;s!==null;){var i=rs(s);if(i!==null&&wd(i),i=Zo(e,t,n,r),i===null&&po(e,t,r,ei,n),i===s)break;s=i}s!==null&&r.stopPropagation()}else po(e,t,r,null,n)}}var ei=null;function Zo(e,t,n,r){if(ei=null,e=Ya(r),e=Zt(e),e!==null)if(t=dn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=dd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ei=e,null}function Sd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Sp()){case Xa:return 1;case md:return 4;case Ys:case Ep:return 16;case vd:return 536870912;default:return 16}default:return 16}}var Pt=null,nl=null,zs=null;function Ed(){if(zs)return zs;var e,t=nl,n=t.length,r,s="value"in Pt?Pt.value:Pt.textContent,i=s.length;for(e=0;e<n&&t[e]===s[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===s[i-r];r++);return zs=s.slice(e,1<r?1-r:void 0)}function Ds(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ys(){return!0}function hu(){return!1}function be(e){function t(n,r,s,i,o){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(i):i[l]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?ys:hu,this.isPropagationStopped=hu,this}return G(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ys)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ys)},persist:function(){},isPersistent:ys}),t}var Kn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},rl=be(Kn),ns=G({},Kn,{view:0,detail:0}),Dp=be(ns),so,io,ar,Si=G({},ns,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:sl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ar&&(ar&&e.type==="mousemove"?(so=e.screenX-ar.screenX,io=e.screenY-ar.screenY):io=so=0,ar=e),so)},movementY:function(e){return"movementY"in e?e.movementY:io}}),fu=be(Si),Up=G({},Si,{dataTransfer:0}),Mp=be(Up),Fp=G({},ns,{relatedTarget:0}),oo=be(Fp),Bp=G({},Kn,{animationName:0,elapsedTime:0,pseudoElement:0}),Vp=be(Bp),Hp=G({},Kn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Wp=be(Hp),qp=G({},Kn,{data:0}),pu=be(qp),Kp={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Gp={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Qp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Jp(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Qp[e])?!!t[e]:!1}function sl(){return Jp}var Yp=G({},ns,{key:function(e){if(e.key){var t=Kp[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ds(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Gp[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:sl,charCode:function(e){return e.type==="keypress"?Ds(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ds(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Xp=be(Yp),Zp=G({},Si,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),mu=be(Zp),em=G({},ns,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:sl}),tm=be(em),nm=G({},Kn,{propertyName:0,elapsedTime:0,pseudoElement:0}),rm=be(nm),sm=G({},Si,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),im=be(sm),om=[9,13,27,32],il=ft&&"CompositionEvent"in window,kr=null;ft&&"documentMode"in document&&(kr=document.documentMode);var am=ft&&"TextEvent"in window&&!kr,Nd=ft&&(!il||kr&&8<kr&&11>=kr),vu=String.fromCharCode(32),gu=!1;function Cd(e,t){switch(e){case"keyup":return om.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Pd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var kn=!1;function lm(e,t){switch(e){case"compositionend":return Pd(t);case"keypress":return t.which!==32?null:(gu=!0,vu);case"textInput":return e=t.data,e===vu&&gu?null:e;default:return null}}function um(e,t){if(kn)return e==="compositionend"||!il&&Cd(e,t)?(e=Ed(),zs=nl=Pt=null,kn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Nd&&t.locale!=="ko"?null:t.data;default:return null}}var cm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function yu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!cm[e.type]:t==="textarea"}function Td(e,t,n,r){od(r),t=ti(t,"onChange"),0<t.length&&(n=new rl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var jr=null,Ur=null;function dm(e){Md(e,0)}function Ei(e){var t=En(e);if(Zc(t))return e}function hm(e,t){if(e==="change")return t}var bd=!1;if(ft){var ao;if(ft){var lo="oninput"in document;if(!lo){var wu=document.createElement("div");wu.setAttribute("oninput","return;"),lo=typeof wu.oninput=="function"}ao=lo}else ao=!1;bd=ao&&(!document.documentMode||9<document.documentMode)}function xu(){jr&&(jr.detachEvent("onpropertychange",Rd),Ur=jr=null)}function Rd(e){if(e.propertyName==="value"&&Ei(Ur)){var t=[];Td(t,Ur,e,Ya(e)),cd(dm,t)}}function fm(e,t,n){e==="focusin"?(xu(),jr=t,Ur=n,jr.attachEvent("onpropertychange",Rd)):e==="focusout"&&xu()}function pm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ei(Ur)}function mm(e,t){if(e==="click")return Ei(t)}function vm(e,t){if(e==="input"||e==="change")return Ei(t)}function gm(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ge=typeof Object.is=="function"?Object.is:gm;function Mr(e,t){if(Ge(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!Ao.call(t,s)||!Ge(e[s],t[s]))return!1}return!0}function _u(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ku(e,t){var n=_u(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=_u(n)}}function Od(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Od(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ld(){for(var e=window,t=Gs();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Gs(e.document)}return t}function ol(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function ym(e){var t=Ld(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Od(n.ownerDocument.documentElement,n)){if(r!==null&&ol(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,i=Math.min(r.start,s);r=r.end===void 0?i:Math.min(r.end,s),!e.extend&&i>r&&(s=r,r=i,i=s),s=ku(n,i);var o=ku(n,r);s&&o&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var wm=ft&&"documentMode"in document&&11>=document.documentMode,jn=null,ea=null,Sr=null,ta=!1;function ju(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ta||jn==null||jn!==Gs(r)||(r=jn,"selectionStart"in r&&ol(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Sr&&Mr(Sr,r)||(Sr=r,r=ti(ea,"onSelect"),0<r.length&&(t=new rl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=jn)))}function ws(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sn={animationend:ws("Animation","AnimationEnd"),animationiteration:ws("Animation","AnimationIteration"),animationstart:ws("Animation","AnimationStart"),transitionend:ws("Transition","TransitionEnd")},uo={},$d={};ft&&($d=document.createElement("div").style,"AnimationEvent"in window||(delete Sn.animationend.animation,delete Sn.animationiteration.animation,delete Sn.animationstart.animation),"TransitionEvent"in window||delete Sn.transitionend.transition);function Ni(e){if(uo[e])return uo[e];if(!Sn[e])return e;var t=Sn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in $d)return uo[e]=t[n];return e}var Ad=Ni("animationend"),Id=Ni("animationiteration"),zd=Ni("animationstart"),Dd=Ni("transitionend"),Ud=new Map,Su="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ft(e,t){Ud.set(e,t),cn(t,[e])}for(var co=0;co<Su.length;co++){var ho=Su[co],xm=ho.toLowerCase(),_m=ho[0].toUpperCase()+ho.slice(1);Ft(xm,"on"+_m)}Ft(Ad,"onAnimationEnd");Ft(Id,"onAnimationIteration");Ft(zd,"onAnimationStart");Ft("dblclick","onDoubleClick");Ft("focusin","onFocus");Ft("focusout","onBlur");Ft(Dd,"onTransitionEnd");Dn("onMouseEnter",["mouseout","mouseover"]);Dn("onMouseLeave",["mouseout","mouseover"]);Dn("onPointerEnter",["pointerout","pointerover"]);Dn("onPointerLeave",["pointerout","pointerover"]);cn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));cn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));cn("onBeforeInput",["compositionend","keypress","textInput","paste"]);cn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));cn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));cn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var gr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),km=new Set("cancel close invalid load scroll toggle".split(" ").concat(gr));function Eu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,xp(r,t,void 0,e),e.currentTarget=null}function Md(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var l=r[o],u=l.instance,c=l.currentTarget;if(l=l.listener,u!==i&&s.isPropagationStopped())break e;Eu(s,l,c),i=u}else for(o=0;o<r.length;o++){if(l=r[o],u=l.instance,c=l.currentTarget,l=l.listener,u!==i&&s.isPropagationStopped())break e;Eu(s,l,c),i=u}}}if(Js)throw e=Jo,Js=!1,Jo=null,e}function B(e,t){var n=t[oa];n===void 0&&(n=t[oa]=new Set);var r=e+"__bubble";n.has(r)||(Fd(t,e,2,!1),n.add(r))}function fo(e,t,n){var r=0;t&&(r|=4),Fd(n,e,r,t)}var xs="_reactListening"+Math.random().toString(36).slice(2);function Fr(e){if(!e[xs]){e[xs]=!0,Gc.forEach(function(n){n!=="selectionchange"&&(km.has(n)||fo(n,!1,e),fo(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[xs]||(t[xs]=!0,fo("selectionchange",!1,t))}}function Fd(e,t,n,r){switch(Sd(t)){case 1:var s=Ip;break;case 4:s=zp;break;default:s=tl}n=s.bind(null,t,n,e),s=void 0,!Qo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function po(e,t,n,r,s){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var l=r.stateNode.containerInfo;if(l===s||l.nodeType===8&&l.parentNode===s)break;if(o===4)for(o=r.return;o!==null;){var u=o.tag;if((u===3||u===4)&&(u=o.stateNode.containerInfo,u===s||u.nodeType===8&&u.parentNode===s))return;o=o.return}for(;l!==null;){if(o=Zt(l),o===null)return;if(u=o.tag,u===5||u===6){r=i=o;continue e}l=l.parentNode}}r=r.return}cd(function(){var c=i,d=Ya(n),h=[];e:{var p=Ud.get(e);if(p!==void 0){var g=rl,w=e;switch(e){case"keypress":if(Ds(n)===0)break e;case"keydown":case"keyup":g=Xp;break;case"focusin":w="focus",g=oo;break;case"focusout":w="blur",g=oo;break;case"beforeblur":case"afterblur":g=oo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=fu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Mp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=tm;break;case Ad:case Id:case zd:g=Vp;break;case Dd:g=rm;break;case"scroll":g=Dp;break;case"wheel":g=im;break;case"copy":case"cut":case"paste":g=Wp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=mu}var y=(t&4)!==0,k=!y&&e==="scroll",m=y?p!==null?p+"Capture":null:p;y=[];for(var f=c,v;f!==null;){v=f;var _=v.stateNode;if(v.tag===5&&_!==null&&(v=_,m!==null&&(_=Ar(f,m),_!=null&&y.push(Br(f,_,v)))),k)break;f=f.return}0<y.length&&(p=new g(p,w,null,n,d),h.push({event:p,listeners:y}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",p&&n!==Ko&&(w=n.relatedTarget||n.fromElement)&&(Zt(w)||w[pt]))break e;if((g||p)&&(p=d.window===d?d:(p=d.ownerDocument)?p.defaultView||p.parentWindow:window,g?(w=n.relatedTarget||n.toElement,g=c,w=w?Zt(w):null,w!==null&&(k=dn(w),w!==k||w.tag!==5&&w.tag!==6)&&(w=null)):(g=null,w=c),g!==w)){if(y=fu,_="onMouseLeave",m="onMouseEnter",f="mouse",(e==="pointerout"||e==="pointerover")&&(y=mu,_="onPointerLeave",m="onPointerEnter",f="pointer"),k=g==null?p:En(g),v=w==null?p:En(w),p=new y(_,f+"leave",g,n,d),p.target=k,p.relatedTarget=v,_=null,Zt(d)===c&&(y=new y(m,f+"enter",w,n,d),y.target=v,y.relatedTarget=k,_=y),k=_,g&&w)t:{for(y=g,m=w,f=0,v=y;v;v=pn(v))f++;for(v=0,_=m;_;_=pn(_))v++;for(;0<f-v;)y=pn(y),f--;for(;0<v-f;)m=pn(m),v--;for(;f--;){if(y===m||m!==null&&y===m.alternate)break t;y=pn(y),m=pn(m)}y=null}else y=null;g!==null&&Nu(h,p,g,y,!1),w!==null&&k!==null&&Nu(h,k,w,y,!0)}}e:{if(p=c?En(c):window,g=p.nodeName&&p.nodeName.toLowerCase(),g==="select"||g==="input"&&p.type==="file")var j=hm;else if(yu(p))if(bd)j=vm;else{j=pm;var E=fm}else(g=p.nodeName)&&g.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(j=mm);if(j&&(j=j(e,c))){Td(h,j,n,d);break e}E&&E(e,p,c),e==="focusout"&&(E=p._wrapperState)&&E.controlled&&p.type==="number"&&Bo(p,"number",p.value)}switch(E=c?En(c):window,e){case"focusin":(yu(E)||E.contentEditable==="true")&&(jn=E,ea=c,Sr=null);break;case"focusout":Sr=ea=jn=null;break;case"mousedown":ta=!0;break;case"contextmenu":case"mouseup":case"dragend":ta=!1,ju(h,n,d);break;case"selectionchange":if(wm)break;case"keydown":case"keyup":ju(h,n,d)}var N;if(il)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else kn?Cd(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(Nd&&n.locale!=="ko"&&(kn||T!=="onCompositionStart"?T==="onCompositionEnd"&&kn&&(N=Ed()):(Pt=d,nl="value"in Pt?Pt.value:Pt.textContent,kn=!0)),E=ti(c,T),0<E.length&&(T=new pu(T,e,null,n,d),h.push({event:T,listeners:E}),N?T.data=N:(N=Pd(n),N!==null&&(T.data=N)))),(N=am?lm(e,n):um(e,n))&&(c=ti(c,"onBeforeInput"),0<c.length&&(d=new pu("onBeforeInput","beforeinput",null,n,d),h.push({event:d,listeners:c}),d.data=N))}Md(h,t)})}function Br(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ti(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=Ar(e,n),i!=null&&r.unshift(Br(e,i,s)),i=Ar(e,t),i!=null&&r.push(Br(e,i,s))),e=e.return}return r}function pn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Nu(e,t,n,r,s){for(var i=t._reactName,o=[];n!==null&&n!==r;){var l=n,u=l.alternate,c=l.stateNode;if(u!==null&&u===r)break;l.tag===5&&c!==null&&(l=c,s?(u=Ar(n,i),u!=null&&o.unshift(Br(n,u,l))):s||(u=Ar(n,i),u!=null&&o.push(Br(n,u,l)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var jm=/\r\n?/g,Sm=/\u0000|\uFFFD/g;function Cu(e){return(typeof e=="string"?e:""+e).replace(jm,`
`).replace(Sm,"")}function _s(e,t,n){if(t=Cu(t),Cu(e)!==t&&n)throw Error(S(425))}function ni(){}var na=null,ra=null;function sa(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ia=typeof setTimeout=="function"?setTimeout:void 0,Em=typeof clearTimeout=="function"?clearTimeout:void 0,Pu=typeof Promise=="function"?Promise:void 0,Nm=typeof queueMicrotask=="function"?queueMicrotask:typeof Pu<"u"?function(e){return Pu.resolve(null).then(e).catch(Cm)}:ia;function Cm(e){setTimeout(function(){throw e})}function mo(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),Dr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);Dr(t)}function Lt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Tu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Gn=Math.random().toString(36).slice(2),Ze="__reactFiber$"+Gn,Vr="__reactProps$"+Gn,pt="__reactContainer$"+Gn,oa="__reactEvents$"+Gn,Pm="__reactListeners$"+Gn,Tm="__reactHandles$"+Gn;function Zt(e){var t=e[Ze];if(t)return t;for(var n=e.parentNode;n;){if(t=n[pt]||n[Ze]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Tu(e);e!==null;){if(n=e[Ze])return n;e=Tu(e)}return t}e=n,n=e.parentNode}return null}function rs(e){return e=e[Ze]||e[pt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function En(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(S(33))}function Ci(e){return e[Vr]||null}var aa=[],Nn=-1;function Bt(e){return{current:e}}function V(e){0>Nn||(e.current=aa[Nn],aa[Nn]=null,Nn--)}function F(e,t){Nn++,aa[Nn]=e.current,e.current=t}var Ut={},he=Bt(Ut),ke=Bt(!1),sn=Ut;function Un(e,t){var n=e.type.contextTypes;if(!n)return Ut;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in n)s[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function je(e){return e=e.childContextTypes,e!=null}function ri(){V(ke),V(he)}function bu(e,t,n){if(he.current!==Ut)throw Error(S(168));F(he,t),F(ke,n)}function Bd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(S(108,fp(e)||"Unknown",s));return G({},n,r)}function si(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ut,sn=he.current,F(he,e),F(ke,ke.current),!0}function Ru(e,t,n){var r=e.stateNode;if(!r)throw Error(S(169));n?(e=Bd(e,t,sn),r.__reactInternalMemoizedMergedChildContext=e,V(ke),V(he),F(he,e)):V(ke),F(ke,n)}var lt=null,Pi=!1,vo=!1;function Vd(e){lt===null?lt=[e]:lt.push(e)}function bm(e){Pi=!0,Vd(e)}function Vt(){if(!vo&&lt!==null){vo=!0;var e=0,t=U;try{var n=lt;for(U=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}lt=null,Pi=!1}catch(s){throw lt!==null&&(lt=lt.slice(e+1)),pd(Xa,Vt),s}finally{U=t,vo=!1}}return null}var Cn=[],Pn=0,ii=null,oi=0,Oe=[],Le=0,on=null,ut=1,ct="";function Qt(e,t){Cn[Pn++]=oi,Cn[Pn++]=ii,ii=e,oi=t}function Hd(e,t,n){Oe[Le++]=ut,Oe[Le++]=ct,Oe[Le++]=on,on=e;var r=ut;e=ct;var s=32-qe(r)-1;r&=~(1<<s),n+=1;var i=32-qe(t)+s;if(30<i){var o=s-s%5;i=(r&(1<<o)-1).toString(32),r>>=o,s-=o,ut=1<<32-qe(t)+s|n<<s|r,ct=i+e}else ut=1<<i|n<<s|r,ct=e}function al(e){e.return!==null&&(Qt(e,1),Hd(e,1,0))}function ll(e){for(;e===ii;)ii=Cn[--Pn],Cn[Pn]=null,oi=Cn[--Pn],Cn[Pn]=null;for(;e===on;)on=Oe[--Le],Oe[Le]=null,ct=Oe[--Le],Oe[Le]=null,ut=Oe[--Le],Oe[Le]=null}var Ce=null,Ne=null,H=!1,He=null;function Wd(e,t){var n=$e(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Ou(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ce=e,Ne=Lt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ce=e,Ne=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=on!==null?{id:ut,overflow:ct}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=$e(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ce=e,Ne=null,!0):!1;default:return!1}}function la(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ua(e){if(H){var t=Ne;if(t){var n=t;if(!Ou(e,t)){if(la(e))throw Error(S(418));t=Lt(n.nextSibling);var r=Ce;t&&Ou(e,t)?Wd(r,n):(e.flags=e.flags&-4097|2,H=!1,Ce=e)}}else{if(la(e))throw Error(S(418));e.flags=e.flags&-4097|2,H=!1,Ce=e}}}function Lu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ce=e}function ks(e){if(e!==Ce)return!1;if(!H)return Lu(e),H=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!sa(e.type,e.memoizedProps)),t&&(t=Ne)){if(la(e))throw qd(),Error(S(418));for(;t;)Wd(e,t),t=Lt(t.nextSibling)}if(Lu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(S(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ne=Lt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ne=null}}else Ne=Ce?Lt(e.stateNode.nextSibling):null;return!0}function qd(){for(var e=Ne;e;)e=Lt(e.nextSibling)}function Mn(){Ne=Ce=null,H=!1}function ul(e){He===null?He=[e]:He.push(e)}var Rm=yt.ReactCurrentBatchConfig;function lr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(S(309));var r=n.stateNode}if(!r)throw Error(S(147,e));var s=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var l=s.refs;o===null?delete l[i]:l[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(S(284));if(!n._owner)throw Error(S(290,e))}return e}function js(e,t){throw e=Object.prototype.toString.call(t),Error(S(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function $u(e){var t=e._init;return t(e._payload)}function Kd(e){function t(m,f){if(e){var v=m.deletions;v===null?(m.deletions=[f],m.flags|=16):v.push(f)}}function n(m,f){if(!e)return null;for(;f!==null;)t(m,f),f=f.sibling;return null}function r(m,f){for(m=new Map;f!==null;)f.key!==null?m.set(f.key,f):m.set(f.index,f),f=f.sibling;return m}function s(m,f){return m=zt(m,f),m.index=0,m.sibling=null,m}function i(m,f,v){return m.index=v,e?(v=m.alternate,v!==null?(v=v.index,v<f?(m.flags|=2,f):v):(m.flags|=2,f)):(m.flags|=1048576,f)}function o(m){return e&&m.alternate===null&&(m.flags|=2),m}function l(m,f,v,_){return f===null||f.tag!==6?(f=jo(v,m.mode,_),f.return=m,f):(f=s(f,v),f.return=m,f)}function u(m,f,v,_){var j=v.type;return j===_n?d(m,f,v.props.children,_,v.key):f!==null&&(f.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===kt&&$u(j)===f.type)?(_=s(f,v.props),_.ref=lr(m,f,v),_.return=m,_):(_=Ws(v.type,v.key,v.props,null,m.mode,_),_.ref=lr(m,f,v),_.return=m,_)}function c(m,f,v,_){return f===null||f.tag!==4||f.stateNode.containerInfo!==v.containerInfo||f.stateNode.implementation!==v.implementation?(f=So(v,m.mode,_),f.return=m,f):(f=s(f,v.children||[]),f.return=m,f)}function d(m,f,v,_,j){return f===null||f.tag!==7?(f=rn(v,m.mode,_,j),f.return=m,f):(f=s(f,v),f.return=m,f)}function h(m,f,v){if(typeof f=="string"&&f!==""||typeof f=="number")return f=jo(""+f,m.mode,v),f.return=m,f;if(typeof f=="object"&&f!==null){switch(f.$$typeof){case hs:return v=Ws(f.type,f.key,f.props,null,m.mode,v),v.ref=lr(m,null,f),v.return=m,v;case xn:return f=So(f,m.mode,v),f.return=m,f;case kt:var _=f._init;return h(m,_(f._payload),v)}if(mr(f)||rr(f))return f=rn(f,m.mode,v,null),f.return=m,f;js(m,f)}return null}function p(m,f,v,_){var j=f!==null?f.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return j!==null?null:l(m,f,""+v,_);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case hs:return v.key===j?u(m,f,v,_):null;case xn:return v.key===j?c(m,f,v,_):null;case kt:return j=v._init,p(m,f,j(v._payload),_)}if(mr(v)||rr(v))return j!==null?null:d(m,f,v,_,null);js(m,v)}return null}function g(m,f,v,_,j){if(typeof _=="string"&&_!==""||typeof _=="number")return m=m.get(v)||null,l(f,m,""+_,j);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case hs:return m=m.get(_.key===null?v:_.key)||null,u(f,m,_,j);case xn:return m=m.get(_.key===null?v:_.key)||null,c(f,m,_,j);case kt:var E=_._init;return g(m,f,v,E(_._payload),j)}if(mr(_)||rr(_))return m=m.get(v)||null,d(f,m,_,j,null);js(f,_)}return null}function w(m,f,v,_){for(var j=null,E=null,N=f,T=f=0,I=null;N!==null&&T<v.length;T++){N.index>T?(I=N,N=null):I=N.sibling;var b=p(m,N,v[T],_);if(b===null){N===null&&(N=I);break}e&&N&&b.alternate===null&&t(m,N),f=i(b,f,T),E===null?j=b:E.sibling=b,E=b,N=I}if(T===v.length)return n(m,N),H&&Qt(m,T),j;if(N===null){for(;T<v.length;T++)N=h(m,v[T],_),N!==null&&(f=i(N,f,T),E===null?j=N:E.sibling=N,E=N);return H&&Qt(m,T),j}for(N=r(m,N);T<v.length;T++)I=g(N,m,T,v[T],_),I!==null&&(e&&I.alternate!==null&&N.delete(I.key===null?T:I.key),f=i(I,f,T),E===null?j=I:E.sibling=I,E=I);return e&&N.forEach(function(ge){return t(m,ge)}),H&&Qt(m,T),j}function y(m,f,v,_){var j=rr(v);if(typeof j!="function")throw Error(S(150));if(v=j.call(v),v==null)throw Error(S(151));for(var E=j=null,N=f,T=f=0,I=null,b=v.next();N!==null&&!b.done;T++,b=v.next()){N.index>T?(I=N,N=null):I=N.sibling;var ge=p(m,N,b.value,_);if(ge===null){N===null&&(N=I);break}e&&N&&ge.alternate===null&&t(m,N),f=i(ge,f,T),E===null?j=ge:E.sibling=ge,E=ge,N=I}if(b.done)return n(m,N),H&&Qt(m,T),j;if(N===null){for(;!b.done;T++,b=v.next())b=h(m,b.value,_),b!==null&&(f=i(b,f,T),E===null?j=b:E.sibling=b,E=b);return H&&Qt(m,T),j}for(N=r(m,N);!b.done;T++,b=v.next())b=g(N,m,T,b.value,_),b!==null&&(e&&b.alternate!==null&&N.delete(b.key===null?T:b.key),f=i(b,f,T),E===null?j=b:E.sibling=b,E=b);return e&&N.forEach(function(tr){return t(m,tr)}),H&&Qt(m,T),j}function k(m,f,v,_){if(typeof v=="object"&&v!==null&&v.type===_n&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case hs:e:{for(var j=v.key,E=f;E!==null;){if(E.key===j){if(j=v.type,j===_n){if(E.tag===7){n(m,E.sibling),f=s(E,v.props.children),f.return=m,m=f;break e}}else if(E.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===kt&&$u(j)===E.type){n(m,E.sibling),f=s(E,v.props),f.ref=lr(m,E,v),f.return=m,m=f;break e}n(m,E);break}else t(m,E);E=E.sibling}v.type===_n?(f=rn(v.props.children,m.mode,_,v.key),f.return=m,m=f):(_=Ws(v.type,v.key,v.props,null,m.mode,_),_.ref=lr(m,f,v),_.return=m,m=_)}return o(m);case xn:e:{for(E=v.key;f!==null;){if(f.key===E)if(f.tag===4&&f.stateNode.containerInfo===v.containerInfo&&f.stateNode.implementation===v.implementation){n(m,f.sibling),f=s(f,v.children||[]),f.return=m,m=f;break e}else{n(m,f);break}else t(m,f);f=f.sibling}f=So(v,m.mode,_),f.return=m,m=f}return o(m);case kt:return E=v._init,k(m,f,E(v._payload),_)}if(mr(v))return w(m,f,v,_);if(rr(v))return y(m,f,v,_);js(m,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,f!==null&&f.tag===6?(n(m,f.sibling),f=s(f,v),f.return=m,m=f):(n(m,f),f=jo(v,m.mode,_),f.return=m,m=f),o(m)):n(m,f)}return k}var Fn=Kd(!0),Gd=Kd(!1),ai=Bt(null),li=null,Tn=null,cl=null;function dl(){cl=Tn=li=null}function hl(e){var t=ai.current;V(ai),e._currentValue=t}function ca(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function In(e,t){li=e,cl=Tn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(_e=!0),e.firstContext=null)}function ze(e){var t=e._currentValue;if(cl!==e)if(e={context:e,memoizedValue:t,next:null},Tn===null){if(li===null)throw Error(S(308));Tn=e,li.dependencies={lanes:0,firstContext:e}}else Tn=Tn.next=e;return t}var en=null;function fl(e){en===null?en=[e]:en.push(e)}function Qd(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,fl(t)):(n.next=s.next,s.next=n),t.interleaved=n,mt(e,r)}function mt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var jt=!1;function pl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Jd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function dt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function $t(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,z&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,mt(e,n)}return s=r.interleaved,s===null?(t.next=t,fl(r)):(t.next=s.next,s.next=t),r.interleaved=t,mt(e,n)}function Us(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Za(e,n)}}function Au(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?s=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?s=i=t:i=i.next=t}else s=i=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ui(e,t,n,r){var s=e.updateQueue;jt=!1;var i=s.firstBaseUpdate,o=s.lastBaseUpdate,l=s.shared.pending;if(l!==null){s.shared.pending=null;var u=l,c=u.next;u.next=null,o===null?i=c:o.next=c,o=u;var d=e.alternate;d!==null&&(d=d.updateQueue,l=d.lastBaseUpdate,l!==o&&(l===null?d.firstBaseUpdate=c:l.next=c,d.lastBaseUpdate=u))}if(i!==null){var h=s.baseState;o=0,d=c=u=null,l=i;do{var p=l.lane,g=l.eventTime;if((r&p)===p){d!==null&&(d=d.next={eventTime:g,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var w=e,y=l;switch(p=t,g=n,y.tag){case 1:if(w=y.payload,typeof w=="function"){h=w.call(g,h,p);break e}h=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=y.payload,p=typeof w=="function"?w.call(g,h,p):w,p==null)break e;h=G({},h,p);break e;case 2:jt=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,p=s.effects,p===null?s.effects=[l]:p.push(l))}else g={eventTime:g,lane:p,tag:l.tag,payload:l.payload,callback:l.callback,next:null},d===null?(c=d=g,u=h):d=d.next=g,o|=p;if(l=l.next,l===null){if(l=s.shared.pending,l===null)break;p=l,l=p.next,p.next=null,s.lastBaseUpdate=p,s.shared.pending=null}}while(1);if(d===null&&(u=h),s.baseState=u,s.firstBaseUpdate=c,s.lastBaseUpdate=d,t=s.shared.interleaved,t!==null){s=t;do o|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);ln|=o,e.lanes=o,e.memoizedState=h}}function Iu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(S(191,s));s.call(r)}}}var ss={},nt=Bt(ss),Hr=Bt(ss),Wr=Bt(ss);function tn(e){if(e===ss)throw Error(S(174));return e}function ml(e,t){switch(F(Wr,t),F(Hr,e),F(nt,ss),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Ho(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Ho(t,e)}V(nt),F(nt,t)}function Bn(){V(nt),V(Hr),V(Wr)}function Yd(e){tn(Wr.current);var t=tn(nt.current),n=Ho(t,e.type);t!==n&&(F(Hr,e),F(nt,n))}function vl(e){Hr.current===e&&(V(nt),V(Hr))}var q=Bt(0);function ci(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var go=[];function gl(){for(var e=0;e<go.length;e++)go[e]._workInProgressVersionPrimary=null;go.length=0}var Ms=yt.ReactCurrentDispatcher,yo=yt.ReactCurrentBatchConfig,an=0,K=null,Z=null,re=null,di=!1,Er=!1,qr=0,Om=0;function le(){throw Error(S(321))}function yl(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ge(e[n],t[n]))return!1;return!0}function wl(e,t,n,r,s,i){if(an=i,K=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ms.current=e===null||e.memoizedState===null?Im:zm,e=n(r,s),Er){i=0;do{if(Er=!1,qr=0,25<=i)throw Error(S(301));i+=1,re=Z=null,t.updateQueue=null,Ms.current=Dm,e=n(r,s)}while(Er)}if(Ms.current=hi,t=Z!==null&&Z.next!==null,an=0,re=Z=K=null,di=!1,t)throw Error(S(300));return e}function xl(){var e=qr!==0;return qr=0,e}function Xe(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return re===null?K.memoizedState=re=e:re=re.next=e,re}function De(){if(Z===null){var e=K.alternate;e=e!==null?e.memoizedState:null}else e=Z.next;var t=re===null?K.memoizedState:re.next;if(t!==null)re=t,Z=e;else{if(e===null)throw Error(S(310));Z=e,e={memoizedState:Z.memoizedState,baseState:Z.baseState,baseQueue:Z.baseQueue,queue:Z.queue,next:null},re===null?K.memoizedState=re=e:re=re.next=e}return re}function Kr(e,t){return typeof t=="function"?t(e):t}function wo(e){var t=De(),n=t.queue;if(n===null)throw Error(S(311));n.lastRenderedReducer=e;var r=Z,s=r.baseQueue,i=n.pending;if(i!==null){if(s!==null){var o=s.next;s.next=i.next,i.next=o}r.baseQueue=s=i,n.pending=null}if(s!==null){i=s.next,r=r.baseState;var l=o=null,u=null,c=i;do{var d=c.lane;if((an&d)===d)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var h={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(l=u=h,o=r):u=u.next=h,K.lanes|=d,ln|=d}c=c.next}while(c!==null&&c!==i);u===null?o=r:u.next=l,Ge(r,t.memoizedState)||(_e=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do i=s.lane,K.lanes|=i,ln|=i,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function xo(e){var t=De(),n=t.queue;if(n===null)throw Error(S(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,i=t.memoizedState;if(s!==null){n.pending=null;var o=s=s.next;do i=e(i,o.action),o=o.next;while(o!==s);Ge(i,t.memoizedState)||(_e=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Xd(){}function Zd(e,t){var n=K,r=De(),s=t(),i=!Ge(r.memoizedState,s);if(i&&(r.memoizedState=s,_e=!0),r=r.queue,_l(nh.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||re!==null&&re.memoizedState.tag&1){if(n.flags|=2048,Gr(9,th.bind(null,n,r,s,t),void 0,null),se===null)throw Error(S(349));an&30||eh(n,t,s)}return s}function eh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function th(e,t,n,r){t.value=n,t.getSnapshot=r,rh(t)&&sh(e)}function nh(e,t,n){return n(function(){rh(t)&&sh(e)})}function rh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ge(e,n)}catch{return!0}}function sh(e){var t=mt(e,1);t!==null&&Ke(t,e,1,-1)}function zu(e){var t=Xe();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Kr,lastRenderedState:e},t.queue=e,e=e.dispatch=Am.bind(null,K,e),[t.memoizedState,e]}function Gr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function ih(){return De().memoizedState}function Fs(e,t,n,r){var s=Xe();K.flags|=e,s.memoizedState=Gr(1|t,n,void 0,r===void 0?null:r)}function Ti(e,t,n,r){var s=De();r=r===void 0?null:r;var i=void 0;if(Z!==null){var o=Z.memoizedState;if(i=o.destroy,r!==null&&yl(r,o.deps)){s.memoizedState=Gr(t,n,i,r);return}}K.flags|=e,s.memoizedState=Gr(1|t,n,i,r)}function Du(e,t){return Fs(8390656,8,e,t)}function _l(e,t){return Ti(2048,8,e,t)}function oh(e,t){return Ti(4,2,e,t)}function ah(e,t){return Ti(4,4,e,t)}function lh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function uh(e,t,n){return n=n!=null?n.concat([e]):null,Ti(4,4,lh.bind(null,t,e),n)}function kl(){}function ch(e,t){var n=De();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&yl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function dh(e,t){var n=De();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&yl(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function hh(e,t,n){return an&21?(Ge(n,t)||(n=gd(),K.lanes|=n,ln|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,_e=!0),e.memoizedState=n)}function Lm(e,t){var n=U;U=n!==0&&4>n?n:4,e(!0);var r=yo.transition;yo.transition={};try{e(!1),t()}finally{U=n,yo.transition=r}}function fh(){return De().memoizedState}function $m(e,t,n){var r=It(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},ph(e))mh(t,n);else if(n=Qd(e,t,n,r),n!==null){var s=pe();Ke(n,e,r,s),vh(n,t,r)}}function Am(e,t,n){var r=It(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(ph(e))mh(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,l=i(o,n);if(s.hasEagerState=!0,s.eagerState=l,Ge(l,o)){var u=t.interleaved;u===null?(s.next=s,fl(t)):(s.next=u.next,u.next=s),t.interleaved=s;return}}catch{}finally{}n=Qd(e,t,s,r),n!==null&&(s=pe(),Ke(n,e,r,s),vh(n,t,r))}}function ph(e){var t=e.alternate;return e===K||t!==null&&t===K}function mh(e,t){Er=di=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function vh(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Za(e,n)}}var hi={readContext:ze,useCallback:le,useContext:le,useEffect:le,useImperativeHandle:le,useInsertionEffect:le,useLayoutEffect:le,useMemo:le,useReducer:le,useRef:le,useState:le,useDebugValue:le,useDeferredValue:le,useTransition:le,useMutableSource:le,useSyncExternalStore:le,useId:le,unstable_isNewReconciler:!1},Im={readContext:ze,useCallback:function(e,t){return Xe().memoizedState=[e,t===void 0?null:t],e},useContext:ze,useEffect:Du,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Fs(4194308,4,lh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Fs(4194308,4,e,t)},useInsertionEffect:function(e,t){return Fs(4,2,e,t)},useMemo:function(e,t){var n=Xe();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Xe();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=$m.bind(null,K,e),[r.memoizedState,e]},useRef:function(e){var t=Xe();return e={current:e},t.memoizedState=e},useState:zu,useDebugValue:kl,useDeferredValue:function(e){return Xe().memoizedState=e},useTransition:function(){var e=zu(!1),t=e[0];return e=Lm.bind(null,e[1]),Xe().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=K,s=Xe();if(H){if(n===void 0)throw Error(S(407));n=n()}else{if(n=t(),se===null)throw Error(S(349));an&30||eh(r,t,n)}s.memoizedState=n;var i={value:n,getSnapshot:t};return s.queue=i,Du(nh.bind(null,r,i,e),[e]),r.flags|=2048,Gr(9,th.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Xe(),t=se.identifierPrefix;if(H){var n=ct,r=ut;n=(r&~(1<<32-qe(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=qr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Om++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},zm={readContext:ze,useCallback:ch,useContext:ze,useEffect:_l,useImperativeHandle:uh,useInsertionEffect:oh,useLayoutEffect:ah,useMemo:dh,useReducer:wo,useRef:ih,useState:function(){return wo(Kr)},useDebugValue:kl,useDeferredValue:function(e){var t=De();return hh(t,Z.memoizedState,e)},useTransition:function(){var e=wo(Kr)[0],t=De().memoizedState;return[e,t]},useMutableSource:Xd,useSyncExternalStore:Zd,useId:fh,unstable_isNewReconciler:!1},Dm={readContext:ze,useCallback:ch,useContext:ze,useEffect:_l,useImperativeHandle:uh,useInsertionEffect:oh,useLayoutEffect:ah,useMemo:dh,useReducer:xo,useRef:ih,useState:function(){return xo(Kr)},useDebugValue:kl,useDeferredValue:function(e){var t=De();return Z===null?t.memoizedState=e:hh(t,Z.memoizedState,e)},useTransition:function(){var e=xo(Kr)[0],t=De().memoizedState;return[e,t]},useMutableSource:Xd,useSyncExternalStore:Zd,useId:fh,unstable_isNewReconciler:!1};function Fe(e,t){if(e&&e.defaultProps){t=G({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function da(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:G({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var bi={isMounted:function(e){return(e=e._reactInternals)?dn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=pe(),s=It(e),i=dt(r,s);i.payload=t,n!=null&&(i.callback=n),t=$t(e,i,s),t!==null&&(Ke(t,e,s,r),Us(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=pe(),s=It(e),i=dt(r,s);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=$t(e,i,s),t!==null&&(Ke(t,e,s,r),Us(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=pe(),r=It(e),s=dt(n,r);s.tag=2,t!=null&&(s.callback=t),t=$t(e,s,r),t!==null&&(Ke(t,e,r,n),Us(t,e,r))}};function Uu(e,t,n,r,s,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!Mr(n,r)||!Mr(s,i):!0}function gh(e,t,n){var r=!1,s=Ut,i=t.contextType;return typeof i=="object"&&i!==null?i=ze(i):(s=je(t)?sn:he.current,r=t.contextTypes,i=(r=r!=null)?Un(e,s):Ut),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=bi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function Mu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&bi.enqueueReplaceState(t,t.state,null)}function ha(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},pl(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=ze(i):(i=je(t)?sn:he.current,s.context=Un(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(da(e,t,i,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&bi.enqueueReplaceState(s,s.state,null),ui(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function Vn(e,t){try{var n="",r=t;do n+=hp(r),r=r.return;while(r);var s=n}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function _o(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function fa(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Um=typeof WeakMap=="function"?WeakMap:Map;function yh(e,t,n){n=dt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){pi||(pi=!0,ja=r),fa(e,t)},n}function wh(e,t,n){n=dt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){fa(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){fa(e,t),typeof r!="function"&&(At===null?At=new Set([this]):At.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function Fu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Um;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=Zm.bind(null,e,t,n),t.then(e,e))}function Bu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Vu(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=dt(-1,1),t.tag=2,$t(n,t,1))),n.lanes|=1),e)}var Mm=yt.ReactCurrentOwner,_e=!1;function fe(e,t,n,r){t.child=e===null?Gd(t,null,n,r):Fn(t,e.child,n,r)}function Hu(e,t,n,r,s){n=n.render;var i=t.ref;return In(t,s),r=wl(e,t,n,r,i,s),n=xl(),e!==null&&!_e?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,vt(e,t,s)):(H&&n&&al(t),t.flags|=1,fe(e,t,r,s),t.child)}function Wu(e,t,n,r,s){if(e===null){var i=n.type;return typeof i=="function"&&!bl(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,xh(e,t,i,r,s)):(e=Ws(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:Mr,n(o,r)&&e.ref===t.ref)return vt(e,t,s)}return t.flags|=1,e=zt(i,r),e.ref=t.ref,e.return=t,t.child=e}function xh(e,t,n,r,s){if(e!==null){var i=e.memoizedProps;if(Mr(i,r)&&e.ref===t.ref)if(_e=!1,t.pendingProps=r=i,(e.lanes&s)!==0)e.flags&131072&&(_e=!0);else return t.lanes=e.lanes,vt(e,t,s)}return pa(e,t,n,r,s)}function _h(e,t,n){var r=t.pendingProps,s=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},F(Rn,Ee),Ee|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,F(Rn,Ee),Ee|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,F(Rn,Ee),Ee|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,F(Rn,Ee),Ee|=r;return fe(e,t,s,n),t.child}function kh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function pa(e,t,n,r,s){var i=je(n)?sn:he.current;return i=Un(t,i),In(t,s),n=wl(e,t,n,r,i,s),r=xl(),e!==null&&!_e?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,vt(e,t,s)):(H&&r&&al(t),t.flags|=1,fe(e,t,n,s),t.child)}function qu(e,t,n,r,s){if(je(n)){var i=!0;si(t)}else i=!1;if(In(t,s),t.stateNode===null)Bs(e,t),gh(t,n,r),ha(t,n,r,s),r=!0;else if(e===null){var o=t.stateNode,l=t.memoizedProps;o.props=l;var u=o.context,c=n.contextType;typeof c=="object"&&c!==null?c=ze(c):(c=je(n)?sn:he.current,c=Un(t,c));var d=n.getDerivedStateFromProps,h=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function";h||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==r||u!==c)&&Mu(t,o,r,c),jt=!1;var p=t.memoizedState;o.state=p,ui(t,r,o,s),u=t.memoizedState,l!==r||p!==u||ke.current||jt?(typeof d=="function"&&(da(t,n,d,r),u=t.memoizedState),(l=jt||Uu(t,n,l,r,p,u,c))?(h||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),o.props=r,o.state=u,o.context=c,r=l):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Jd(e,t),l=t.memoizedProps,c=t.type===t.elementType?l:Fe(t.type,l),o.props=c,h=t.pendingProps,p=o.context,u=n.contextType,typeof u=="object"&&u!==null?u=ze(u):(u=je(n)?sn:he.current,u=Un(t,u));var g=n.getDerivedStateFromProps;(d=typeof g=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==h||p!==u)&&Mu(t,o,r,u),jt=!1,p=t.memoizedState,o.state=p,ui(t,r,o,s);var w=t.memoizedState;l!==h||p!==w||ke.current||jt?(typeof g=="function"&&(da(t,n,g,r),w=t.memoizedState),(c=jt||Uu(t,n,c,r,p,w,u)||!1)?(d||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,w,u),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,w,u)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),o.props=r,o.state=w,o.context=u,r=c):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return ma(e,t,n,r,i,s)}function ma(e,t,n,r,s,i){kh(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return s&&Ru(t,n,!1),vt(e,t,i);r=t.stateNode,Mm.current=t;var l=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Fn(t,e.child,null,i),t.child=Fn(t,null,l,i)):fe(e,t,l,i),t.memoizedState=r.state,s&&Ru(t,n,!0),t.child}function jh(e){var t=e.stateNode;t.pendingContext?bu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&bu(e,t.context,!1),ml(e,t.containerInfo)}function Ku(e,t,n,r,s){return Mn(),ul(s),t.flags|=256,fe(e,t,n,r),t.child}var va={dehydrated:null,treeContext:null,retryLane:0};function ga(e){return{baseLanes:e,cachePool:null,transitions:null}}function Sh(e,t,n){var r=t.pendingProps,s=q.current,i=!1,o=(t.flags&128)!==0,l;if((l=o)||(l=e!==null&&e.memoizedState===null?!1:(s&2)!==0),l?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),F(q,s&1),e===null)return ua(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=Li(o,r,0,null),e=rn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=ga(n),t.memoizedState=va,e):jl(t,o));if(s=e.memoizedState,s!==null&&(l=s.dehydrated,l!==null))return Fm(e,t,o,r,l,s,n);if(i){i=r.fallback,o=t.mode,s=e.child,l=s.sibling;var u={mode:"hidden",children:r.children};return!(o&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=zt(s,u),r.subtreeFlags=s.subtreeFlags&14680064),l!==null?i=zt(l,i):(i=rn(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?ga(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=va,r}return i=e.child,e=i.sibling,r=zt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function jl(e,t){return t=Li({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ss(e,t,n,r){return r!==null&&ul(r),Fn(t,e.child,null,n),e=jl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Fm(e,t,n,r,s,i,o){if(n)return t.flags&256?(t.flags&=-257,r=_o(Error(S(422))),Ss(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,s=t.mode,r=Li({mode:"visible",children:r.children},s,0,null),i=rn(i,s,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Fn(t,e.child,null,o),t.child.memoizedState=ga(o),t.memoizedState=va,i);if(!(t.mode&1))return Ss(e,t,o,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var l=r.dgst;return r=l,i=Error(S(419)),r=_o(i,r,void 0),Ss(e,t,o,r)}if(l=(o&e.childLanes)!==0,_e||l){if(r=se,r!==null){switch(o&-o){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|o)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,mt(e,s),Ke(r,e,s,-1))}return Tl(),r=_o(Error(S(421))),Ss(e,t,o,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=ev.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,Ne=Lt(s.nextSibling),Ce=t,H=!0,He=null,e!==null&&(Oe[Le++]=ut,Oe[Le++]=ct,Oe[Le++]=on,ut=e.id,ct=e.overflow,on=t),t=jl(t,r.children),t.flags|=4096,t)}function Gu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ca(e.return,t,n)}function ko(e,t,n,r,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=s)}function Eh(e,t,n){var r=t.pendingProps,s=r.revealOrder,i=r.tail;if(fe(e,t,r.children,n),r=q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Gu(e,n,t);else if(e.tag===19)Gu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(F(q,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&ci(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),ko(t,!1,s,n,i);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&ci(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}ko(t,!0,n,null,i);break;case"together":ko(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Bs(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function vt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),ln|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(S(153));if(t.child!==null){for(e=t.child,n=zt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=zt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Bm(e,t,n){switch(t.tag){case 3:jh(t),Mn();break;case 5:Yd(t);break;case 1:je(t.type)&&si(t);break;case 4:ml(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;F(ai,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(F(q,q.current&1),t.flags|=128,null):n&t.child.childLanes?Sh(e,t,n):(F(q,q.current&1),e=vt(e,t,n),e!==null?e.sibling:null);F(q,q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Eh(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),F(q,q.current),r)break;return null;case 22:case 23:return t.lanes=0,_h(e,t,n)}return vt(e,t,n)}var Nh,ya,Ch,Ph;Nh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ya=function(){};Ch=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,tn(nt.current);var i=null;switch(n){case"input":s=Mo(e,s),r=Mo(e,r),i=[];break;case"select":s=G({},s,{value:void 0}),r=G({},r,{value:void 0}),i=[];break;case"textarea":s=Vo(e,s),r=Vo(e,r),i=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ni)}Wo(n,r);var o;n=null;for(c in s)if(!r.hasOwnProperty(c)&&s.hasOwnProperty(c)&&s[c]!=null)if(c==="style"){var l=s[c];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Lr.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in r){var u=r[c];if(l=s!=null?s[c]:void 0,r.hasOwnProperty(c)&&u!==l&&(u!=null||l!=null))if(c==="style")if(l){for(o in l)!l.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&l[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(i||(i=[]),i.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,l=l?l.__html:void 0,u!=null&&l!==u&&(i=i||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(i=i||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Lr.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&B("scroll",e),i||l===u||(i=[])):(i=i||[]).push(c,u))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}};Ph=function(e,t,n,r){n!==r&&(t.flags|=4)};function ur(e,t){if(!H)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ue(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Vm(e,t,n){var r=t.pendingProps;switch(ll(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ue(t),null;case 1:return je(t.type)&&ri(),ue(t),null;case 3:return r=t.stateNode,Bn(),V(ke),V(he),gl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(ks(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,He!==null&&(Na(He),He=null))),ya(e,t),ue(t),null;case 5:vl(t);var s=tn(Wr.current);if(n=t.type,e!==null&&t.stateNode!=null)Ch(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(S(166));return ue(t),null}if(e=tn(nt.current),ks(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Ze]=t,r[Vr]=i,e=(t.mode&1)!==0,n){case"dialog":B("cancel",r),B("close",r);break;case"iframe":case"object":case"embed":B("load",r);break;case"video":case"audio":for(s=0;s<gr.length;s++)B(gr[s],r);break;case"source":B("error",r);break;case"img":case"image":case"link":B("error",r),B("load",r);break;case"details":B("toggle",r);break;case"input":ru(r,i),B("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},B("invalid",r);break;case"textarea":iu(r,i),B("invalid",r)}Wo(n,i),s=null;for(var o in i)if(i.hasOwnProperty(o)){var l=i[o];o==="children"?typeof l=="string"?r.textContent!==l&&(i.suppressHydrationWarning!==!0&&_s(r.textContent,l,e),s=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(i.suppressHydrationWarning!==!0&&_s(r.textContent,l,e),s=["children",""+l]):Lr.hasOwnProperty(o)&&l!=null&&o==="onScroll"&&B("scroll",r)}switch(n){case"input":fs(r),su(r,i,!0);break;case"textarea":fs(r),ou(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=ni)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=nd(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[Ze]=t,e[Vr]=r,Nh(e,t,!1,!1),t.stateNode=e;e:{switch(o=qo(n,r),n){case"dialog":B("cancel",e),B("close",e),s=r;break;case"iframe":case"object":case"embed":B("load",e),s=r;break;case"video":case"audio":for(s=0;s<gr.length;s++)B(gr[s],e);s=r;break;case"source":B("error",e),s=r;break;case"img":case"image":case"link":B("error",e),B("load",e),s=r;break;case"details":B("toggle",e),s=r;break;case"input":ru(e,r),s=Mo(e,r),B("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=G({},r,{value:void 0}),B("invalid",e);break;case"textarea":iu(e,r),s=Vo(e,r),B("invalid",e);break;default:s=r}Wo(n,s),l=s;for(i in l)if(l.hasOwnProperty(i)){var u=l[i];i==="style"?id(e,u):i==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&rd(e,u)):i==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&$r(e,u):typeof u=="number"&&$r(e,""+u):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Lr.hasOwnProperty(i)?u!=null&&i==="onScroll"&&B("scroll",e):u!=null&&Ka(e,i,u,o))}switch(n){case"input":fs(e),su(e,r,!1);break;case"textarea":fs(e),ou(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Dt(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?On(e,!!r.multiple,i,!1):r.defaultValue!=null&&On(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=ni)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ue(t),null;case 6:if(e&&t.stateNode!=null)Ph(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(S(166));if(n=tn(Wr.current),tn(nt.current),ks(t)){if(r=t.stateNode,n=t.memoizedProps,r[Ze]=t,(i=r.nodeValue!==n)&&(e=Ce,e!==null))switch(e.tag){case 3:_s(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&_s(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Ze]=t,t.stateNode=r}return ue(t),null;case 13:if(V(q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(H&&Ne!==null&&t.mode&1&&!(t.flags&128))qd(),Mn(),t.flags|=98560,i=!1;else if(i=ks(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(S(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(S(317));i[Ze]=t}else Mn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ue(t),i=!1}else He!==null&&(Na(He),He=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||q.current&1?ee===0&&(ee=3):Tl())),t.updateQueue!==null&&(t.flags|=4),ue(t),null);case 4:return Bn(),ya(e,t),e===null&&Fr(t.stateNode.containerInfo),ue(t),null;case 10:return hl(t.type._context),ue(t),null;case 17:return je(t.type)&&ri(),ue(t),null;case 19:if(V(q),i=t.memoizedState,i===null)return ue(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)ur(i,!1);else{if(ee!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=ci(e),o!==null){for(t.flags|=128,ur(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return F(q,q.current&1|2),t.child}e=e.sibling}i.tail!==null&&Y()>Hn&&(t.flags|=128,r=!0,ur(i,!1),t.lanes=4194304)}else{if(!r)if(e=ci(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),ur(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!H)return ue(t),null}else 2*Y()-i.renderingStartTime>Hn&&n!==1073741824&&(t.flags|=128,r=!0,ur(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Y(),t.sibling=null,n=q.current,F(q,r?n&1|2:n&1),t):(ue(t),null);case 22:case 23:return Pl(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ee&1073741824&&(ue(t),t.subtreeFlags&6&&(t.flags|=8192)):ue(t),null;case 24:return null;case 25:return null}throw Error(S(156,t.tag))}function Hm(e,t){switch(ll(t),t.tag){case 1:return je(t.type)&&ri(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Bn(),V(ke),V(he),gl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return vl(t),null;case 13:if(V(q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(S(340));Mn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return V(q),null;case 4:return Bn(),null;case 10:return hl(t.type._context),null;case 22:case 23:return Pl(),null;case 24:return null;default:return null}}var Es=!1,de=!1,Wm=typeof WeakSet=="function"?WeakSet:Set,C=null;function bn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Q(e,t,r)}else n.current=null}function wa(e,t,n){try{n()}catch(r){Q(e,t,r)}}var Qu=!1;function qm(e,t){if(na=Zs,e=Ld(),ol(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,l=-1,u=-1,c=0,d=0,h=e,p=null;t:for(;;){for(var g;h!==n||s!==0&&h.nodeType!==3||(l=o+s),h!==i||r!==0&&h.nodeType!==3||(u=o+r),h.nodeType===3&&(o+=h.nodeValue.length),(g=h.firstChild)!==null;)p=h,h=g;for(;;){if(h===e)break t;if(p===n&&++c===s&&(l=o),p===i&&++d===r&&(u=o),(g=h.nextSibling)!==null)break;h=p,p=h.parentNode}h=g}n=l===-1||u===-1?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ra={focusedElem:e,selectionRange:n},Zs=!1,C=t;C!==null;)if(t=C,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,C=e;else for(;C!==null;){t=C;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var y=w.memoizedProps,k=w.memoizedState,m=t.stateNode,f=m.getSnapshotBeforeUpdate(t.elementType===t.type?y:Fe(t.type,y),k);m.__reactInternalSnapshotBeforeUpdate=f}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(S(163))}}catch(_){Q(t,t.return,_)}if(e=t.sibling,e!==null){e.return=t.return,C=e;break}C=t.return}return w=Qu,Qu=!1,w}function Nr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&wa(t,n,i)}s=s.next}while(s!==r)}}function Ri(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function xa(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Th(e){var t=e.alternate;t!==null&&(e.alternate=null,Th(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ze],delete t[Vr],delete t[oa],delete t[Pm],delete t[Tm])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function bh(e){return e.tag===5||e.tag===3||e.tag===4}function Ju(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||bh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function _a(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ni));else if(r!==4&&(e=e.child,e!==null))for(_a(e,t,n),e=e.sibling;e!==null;)_a(e,t,n),e=e.sibling}function ka(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ka(e,t,n),e=e.sibling;e!==null;)ka(e,t,n),e=e.sibling}var ie=null,Be=!1;function xt(e,t,n){for(n=n.child;n!==null;)Rh(e,t,n),n=n.sibling}function Rh(e,t,n){if(tt&&typeof tt.onCommitFiberUnmount=="function")try{tt.onCommitFiberUnmount(ji,n)}catch{}switch(n.tag){case 5:de||bn(n,t);case 6:var r=ie,s=Be;ie=null,xt(e,t,n),ie=r,Be=s,ie!==null&&(Be?(e=ie,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ie.removeChild(n.stateNode));break;case 18:ie!==null&&(Be?(e=ie,n=n.stateNode,e.nodeType===8?mo(e.parentNode,n):e.nodeType===1&&mo(e,n),Dr(e)):mo(ie,n.stateNode));break;case 4:r=ie,s=Be,ie=n.stateNode.containerInfo,Be=!0,xt(e,t,n),ie=r,Be=s;break;case 0:case 11:case 14:case 15:if(!de&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var i=s,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&wa(n,t,o),s=s.next}while(s!==r)}xt(e,t,n);break;case 1:if(!de&&(bn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Q(n,t,l)}xt(e,t,n);break;case 21:xt(e,t,n);break;case 22:n.mode&1?(de=(r=de)||n.memoizedState!==null,xt(e,t,n),de=r):xt(e,t,n);break;default:xt(e,t,n)}}function Yu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Wm),t.forEach(function(r){var s=tv.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function Ue(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var i=e,o=t,l=o;e:for(;l!==null;){switch(l.tag){case 5:ie=l.stateNode,Be=!1;break e;case 3:ie=l.stateNode.containerInfo,Be=!0;break e;case 4:ie=l.stateNode.containerInfo,Be=!0;break e}l=l.return}if(ie===null)throw Error(S(160));Rh(i,o,s),ie=null,Be=!1;var u=s.alternate;u!==null&&(u.return=null),s.return=null}catch(c){Q(s,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Oh(t,e),t=t.sibling}function Oh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ue(t,e),Ye(e),r&4){try{Nr(3,e,e.return),Ri(3,e)}catch(y){Q(e,e.return,y)}try{Nr(5,e,e.return)}catch(y){Q(e,e.return,y)}}break;case 1:Ue(t,e),Ye(e),r&512&&n!==null&&bn(n,n.return);break;case 5:if(Ue(t,e),Ye(e),r&512&&n!==null&&bn(n,n.return),e.flags&32){var s=e.stateNode;try{$r(s,"")}catch(y){Q(e,e.return,y)}}if(r&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,l=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{l==="input"&&i.type==="radio"&&i.name!=null&&ed(s,i),qo(l,o);var c=qo(l,i);for(o=0;o<u.length;o+=2){var d=u[o],h=u[o+1];d==="style"?id(s,h):d==="dangerouslySetInnerHTML"?rd(s,h):d==="children"?$r(s,h):Ka(s,d,h,c)}switch(l){case"input":Fo(s,i);break;case"textarea":td(s,i);break;case"select":var p=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var g=i.value;g!=null?On(s,!!i.multiple,g,!1):p!==!!i.multiple&&(i.defaultValue!=null?On(s,!!i.multiple,i.defaultValue,!0):On(s,!!i.multiple,i.multiple?[]:"",!1))}s[Vr]=i}catch(y){Q(e,e.return,y)}}break;case 6:if(Ue(t,e),Ye(e),r&4){if(e.stateNode===null)throw Error(S(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(y){Q(e,e.return,y)}}break;case 3:if(Ue(t,e),Ye(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Dr(t.containerInfo)}catch(y){Q(e,e.return,y)}break;case 4:Ue(t,e),Ye(e);break;case 13:Ue(t,e),Ye(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(Nl=Y())),r&4&&Yu(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(de=(c=de)||d,Ue(t,e),de=c):Ue(t,e),Ye(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!d&&e.mode&1)for(C=e,d=e.child;d!==null;){for(h=C=d;C!==null;){switch(p=C,g=p.child,p.tag){case 0:case 11:case 14:case 15:Nr(4,p,p.return);break;case 1:bn(p,p.return);var w=p.stateNode;if(typeof w.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(y){Q(r,n,y)}}break;case 5:bn(p,p.return);break;case 22:if(p.memoizedState!==null){Zu(h);continue}}g!==null?(g.return=p,C=g):Zu(h)}d=d.sibling}e:for(d=null,h=e;;){if(h.tag===5){if(d===null){d=h;try{s=h.stateNode,c?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(l=h.stateNode,u=h.memoizedProps.style,o=u!=null&&u.hasOwnProperty("display")?u.display:null,l.style.display=sd("display",o))}catch(y){Q(e,e.return,y)}}}else if(h.tag===6){if(d===null)try{h.stateNode.nodeValue=c?"":h.memoizedProps}catch(y){Q(e,e.return,y)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;d===h&&(d=null),h=h.return}d===h&&(d=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:Ue(t,e),Ye(e),r&4&&Yu(e);break;case 21:break;default:Ue(t,e),Ye(e)}}function Ye(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(bh(n)){var r=n;break e}n=n.return}throw Error(S(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&($r(s,""),r.flags&=-33);var i=Ju(e);ka(e,i,s);break;case 3:case 4:var o=r.stateNode.containerInfo,l=Ju(e);_a(e,l,o);break;default:throw Error(S(161))}}catch(u){Q(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Km(e,t,n){C=e,Lh(e)}function Lh(e,t,n){for(var r=(e.mode&1)!==0;C!==null;){var s=C,i=s.child;if(s.tag===22&&r){var o=s.memoizedState!==null||Es;if(!o){var l=s.alternate,u=l!==null&&l.memoizedState!==null||de;l=Es;var c=de;if(Es=o,(de=u)&&!c)for(C=s;C!==null;)o=C,u=o.child,o.tag===22&&o.memoizedState!==null?ec(s):u!==null?(u.return=o,C=u):ec(s);for(;i!==null;)C=i,Lh(i),i=i.sibling;C=s,Es=l,de=c}Xu(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,C=i):Xu(e)}}function Xu(e){for(;C!==null;){var t=C;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:de||Ri(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!de)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:Fe(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Iu(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Iu(t,o,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var d=c.memoizedState;if(d!==null){var h=d.dehydrated;h!==null&&Dr(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(S(163))}de||t.flags&512&&xa(t)}catch(p){Q(t,t.return,p)}}if(t===e){C=null;break}if(n=t.sibling,n!==null){n.return=t.return,C=n;break}C=t.return}}function Zu(e){for(;C!==null;){var t=C;if(t===e){C=null;break}var n=t.sibling;if(n!==null){n.return=t.return,C=n;break}C=t.return}}function ec(e){for(;C!==null;){var t=C;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ri(4,t)}catch(u){Q(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(u){Q(t,s,u)}}var i=t.return;try{xa(t)}catch(u){Q(t,i,u)}break;case 5:var o=t.return;try{xa(t)}catch(u){Q(t,o,u)}}}catch(u){Q(t,t.return,u)}if(t===e){C=null;break}var l=t.sibling;if(l!==null){l.return=t.return,C=l;break}C=t.return}}var Gm=Math.ceil,fi=yt.ReactCurrentDispatcher,Sl=yt.ReactCurrentOwner,Ae=yt.ReactCurrentBatchConfig,z=0,se=null,X=null,oe=0,Ee=0,Rn=Bt(0),ee=0,Qr=null,ln=0,Oi=0,El=0,Cr=null,we=null,Nl=0,Hn=1/0,ot=null,pi=!1,ja=null,At=null,Ns=!1,Tt=null,mi=0,Pr=0,Sa=null,Vs=-1,Hs=0;function pe(){return z&6?Y():Vs!==-1?Vs:Vs=Y()}function It(e){return e.mode&1?z&2&&oe!==0?oe&-oe:Rm.transition!==null?(Hs===0&&(Hs=gd()),Hs):(e=U,e!==0||(e=window.event,e=e===void 0?16:Sd(e.type)),e):1}function Ke(e,t,n,r){if(50<Pr)throw Pr=0,Sa=null,Error(S(185));ts(e,n,r),(!(z&2)||e!==se)&&(e===se&&(!(z&2)&&(Oi|=n),ee===4&&Nt(e,oe)),Se(e,r),n===1&&z===0&&!(t.mode&1)&&(Hn=Y()+500,Pi&&Vt()))}function Se(e,t){var n=e.callbackNode;Rp(e,t);var r=Xs(e,e===se?oe:0);if(r===0)n!==null&&uu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&uu(n),t===1)e.tag===0?bm(tc.bind(null,e)):Vd(tc.bind(null,e)),Nm(function(){!(z&6)&&Vt()}),n=null;else{switch(yd(r)){case 1:n=Xa;break;case 4:n=md;break;case 16:n=Ys;break;case 536870912:n=vd;break;default:n=Ys}n=Fh(n,$h.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function $h(e,t){if(Vs=-1,Hs=0,z&6)throw Error(S(327));var n=e.callbackNode;if(zn()&&e.callbackNode!==n)return null;var r=Xs(e,e===se?oe:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=vi(e,r);else{t=r;var s=z;z|=2;var i=Ih();(se!==e||oe!==t)&&(ot=null,Hn=Y()+500,nn(e,t));do try{Ym();break}catch(l){Ah(e,l)}while(1);dl(),fi.current=i,z=s,X!==null?t=0:(se=null,oe=0,t=ee)}if(t!==0){if(t===2&&(s=Yo(e),s!==0&&(r=s,t=Ea(e,s))),t===1)throw n=Qr,nn(e,0),Nt(e,r),Se(e,Y()),n;if(t===6)Nt(e,r);else{if(s=e.current.alternate,!(r&30)&&!Qm(s)&&(t=vi(e,r),t===2&&(i=Yo(e),i!==0&&(r=i,t=Ea(e,i))),t===1))throw n=Qr,nn(e,0),Nt(e,r),Se(e,Y()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(S(345));case 2:Jt(e,we,ot);break;case 3:if(Nt(e,r),(r&130023424)===r&&(t=Nl+500-Y(),10<t)){if(Xs(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){pe(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=ia(Jt.bind(null,e,we,ot),t);break}Jt(e,we,ot);break;case 4:if(Nt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var o=31-qe(r);i=1<<o,o=t[o],o>s&&(s=o),r&=~i}if(r=s,r=Y()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Gm(r/1960))-r,10<r){e.timeoutHandle=ia(Jt.bind(null,e,we,ot),r);break}Jt(e,we,ot);break;case 5:Jt(e,we,ot);break;default:throw Error(S(329))}}}return Se(e,Y()),e.callbackNode===n?$h.bind(null,e):null}function Ea(e,t){var n=Cr;return e.current.memoizedState.isDehydrated&&(nn(e,t).flags|=256),e=vi(e,t),e!==2&&(t=we,we=n,t!==null&&Na(t)),e}function Na(e){we===null?we=e:we.push.apply(we,e)}function Qm(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],i=s.getSnapshot;s=s.value;try{if(!Ge(i(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Nt(e,t){for(t&=~El,t&=~Oi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-qe(t),r=1<<n;e[n]=-1,t&=~r}}function tc(e){if(z&6)throw Error(S(327));zn();var t=Xs(e,0);if(!(t&1))return Se(e,Y()),null;var n=vi(e,t);if(e.tag!==0&&n===2){var r=Yo(e);r!==0&&(t=r,n=Ea(e,r))}if(n===1)throw n=Qr,nn(e,0),Nt(e,t),Se(e,Y()),n;if(n===6)throw Error(S(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Jt(e,we,ot),Se(e,Y()),null}function Cl(e,t){var n=z;z|=1;try{return e(t)}finally{z=n,z===0&&(Hn=Y()+500,Pi&&Vt())}}function un(e){Tt!==null&&Tt.tag===0&&!(z&6)&&zn();var t=z;z|=1;var n=Ae.transition,r=U;try{if(Ae.transition=null,U=1,e)return e()}finally{U=r,Ae.transition=n,z=t,!(z&6)&&Vt()}}function Pl(){Ee=Rn.current,V(Rn)}function nn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Em(n)),X!==null)for(n=X.return;n!==null;){var r=n;switch(ll(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ri();break;case 3:Bn(),V(ke),V(he),gl();break;case 5:vl(r);break;case 4:Bn();break;case 13:V(q);break;case 19:V(q);break;case 10:hl(r.type._context);break;case 22:case 23:Pl()}n=n.return}if(se=e,X=e=zt(e.current,null),oe=Ee=t,ee=0,Qr=null,El=Oi=ln=0,we=Cr=null,en!==null){for(t=0;t<en.length;t++)if(n=en[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=s,r.next=o}n.pending=r}en=null}return e}function Ah(e,t){do{var n=X;try{if(dl(),Ms.current=hi,di){for(var r=K.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}di=!1}if(an=0,re=Z=K=null,Er=!1,qr=0,Sl.current=null,n===null||n.return===null){ee=1,Qr=t,X=null;break}e:{var i=e,o=n.return,l=n,u=t;if(t=oe,l.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,d=l,h=d.tag;if(!(d.mode&1)&&(h===0||h===11||h===15)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var g=Bu(o);if(g!==null){g.flags&=-257,Vu(g,o,l,i,t),g.mode&1&&Fu(i,c,t),t=g,u=c;var w=t.updateQueue;if(w===null){var y=new Set;y.add(u),t.updateQueue=y}else w.add(u);break e}else{if(!(t&1)){Fu(i,c,t),Tl();break e}u=Error(S(426))}}else if(H&&l.mode&1){var k=Bu(o);if(k!==null){!(k.flags&65536)&&(k.flags|=256),Vu(k,o,l,i,t),ul(Vn(u,l));break e}}i=u=Vn(u,l),ee!==4&&(ee=2),Cr===null?Cr=[i]:Cr.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var m=yh(i,u,t);Au(i,m);break e;case 1:l=u;var f=i.type,v=i.stateNode;if(!(i.flags&128)&&(typeof f.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(At===null||!At.has(v)))){i.flags|=65536,t&=-t,i.lanes|=t;var _=wh(i,l,t);Au(i,_);break e}}i=i.return}while(i!==null)}Dh(n)}catch(j){t=j,X===n&&n!==null&&(X=n=n.return);continue}break}while(1)}function Ih(){var e=fi.current;return fi.current=hi,e===null?hi:e}function Tl(){(ee===0||ee===3||ee===2)&&(ee=4),se===null||!(ln&268435455)&&!(Oi&268435455)||Nt(se,oe)}function vi(e,t){var n=z;z|=2;var r=Ih();(se!==e||oe!==t)&&(ot=null,nn(e,t));do try{Jm();break}catch(s){Ah(e,s)}while(1);if(dl(),z=n,fi.current=r,X!==null)throw Error(S(261));return se=null,oe=0,ee}function Jm(){for(;X!==null;)zh(X)}function Ym(){for(;X!==null&&!kp();)zh(X)}function zh(e){var t=Mh(e.alternate,e,Ee);e.memoizedProps=e.pendingProps,t===null?Dh(e):X=t,Sl.current=null}function Dh(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Hm(n,t),n!==null){n.flags&=32767,X=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ee=6,X=null;return}}else if(n=Vm(n,t,Ee),n!==null){X=n;return}if(t=t.sibling,t!==null){X=t;return}X=t=e}while(t!==null);ee===0&&(ee=5)}function Jt(e,t,n){var r=U,s=Ae.transition;try{Ae.transition=null,U=1,Xm(e,t,n,r)}finally{Ae.transition=s,U=r}return null}function Xm(e,t,n,r){do zn();while(Tt!==null);if(z&6)throw Error(S(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(S(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Op(e,i),e===se&&(X=se=null,oe=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ns||(Ns=!0,Fh(Ys,function(){return zn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Ae.transition,Ae.transition=null;var o=U;U=1;var l=z;z|=4,Sl.current=null,qm(e,n),Oh(n,e),ym(ra),Zs=!!na,ra=na=null,e.current=n,Km(n),jp(),z=l,U=o,Ae.transition=i}else e.current=n;if(Ns&&(Ns=!1,Tt=e,mi=s),i=e.pendingLanes,i===0&&(At=null),Np(n.stateNode),Se(e,Y()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(pi)throw pi=!1,e=ja,ja=null,e;return mi&1&&e.tag!==0&&zn(),i=e.pendingLanes,i&1?e===Sa?Pr++:(Pr=0,Sa=e):Pr=0,Vt(),null}function zn(){if(Tt!==null){var e=yd(mi),t=Ae.transition,n=U;try{if(Ae.transition=null,U=16>e?16:e,Tt===null)var r=!1;else{if(e=Tt,Tt=null,mi=0,z&6)throw Error(S(331));var s=z;for(z|=4,C=e.current;C!==null;){var i=C,o=i.child;if(C.flags&16){var l=i.deletions;if(l!==null){for(var u=0;u<l.length;u++){var c=l[u];for(C=c;C!==null;){var d=C;switch(d.tag){case 0:case 11:case 15:Nr(8,d,i)}var h=d.child;if(h!==null)h.return=d,C=h;else for(;C!==null;){d=C;var p=d.sibling,g=d.return;if(Th(d),d===c){C=null;break}if(p!==null){p.return=g,C=p;break}C=g}}}var w=i.alternate;if(w!==null){var y=w.child;if(y!==null){w.child=null;do{var k=y.sibling;y.sibling=null,y=k}while(y!==null)}}C=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,C=o;else e:for(;C!==null;){if(i=C,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Nr(9,i,i.return)}var m=i.sibling;if(m!==null){m.return=i.return,C=m;break e}C=i.return}}var f=e.current;for(C=f;C!==null;){o=C;var v=o.child;if(o.subtreeFlags&2064&&v!==null)v.return=o,C=v;else e:for(o=f;C!==null;){if(l=C,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Ri(9,l)}}catch(j){Q(l,l.return,j)}if(l===o){C=null;break e}var _=l.sibling;if(_!==null){_.return=l.return,C=_;break e}C=l.return}}if(z=s,Vt(),tt&&typeof tt.onPostCommitFiberRoot=="function")try{tt.onPostCommitFiberRoot(ji,e)}catch{}r=!0}return r}finally{U=n,Ae.transition=t}}return!1}function nc(e,t,n){t=Vn(n,t),t=yh(e,t,1),e=$t(e,t,1),t=pe(),e!==null&&(ts(e,1,t),Se(e,t))}function Q(e,t,n){if(e.tag===3)nc(e,e,n);else for(;t!==null;){if(t.tag===3){nc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(At===null||!At.has(r))){e=Vn(n,e),e=wh(t,e,1),t=$t(t,e,1),e=pe(),t!==null&&(ts(t,1,e),Se(t,e));break}}t=t.return}}function Zm(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=pe(),e.pingedLanes|=e.suspendedLanes&n,se===e&&(oe&n)===n&&(ee===4||ee===3&&(oe&130023424)===oe&&500>Y()-Nl?nn(e,0):El|=n),Se(e,t)}function Uh(e,t){t===0&&(e.mode&1?(t=vs,vs<<=1,!(vs&130023424)&&(vs=4194304)):t=1);var n=pe();e=mt(e,t),e!==null&&(ts(e,t,n),Se(e,n))}function ev(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Uh(e,n)}function tv(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(S(314))}r!==null&&r.delete(t),Uh(e,n)}var Mh;Mh=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ke.current)_e=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return _e=!1,Bm(e,t,n);_e=!!(e.flags&131072)}else _e=!1,H&&t.flags&1048576&&Hd(t,oi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Bs(e,t),e=t.pendingProps;var s=Un(t,he.current);In(t,n),s=wl(null,t,r,e,s,n);var i=xl();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,je(r)?(i=!0,si(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,pl(t),s.updater=bi,t.stateNode=s,s._reactInternals=t,ha(t,r,e,n),t=ma(null,t,r,!0,i,n)):(t.tag=0,H&&i&&al(t),fe(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Bs(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=rv(r),e=Fe(r,e),s){case 0:t=pa(null,t,r,e,n);break e;case 1:t=qu(null,t,r,e,n);break e;case 11:t=Hu(null,t,r,e,n);break e;case 14:t=Wu(null,t,r,Fe(r.type,e),n);break e}throw Error(S(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Fe(r,s),pa(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Fe(r,s),qu(e,t,r,s,n);case 3:e:{if(jh(t),e===null)throw Error(S(387));r=t.pendingProps,i=t.memoizedState,s=i.element,Jd(e,t),ui(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=Vn(Error(S(423)),t),t=Ku(e,t,r,n,s);break e}else if(r!==s){s=Vn(Error(S(424)),t),t=Ku(e,t,r,n,s);break e}else for(Ne=Lt(t.stateNode.containerInfo.firstChild),Ce=t,H=!0,He=null,n=Gd(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Mn(),r===s){t=vt(e,t,n);break e}fe(e,t,r,n)}t=t.child}return t;case 5:return Yd(t),e===null&&ua(t),r=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,o=s.children,sa(r,s)?o=null:i!==null&&sa(r,i)&&(t.flags|=32),kh(e,t),fe(e,t,o,n),t.child;case 6:return e===null&&ua(t),null;case 13:return Sh(e,t,n);case 4:return ml(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Fn(t,null,r,n):fe(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Fe(r,s),Hu(e,t,r,s,n);case 7:return fe(e,t,t.pendingProps,n),t.child;case 8:return fe(e,t,t.pendingProps.children,n),t.child;case 12:return fe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,i=t.memoizedProps,o=s.value,F(ai,r._currentValue),r._currentValue=o,i!==null)if(Ge(i.value,o)){if(i.children===s.children&&!ke.current){t=vt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var l=i.dependencies;if(l!==null){o=i.child;for(var u=l.firstContext;u!==null;){if(u.context===r){if(i.tag===1){u=dt(-1,n&-n),u.tag=2;var c=i.updateQueue;if(c!==null){c=c.shared;var d=c.pending;d===null?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}i.lanes|=n,u=i.alternate,u!==null&&(u.lanes|=n),ca(i.return,n,t),l.lanes|=n;break}u=u.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(S(341));o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),ca(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}fe(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,In(t,n),s=ze(s),r=r(s),t.flags|=1,fe(e,t,r,n),t.child;case 14:return r=t.type,s=Fe(r,t.pendingProps),s=Fe(r.type,s),Wu(e,t,r,s,n);case 15:return xh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Fe(r,s),Bs(e,t),t.tag=1,je(r)?(e=!0,si(t)):e=!1,In(t,n),gh(t,r,s),ha(t,r,s,n),ma(null,t,r,!0,e,n);case 19:return Eh(e,t,n);case 22:return _h(e,t,n)}throw Error(S(156,t.tag))};function Fh(e,t){return pd(e,t)}function nv(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function $e(e,t,n,r){return new nv(e,t,n,r)}function bl(e){return e=e.prototype,!(!e||!e.isReactComponent)}function rv(e){if(typeof e=="function")return bl(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Qa)return 11;if(e===Ja)return 14}return 2}function zt(e,t){var n=e.alternate;return n===null?(n=$e(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ws(e,t,n,r,s,i){var o=2;if(r=e,typeof e=="function")bl(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case _n:return rn(n.children,s,i,t);case Ga:o=8,s|=8;break;case Io:return e=$e(12,n,t,s|2),e.elementType=Io,e.lanes=i,e;case zo:return e=$e(13,n,t,s),e.elementType=zo,e.lanes=i,e;case Do:return e=$e(19,n,t,s),e.elementType=Do,e.lanes=i,e;case Yc:return Li(n,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Qc:o=10;break e;case Jc:o=9;break e;case Qa:o=11;break e;case Ja:o=14;break e;case kt:o=16,r=null;break e}throw Error(S(130,e==null?e:typeof e,""))}return t=$e(o,n,t,s),t.elementType=e,t.type=r,t.lanes=i,t}function rn(e,t,n,r){return e=$e(7,e,r,t),e.lanes=n,e}function Li(e,t,n,r){return e=$e(22,e,r,t),e.elementType=Yc,e.lanes=n,e.stateNode={isHidden:!1},e}function jo(e,t,n){return e=$e(6,e,null,t),e.lanes=n,e}function So(e,t,n){return t=$e(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function sv(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=ro(0),this.expirationTimes=ro(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ro(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function Rl(e,t,n,r,s,i,o,l,u){return e=new sv(e,t,n,l,u),t===1?(t=1,i===!0&&(t|=8)):t=0,i=$e(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},pl(i),e}function iv(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:xn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Bh(e){if(!e)return Ut;e=e._reactInternals;e:{if(dn(e)!==e||e.tag!==1)throw Error(S(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(je(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(S(171))}if(e.tag===1){var n=e.type;if(je(n))return Bd(e,n,t)}return t}function Vh(e,t,n,r,s,i,o,l,u){return e=Rl(n,r,!0,e,s,i,o,l,u),e.context=Bh(null),n=e.current,r=pe(),s=It(n),i=dt(r,s),i.callback=t??null,$t(n,i,s),e.current.lanes=s,ts(e,s,r),Se(e,r),e}function $i(e,t,n,r){var s=t.current,i=pe(),o=It(s);return n=Bh(n),t.context===null?t.context=n:t.pendingContext=n,t=dt(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=$t(s,t,o),e!==null&&(Ke(e,s,o,i),Us(e,s,o)),o}function gi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function rc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Ol(e,t){rc(e,t),(e=e.alternate)&&rc(e,t)}function ov(){return null}var Hh=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ll(e){this._internalRoot=e}Ai.prototype.render=Ll.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(S(409));$i(e,t,null,null)};Ai.prototype.unmount=Ll.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;un(function(){$i(null,e,null,null)}),t[pt]=null}};function Ai(e){this._internalRoot=e}Ai.prototype.unstable_scheduleHydration=function(e){if(e){var t=_d();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Et.length&&t!==0&&t<Et[n].priority;n++);Et.splice(n,0,e),n===0&&jd(e)}};function $l(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ii(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function sc(){}function av(e,t,n,r,s){if(s){if(typeof r=="function"){var i=r;r=function(){var c=gi(o);i.call(c)}}var o=Vh(t,r,e,0,null,!1,!1,"",sc);return e._reactRootContainer=o,e[pt]=o.current,Fr(e.nodeType===8?e.parentNode:e),un(),o}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var l=r;r=function(){var c=gi(u);l.call(c)}}var u=Rl(e,0,!1,null,null,!1,!1,"",sc);return e._reactRootContainer=u,e[pt]=u.current,Fr(e.nodeType===8?e.parentNode:e),un(function(){$i(t,u,n,r)}),u}function zi(e,t,n,r,s){var i=n._reactRootContainer;if(i){var o=i;if(typeof s=="function"){var l=s;s=function(){var u=gi(o);l.call(u)}}$i(t,o,e,s)}else o=av(n,t,e,s,r);return gi(o)}wd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=vr(t.pendingLanes);n!==0&&(Za(t,n|1),Se(t,Y()),!(z&6)&&(Hn=Y()+500,Vt()))}break;case 13:un(function(){var r=mt(e,1);if(r!==null){var s=pe();Ke(r,e,1,s)}}),Ol(e,1)}};el=function(e){if(e.tag===13){var t=mt(e,134217728);if(t!==null){var n=pe();Ke(t,e,134217728,n)}Ol(e,134217728)}};xd=function(e){if(e.tag===13){var t=It(e),n=mt(e,t);if(n!==null){var r=pe();Ke(n,e,t,r)}Ol(e,t)}};_d=function(){return U};kd=function(e,t){var n=U;try{return U=e,t()}finally{U=n}};Go=function(e,t,n){switch(t){case"input":if(Fo(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=Ci(r);if(!s)throw Error(S(90));Zc(r),Fo(r,s)}}}break;case"textarea":td(e,n);break;case"select":t=n.value,t!=null&&On(e,!!n.multiple,t,!1)}};ld=Cl;ud=un;var lv={usingClientEntryPoint:!1,Events:[rs,En,Ci,od,ad,Cl]},cr={findFiberByHostInstance:Zt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},uv={bundleType:cr.bundleType,version:cr.version,rendererPackageName:cr.rendererPackageName,rendererConfig:cr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:yt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=hd(e),e===null?null:e.stateNode},findFiberByHostInstance:cr.findFiberByHostInstance||ov,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Cs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Cs.isDisabled&&Cs.supportsFiber)try{ji=Cs.inject(uv),tt=Cs}catch{}}Te.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=lv;Te.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!$l(t))throw Error(S(200));return iv(e,t,null,n)};Te.createRoot=function(e,t){if(!$l(e))throw Error(S(299));var n=!1,r="",s=Hh;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=Rl(e,1,!1,null,null,n,!1,r,s),e[pt]=t.current,Fr(e.nodeType===8?e.parentNode:e),new Ll(t)};Te.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(S(188)):(e=Object.keys(e).join(","),Error(S(268,e)));return e=hd(t),e=e===null?null:e.stateNode,e};Te.flushSync=function(e){return un(e)};Te.hydrate=function(e,t,n){if(!Ii(t))throw Error(S(200));return zi(null,e,t,!0,n)};Te.hydrateRoot=function(e,t,n){if(!$l(e))throw Error(S(405));var r=n!=null&&n.hydratedSources||null,s=!1,i="",o=Hh;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=Vh(t,null,e,1,n??null,s,!1,i,o),e[pt]=t.current,Fr(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new Ai(t)};Te.render=function(e,t,n){if(!Ii(t))throw Error(S(200));return zi(null,e,t,!1,n)};Te.unmountComponentAtNode=function(e){if(!Ii(e))throw Error(S(40));return e._reactRootContainer?(un(function(){zi(null,null,e,!1,function(){e._reactRootContainer=null,e[pt]=null})}),!0):!1};Te.unstable_batchedUpdates=Cl;Te.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ii(n))throw Error(S(200));if(e==null||e._reactInternals===void 0)throw Error(S(38));return zi(e,t,n,!1,r)};Te.version="18.3.1-next-f1338f8080-20240426";function Wh(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Wh)}catch(e){console.error(e)}}Wh(),Wc.exports=Te;var cv=Wc.exports,ic=cv;$o.createRoot=ic.createRoot,$o.hydrateRoot=ic.hydrateRoot;const dv="modulepreload",hv=function(e){return"/"+e},oc={},Qn=function(t,n,r){if(!n||n.length===0)return t();const s=document.getElementsByTagName("link");return Promise.all(n.map(i=>{if(i=hv(i),i in oc)return;oc[i]=!0;const o=i.endsWith(".css"),l=o?'[rel="stylesheet"]':"";if(!!r)for(let d=s.length-1;d>=0;d--){const h=s[d];if(h.href===i&&(!o||h.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${i}"]${l}`))return;const c=document.createElement("link");if(c.rel=o?"stylesheet":dv,o||(c.as="script",c.crossOrigin=""),c.href=i,document.head.appendChild(c),o)return new Promise((d,h)=>{c.addEventListener("load",d),c.addEventListener("error",()=>h(new Error(`Unable to preload CSS for ${i}`)))})})).then(()=>t()).catch(i=>{const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=i,window.dispatchEvent(o),!o.defaultPrevented)throw i})};var Al={};Object.defineProperty(Al,"__esModule",{value:!0});Al.parse=wv;Al.serialize=xv;const fv=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,pv=/^[\u0021-\u003A\u003C-\u007E]*$/,mv=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,vv=/^[\u0020-\u003A\u003D-\u007E]*$/,gv=Object.prototype.toString,yv=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function wv(e,t){const n=new yv,r=e.length;if(r<2)return n;const s=(t==null?void 0:t.decode)||_v;let i=0;do{const o=e.indexOf("=",i);if(o===-1)break;const l=e.indexOf(";",i),u=l===-1?r:l;if(o>u){i=e.lastIndexOf(";",o-1)+1;continue}const c=ac(e,i,o),d=lc(e,o,c),h=e.slice(c,d);if(n[h]===void 0){let p=ac(e,o+1,u),g=lc(e,u,p);const w=s(e.slice(p,g));n[h]=w}i=u+1}while(i<r);return n}function ac(e,t,n){do{const r=e.charCodeAt(t);if(r!==32&&r!==9)return t}while(++t<n);return n}function lc(e,t,n){for(;t>n;){const r=e.charCodeAt(--t);if(r!==32&&r!==9)return t+1}return n}function xv(e,t,n){const r=(n==null?void 0:n.encode)||encodeURIComponent;if(!fv.test(e))throw new TypeError(`argument name is invalid: ${e}`);const s=r(t);if(!pv.test(s))throw new TypeError(`argument val is invalid: ${t}`);let i=e+"="+s;if(!n)return i;if(n.maxAge!==void 0){if(!Number.isInteger(n.maxAge))throw new TypeError(`option maxAge is invalid: ${n.maxAge}`);i+="; Max-Age="+n.maxAge}if(n.domain){if(!mv.test(n.domain))throw new TypeError(`option domain is invalid: ${n.domain}`);i+="; Domain="+n.domain}if(n.path){if(!vv.test(n.path))throw new TypeError(`option path is invalid: ${n.path}`);i+="; Path="+n.path}if(n.expires){if(!kv(n.expires)||!Number.isFinite(n.expires.valueOf()))throw new TypeError(`option expires is invalid: ${n.expires}`);i+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(i+="; HttpOnly"),n.secure&&(i+="; Secure"),n.partitioned&&(i+="; Partitioned"),n.priority)switch(typeof n.priority=="string"?n.priority.toLowerCase():void 0){case"low":i+="; Priority=Low";break;case"medium":i+="; Priority=Medium";break;case"high":i+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${n.priority}`)}if(n.sameSite)switch(typeof n.sameSite=="string"?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"none":i+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${n.sameSite}`)}return i}function _v(e){if(e.indexOf("%")===-1)return e;try{return decodeURIComponent(e)}catch{return e}}function kv(e){return gv.call(e)==="[object Date]"}var uc="popstate";function jv(e={}){function t(r,s){let{pathname:i,search:o,hash:l}=r.location;return Ca("",{pathname:i,search:o,hash:l},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function n(r,s){return typeof s=="string"?s:Jr(s)}return Ev(t,n,null,e)}function W(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Qe(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Sv(){return Math.random().toString(36).substring(2,10)}function cc(e,t){return{usr:e.state,key:e.key,idx:t}}function Ca(e,t,n=null,r){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?Jn(t):t,state:n,key:t&&t.key||r||Sv()}}function Jr({pathname:e="/",search:t="",hash:n=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),n&&n!=="#"&&(e+=n.charAt(0)==="#"?n:"#"+n),e}function Jn(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function Ev(e,t,n,r={}){let{window:s=document.defaultView,v5Compat:i=!1}=r,o=s.history,l="POP",u=null,c=d();c==null&&(c=0,o.replaceState({...o.state,idx:c},""));function d(){return(o.state||{idx:null}).idx}function h(){l="POP";let k=d(),m=k==null?null:k-c;c=k,u&&u({action:l,location:y.location,delta:m})}function p(k,m){l="PUSH";let f=Ca(y.location,k,m);n&&n(f,k),c=d()+1;let v=cc(f,c),_=y.createHref(f);try{o.pushState(v,"",_)}catch(j){if(j instanceof DOMException&&j.name==="DataCloneError")throw j;s.location.assign(_)}i&&u&&u({action:l,location:y.location,delta:1})}function g(k,m){l="REPLACE";let f=Ca(y.location,k,m);n&&n(f,k),c=d();let v=cc(f,c),_=y.createHref(f);o.replaceState(v,"",_),i&&u&&u({action:l,location:y.location,delta:0})}function w(k){return Nv(k)}let y={get action(){return l},get location(){return e(s,o)},listen(k){if(u)throw new Error("A history only accepts one active listener");return s.addEventListener(uc,h),u=k,()=>{s.removeEventListener(uc,h),u=null}},createHref(k){return t(s,k)},createURL:w,encodeLocation(k){let m=w(k);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:p,replace:g,go(k){return o.go(k)}};return y}function Nv(e,t=!1){let n="http://localhost";typeof window<"u"&&(n=window.location.origin!=="null"?window.location.origin:window.location.href),W(n,"No window.location.(origin|href) available to create URL");let r=typeof e=="string"?e:Jr(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}function qh(e,t,n="/"){return Cv(e,t,n,!1)}function Cv(e,t,n,r){let s=typeof t=="string"?Jn(t):t,i=gt(s.pathname||"/",n);if(i==null)return null;let o=Kh(e);Pv(o);let l=null;for(let u=0;l==null&&u<o.length;++u){let c=Uv(i);l=zv(o[u],c,r)}return l}function Kh(e,t=[],n=[],r=""){let s=(i,o,l)=>{let u={relativePath:l===void 0?i.path||"":l,caseSensitive:i.caseSensitive===!0,childrenIndex:o,route:i};u.relativePath.startsWith("/")&&(W(u.relativePath.startsWith(r),`Absolute route path "${u.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),u.relativePath=u.relativePath.slice(r.length));let c=ht([r,u.relativePath]),d=n.concat(u);i.children&&i.children.length>0&&(W(i.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${c}".`),Kh(i.children,t,d,c)),!(i.path==null&&!i.index)&&t.push({path:c,score:Av(c,i.index),routesMeta:d})};return e.forEach((i,o)=>{var l;if(i.path===""||!((l=i.path)!=null&&l.includes("?")))s(i,o);else for(let u of Gh(i.path))s(i,o,u)}),t}function Gh(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,s=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return s?[i,""]:[i];let o=Gh(r.join("/")),l=[];return l.push(...o.map(u=>u===""?i:[i,u].join("/"))),s&&l.push(...o),l.map(u=>e.startsWith("/")&&u===""?"/":u)}function Pv(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Iv(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}var Tv=/^:[\w-]+$/,bv=3,Rv=2,Ov=1,Lv=10,$v=-2,dc=e=>e==="*";function Av(e,t){let n=e.split("/"),r=n.length;return n.some(dc)&&(r+=$v),t&&(r+=Rv),n.filter(s=>!dc(s)).reduce((s,i)=>s+(Tv.test(i)?bv:i===""?Ov:Lv),r)}function Iv(e,t){return e.length===t.length&&e.slice(0,-1).every((r,s)=>r===t[s])?e[e.length-1]-t[t.length-1]:0}function zv(e,t,n=!1){let{routesMeta:r}=e,s={},i="/",o=[];for(let l=0;l<r.length;++l){let u=r[l],c=l===r.length-1,d=i==="/"?t:t.slice(i.length)||"/",h=yi({path:u.relativePath,caseSensitive:u.caseSensitive,end:c},d),p=u.route;if(!h&&c&&n&&!r[r.length-1].route.index&&(h=yi({path:u.relativePath,caseSensitive:u.caseSensitive,end:!1},d)),!h)return null;Object.assign(s,h.params),o.push({params:s,pathname:ht([i,h.pathname]),pathnameBase:Vv(ht([i,h.pathnameBase])),route:p}),h.pathnameBase!=="/"&&(i=ht([i,h.pathnameBase]))}return o}function yi(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Dv(e.path,e.caseSensitive,e.end),s=t.match(n);if(!s)return null;let i=s[0],o=i.replace(/(.)\/+$/,"$1"),l=s.slice(1);return{params:r.reduce((c,{paramName:d,isOptional:h},p)=>{if(d==="*"){let w=l[p]||"";o=i.slice(0,i.length-w.length).replace(/(.)\/+$/,"$1")}const g=l[p];return h&&!g?c[d]=void 0:c[d]=(g||"").replace(/%2F/g,"/"),c},{}),pathname:i,pathnameBase:o,pattern:e}}function Dv(e,t=!1,n=!0){Qe(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],s="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,l,u)=>(r.push({paramName:l,isOptional:u!=null}),u?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),s+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?s+="\\/*$":e!==""&&e!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,t?void 0:"i"),r]}function Uv(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Qe(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function gt(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Mv(e,t="/"){let{pathname:n,search:r="",hash:s=""}=typeof e=="string"?Jn(e):e;return{pathname:n?n.startsWith("/")?n:Fv(n,t):t,search:Hv(r),hash:Wv(s)}}function Fv(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(s=>{s===".."?n.length>1&&n.pop():s!=="."&&n.push(s)}),n.length>1?n.join("/"):"/"}function Eo(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Bv(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Il(e){let t=Bv(e);return t.map((n,r)=>r===t.length-1?n.pathname:n.pathnameBase)}function zl(e,t,n,r=!1){let s;typeof e=="string"?s=Jn(e):(s={...e},W(!s.pathname||!s.pathname.includes("?"),Eo("?","pathname","search",s)),W(!s.pathname||!s.pathname.includes("#"),Eo("#","pathname","hash",s)),W(!s.search||!s.search.includes("#"),Eo("#","search","hash",s)));let i=e===""||s.pathname==="",o=i?"/":s.pathname,l;if(o==null)l=n;else{let h=t.length-1;if(!r&&o.startsWith("..")){let p=o.split("/");for(;p[0]==="..";)p.shift(),h-=1;s.pathname=p.join("/")}l=h>=0?t[h]:"/"}let u=Mv(s,l),c=o&&o!=="/"&&o.endsWith("/"),d=(i||o===".")&&n.endsWith("/");return!u.pathname.endsWith("/")&&(c||d)&&(u.pathname+="/"),u}var ht=e=>e.join("/").replace(/\/\/+/g,"/"),Vv=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Hv=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Wv=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function qv(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var Qh=["POST","PUT","PATCH","DELETE"];new Set(Qh);var Kv=["GET",...Qh];new Set(Kv);var Yn=x.createContext(null);Yn.displayName="DataRouter";var Di=x.createContext(null);Di.displayName="DataRouterState";var Jh=x.createContext({isTransitioning:!1});Jh.displayName="ViewTransition";var Gv=x.createContext(new Map);Gv.displayName="Fetchers";var Qv=x.createContext(null);Qv.displayName="Await";var Je=x.createContext(null);Je.displayName="Navigation";var is=x.createContext(null);is.displayName="Location";var rt=x.createContext({outlet:null,matches:[],isDataRoute:!1});rt.displayName="Route";var Dl=x.createContext(null);Dl.displayName="RouteError";function Jv(e,{relative:t}={}){W(Xn(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=x.useContext(Je),{hash:s,pathname:i,search:o}=os(e,{relative:t}),l=i;return n!=="/"&&(l=i==="/"?n:ht([n,i])),r.createHref({pathname:l,search:o,hash:s})}function Xn(){return x.useContext(is)!=null}function wt(){return W(Xn(),"useLocation() may be used only in the context of a <Router> component."),x.useContext(is).location}var Yh="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Xh(e){x.useContext(Je).static||x.useLayoutEffect(e)}function hn(){let{isDataRoute:e}=x.useContext(rt);return e?ug():Yv()}function Yv(){W(Xn(),"useNavigate() may be used only in the context of a <Router> component.");let e=x.useContext(Yn),{basename:t,navigator:n}=x.useContext(Je),{matches:r}=x.useContext(rt),{pathname:s}=wt(),i=JSON.stringify(Il(r)),o=x.useRef(!1);return Xh(()=>{o.current=!0}),x.useCallback((u,c={})=>{if(Qe(o.current,Yh),!o.current)return;if(typeof u=="number"){n.go(u);return}let d=zl(u,JSON.parse(i),s,c.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:ht([t,d.pathname])),(c.replace?n.replace:n.push)(d,c.state,c)},[t,n,i,s,e])}x.createContext(null);function os(e,{relative:t}={}){let{matches:n}=x.useContext(rt),{pathname:r}=wt(),s=JSON.stringify(Il(n));return x.useMemo(()=>zl(e,JSON.parse(s),r,t==="path"),[e,s,r,t])}function Xv(e,t){return Zh(e,t)}function Zh(e,t,n,r){var m;W(Xn(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:s}=x.useContext(Je),{matches:i}=x.useContext(rt),o=i[i.length-1],l=o?o.params:{},u=o?o.pathname:"/",c=o?o.pathnameBase:"/",d=o&&o.route;{let f=d&&d.path||"";ef(u,!d||f.endsWith("*")||f.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${u}" (under <Route path="${f}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${f}"> to <Route path="${f==="/"?"*":`${f}/*`}">.`)}let h=wt(),p;if(t){let f=typeof t=="string"?Jn(t):t;W(c==="/"||((m=f.pathname)==null?void 0:m.startsWith(c)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${c}" but pathname "${f.pathname}" was given in the \`location\` prop.`),p=f}else p=h;let g=p.pathname||"/",w=g;if(c!=="/"){let f=c.replace(/^\//,"").split("/");w="/"+g.replace(/^\//,"").split("/").slice(f.length).join("/")}let y=qh(e,{pathname:w});Qe(d||y!=null,`No routes matched location "${p.pathname}${p.search}${p.hash}" `),Qe(y==null||y[y.length-1].route.element!==void 0||y[y.length-1].route.Component!==void 0||y[y.length-1].route.lazy!==void 0,`Matched leaf route at location "${p.pathname}${p.search}${p.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let k=rg(y&&y.map(f=>Object.assign({},f,{params:Object.assign({},l,f.params),pathname:ht([c,s.encodeLocation?s.encodeLocation(f.pathname).pathname:f.pathname]),pathnameBase:f.pathnameBase==="/"?c:ht([c,s.encodeLocation?s.encodeLocation(f.pathnameBase).pathname:f.pathnameBase])})),i,n,r);return t&&k?x.createElement(is.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...p},navigationType:"POP"}},k):k}function Zv(){let e=lg(),t=qv(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",s={padding:"0.5rem",backgroundColor:r},i={padding:"2px 4px",backgroundColor:r},o=null;return console.error("Error handled by React Router default ErrorBoundary:",e),o=x.createElement(x.Fragment,null,x.createElement("p",null,"💿 Hey developer 👋"),x.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",x.createElement("code",{style:i},"ErrorBoundary")," or"," ",x.createElement("code",{style:i},"errorElement")," prop on your route.")),x.createElement(x.Fragment,null,x.createElement("h2",null,"Unexpected Application Error!"),x.createElement("h3",{style:{fontStyle:"italic"}},t),n?x.createElement("pre",{style:s},n):null,o)}var eg=x.createElement(Zv,null),tg=class extends x.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?x.createElement(rt.Provider,{value:this.props.routeContext},x.createElement(Dl.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function ng({routeContext:e,match:t,children:n}){let r=x.useContext(Yn);return r&&r.static&&r.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=t.route.id),x.createElement(rt.Provider,{value:e},n)}function rg(e,t=[],n=null,r=null){if(e==null){if(!n)return null;if(n.errors)e=n.matches;else if(t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let s=e,i=n==null?void 0:n.errors;if(i!=null){let u=s.findIndex(c=>c.route.id&&(i==null?void 0:i[c.route.id])!==void 0);W(u>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(i).join(",")}`),s=s.slice(0,Math.min(s.length,u+1))}let o=!1,l=-1;if(n)for(let u=0;u<s.length;u++){let c=s[u];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(l=u),c.route.id){let{loaderData:d,errors:h}=n,p=c.route.loader&&!d.hasOwnProperty(c.route.id)&&(!h||h[c.route.id]===void 0);if(c.route.lazy||p){o=!0,l>=0?s=s.slice(0,l+1):s=[s[0]];break}}}return s.reduceRight((u,c,d)=>{let h,p=!1,g=null,w=null;n&&(h=i&&c.route.id?i[c.route.id]:void 0,g=c.route.errorElement||eg,o&&(l<0&&d===0?(ef("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),p=!0,w=null):l===d&&(p=!0,w=c.route.hydrateFallbackElement||null)));let y=t.concat(s.slice(0,d+1)),k=()=>{let m;return h?m=g:p?m=w:c.route.Component?m=x.createElement(c.route.Component,null):c.route.element?m=c.route.element:m=u,x.createElement(ng,{match:c,routeContext:{outlet:u,matches:y,isDataRoute:n!=null},children:m})};return n&&(c.route.ErrorBoundary||c.route.errorElement||d===0)?x.createElement(tg,{location:n.location,revalidation:n.revalidation,component:g,error:h,children:k(),routeContext:{outlet:null,matches:y,isDataRoute:!0}}):k()},null)}function Ul(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function sg(e){let t=x.useContext(Yn);return W(t,Ul(e)),t}function ig(e){let t=x.useContext(Di);return W(t,Ul(e)),t}function og(e){let t=x.useContext(rt);return W(t,Ul(e)),t}function Ml(e){let t=og(e),n=t.matches[t.matches.length-1];return W(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}function ag(){return Ml("useRouteId")}function lg(){var r;let e=x.useContext(Dl),t=ig("useRouteError"),n=Ml("useRouteError");return e!==void 0?e:(r=t.errors)==null?void 0:r[n]}function ug(){let{router:e}=sg("useNavigate"),t=Ml("useNavigate"),n=x.useRef(!1);return Xh(()=>{n.current=!0}),x.useCallback(async(s,i={})=>{Qe(n.current,Yh),n.current&&(typeof s=="number"?e.navigate(s):await e.navigate(s,{fromRouteId:t,...i}))},[e,t])}var hc={};function ef(e,t,n){!t&&!hc[e]&&(hc[e]=!0,Qe(!1,n))}x.memo(cg);function cg({routes:e,future:t,state:n}){return Zh(e,void 0,n,t)}function yr({to:e,replace:t,state:n,relative:r}){W(Xn(),"<Navigate> may be used only in the context of a <Router> component.");let{static:s}=x.useContext(Je);Qe(!s,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:i}=x.useContext(rt),{pathname:o}=wt(),l=hn(),u=zl(e,Il(i),o,r==="path"),c=JSON.stringify(u);return x.useEffect(()=>{l(JSON.parse(c),{replace:t,state:n,relative:r})},[l,c,r,t,n]),null}function st(e){W(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function dg({basename:e="/",children:t=null,location:n,navigationType:r="POP",navigator:s,static:i=!1}){W(!Xn(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let o=e.replace(/^\/*/,"/"),l=x.useMemo(()=>({basename:o,navigator:s,static:i,future:{}}),[o,s,i]);typeof n=="string"&&(n=Jn(n));let{pathname:u="/",search:c="",hash:d="",state:h=null,key:p="default"}=n,g=x.useMemo(()=>{let w=gt(u,o);return w==null?null:{location:{pathname:w,search:c,hash:d,state:h,key:p},navigationType:r}},[o,u,c,d,h,p,r]);return Qe(g!=null,`<Router basename="${o}"> is not able to match the URL "${u}${c}${d}" because it does not start with the basename, so the <Router> won't render anything.`),g==null?null:x.createElement(Je.Provider,{value:l},x.createElement(is.Provider,{children:t,value:g}))}function hg({children:e,location:t}){return Xv(Pa(e),t)}function Pa(e,t=[]){let n=[];return x.Children.forEach(e,(r,s)=>{if(!x.isValidElement(r))return;let i=[...t,s];if(r.type===x.Fragment){n.push.apply(n,Pa(r.props.children,i));return}W(r.type===st,`[${typeof r.type=="string"?r.type:r.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),W(!r.props.index||!r.props.children,"An index route cannot have child routes.");let o={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,hydrateFallbackElement:r.props.hydrateFallbackElement,HydrateFallback:r.props.HydrateFallback,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.hasErrorBoundary===!0||r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=Pa(r.props.children,i)),n.push(o)}),n}var qs="get",Ks="application/x-www-form-urlencoded";function Ui(e){return e!=null&&typeof e.tagName=="string"}function fg(e){return Ui(e)&&e.tagName.toLowerCase()==="button"}function pg(e){return Ui(e)&&e.tagName.toLowerCase()==="form"}function mg(e){return Ui(e)&&e.tagName.toLowerCase()==="input"}function vg(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function gg(e,t){return e.button===0&&(!t||t==="_self")&&!vg(e)}var Ps=null;function yg(){if(Ps===null)try{new FormData(document.createElement("form"),0),Ps=!1}catch{Ps=!0}return Ps}var wg=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function No(e){return e!=null&&!wg.has(e)?(Qe(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Ks}"`),null):e}function xg(e,t){let n,r,s,i,o;if(pg(e)){let l=e.getAttribute("action");r=l?gt(l,t):null,n=e.getAttribute("method")||qs,s=No(e.getAttribute("enctype"))||Ks,i=new FormData(e)}else if(fg(e)||mg(e)&&(e.type==="submit"||e.type==="image")){let l=e.form;if(l==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let u=e.getAttribute("formaction")||l.getAttribute("action");if(r=u?gt(u,t):null,n=e.getAttribute("formmethod")||l.getAttribute("method")||qs,s=No(e.getAttribute("formenctype"))||No(l.getAttribute("enctype"))||Ks,i=new FormData(l,e),!yg()){let{name:c,type:d,value:h}=e;if(d==="image"){let p=c?`${c}.`:"";i.append(`${p}x`,"0"),i.append(`${p}y`,"0")}else c&&i.append(c,h)}}else{if(Ui(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=qs,r=null,s=Ks,o=e}return i&&s==="text/plain"&&(o=i,i=void 0),{action:r,method:n.toLowerCase(),encType:s,formData:i,body:o}}function Fl(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function _g(e,t){if(e.id in t)return t[e.id];try{let n=await Qn(()=>import(e.module),[]);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function kg(e){return e!=null&&typeof e.page=="string"}function jg(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function Sg(e,t,n){let r=await Promise.all(e.map(async s=>{let i=t.routes[s.route.id];if(i){let o=await _g(i,n);return o.links?o.links():[]}return[]}));return Pg(r.flat(1).filter(jg).filter(s=>s.rel==="stylesheet"||s.rel==="preload").map(s=>s.rel==="stylesheet"?{...s,rel:"prefetch",as:"style"}:{...s,rel:"prefetch"}))}function fc(e,t,n,r,s,i){let o=(u,c)=>n[c]?u.route.id!==n[c].route.id:!0,l=(u,c)=>{var d;return n[c].pathname!==u.pathname||((d=n[c].route.path)==null?void 0:d.endsWith("*"))&&n[c].params["*"]!==u.params["*"]};return i==="assets"?t.filter((u,c)=>o(u,c)||l(u,c)):i==="data"?t.filter((u,c)=>{var h;let d=r.routes[u.route.id];if(!d||!d.hasLoader)return!1;if(o(u,c)||l(u,c))return!0;if(u.route.shouldRevalidate){let p=u.route.shouldRevalidate({currentUrl:new URL(s.pathname+s.search+s.hash,window.origin),currentParams:((h=n[0])==null?void 0:h.params)||{},nextUrl:new URL(e,window.origin),nextParams:u.params,defaultShouldRevalidate:!0});if(typeof p=="boolean")return p}return!0}):[]}function Eg(e,t,{includeHydrateFallback:n}={}){return Ng(e.map(r=>{let s=t.routes[r.route.id];if(!s)return[];let i=[s.module];return s.clientActionModule&&(i=i.concat(s.clientActionModule)),s.clientLoaderModule&&(i=i.concat(s.clientLoaderModule)),n&&s.hydrateFallbackModule&&(i=i.concat(s.hydrateFallbackModule)),s.imports&&(i=i.concat(s.imports)),i}).flat(1))}function Ng(e){return[...new Set(e)]}function Cg(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}function Pg(e,t){let n=new Set,r=new Set(t);return e.reduce((s,i)=>{if(t&&!kg(i)&&i.as==="script"&&i.href&&r.has(i.href))return s;let l=JSON.stringify(Cg(i));return n.has(l)||(n.add(l),s.push({key:l,link:i})),s},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Tg=new Set([100,101,204,205]);function bg(e,t){let n=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return n.pathname==="/"?n.pathname="_root.data":t&&gt(n.pathname,t)==="/"?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}function tf(){let e=x.useContext(Yn);return Fl(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function Rg(){let e=x.useContext(Di);return Fl(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var Bl=x.createContext(void 0);Bl.displayName="FrameworkContext";function nf(){let e=x.useContext(Bl);return Fl(e,"You must render this element inside a <HydratedRouter> element"),e}function Og(e,t){let n=x.useContext(Bl),[r,s]=x.useState(!1),[i,o]=x.useState(!1),{onFocus:l,onBlur:u,onMouseEnter:c,onMouseLeave:d,onTouchStart:h}=t,p=x.useRef(null);x.useEffect(()=>{if(e==="render"&&o(!0),e==="viewport"){let y=m=>{m.forEach(f=>{o(f.isIntersecting)})},k=new IntersectionObserver(y,{threshold:.5});return p.current&&k.observe(p.current),()=>{k.disconnect()}}},[e]),x.useEffect(()=>{if(r){let y=setTimeout(()=>{o(!0)},100);return()=>{clearTimeout(y)}}},[r]);let g=()=>{s(!0)},w=()=>{s(!1),o(!1)};return n?e!=="intent"?[i,p,{}]:[i,p,{onFocus:dr(l,g),onBlur:dr(u,w),onMouseEnter:dr(c,g),onMouseLeave:dr(d,w),onTouchStart:dr(h,g)}]:[!1,p,{}]}function dr(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function Lg({page:e,...t}){let{router:n}=tf(),r=x.useMemo(()=>qh(n.routes,e,n.basename),[n.routes,e,n.basename]);return r?x.createElement(Ag,{page:e,matches:r,...t}):null}function $g(e){let{manifest:t,routeModules:n}=nf(),[r,s]=x.useState([]);return x.useEffect(()=>{let i=!1;return Sg(e,t,n).then(o=>{i||s(o)}),()=>{i=!0}},[e,t,n]),r}function Ag({page:e,matches:t,...n}){let r=wt(),{manifest:s,routeModules:i}=nf(),{basename:o}=tf(),{loaderData:l,matches:u}=Rg(),c=x.useMemo(()=>fc(e,t,u,s,r,"data"),[e,t,u,s,r]),d=x.useMemo(()=>fc(e,t,u,s,r,"assets"),[e,t,u,s,r]),h=x.useMemo(()=>{if(e===r.pathname+r.search+r.hash)return[];let w=new Set,y=!1;if(t.forEach(m=>{var v;let f=s.routes[m.route.id];!f||!f.hasLoader||(!c.some(_=>_.route.id===m.route.id)&&m.route.id in l&&((v=i[m.route.id])!=null&&v.shouldRevalidate)||f.hasClientLoader?y=!0:w.add(m.route.id))}),w.size===0)return[];let k=bg(e,o);return y&&w.size>0&&k.searchParams.set("_routes",t.filter(m=>w.has(m.route.id)).map(m=>m.route.id).join(",")),[k.pathname+k.search]},[o,l,r,s,c,t,e,i]),p=x.useMemo(()=>Eg(d,s),[d,s]),g=$g(d);return x.createElement(x.Fragment,null,h.map(w=>x.createElement("link",{key:w,rel:"prefetch",as:"fetch",href:w,...n})),p.map(w=>x.createElement("link",{key:w,rel:"modulepreload",href:w,...n})),g.map(({key:w,link:y})=>x.createElement("link",{key:w,...y})))}function Ig(...e){return t=>{e.forEach(n=>{typeof n=="function"?n(t):n!=null&&(n.current=t)})}}var rf=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{rf&&(window.__reactRouterVersion="7.6.3")}catch{}function zg({basename:e,children:t,window:n}){let r=x.useRef();r.current==null&&(r.current=jv({window:n,v5Compat:!0}));let s=r.current,[i,o]=x.useState({action:s.action,location:s.location}),l=x.useCallback(u=>{x.startTransition(()=>o(u))},[o]);return x.useLayoutEffect(()=>s.listen(l),[s,l]),x.createElement(dg,{basename:e,children:t,location:i.location,navigationType:i.action,navigator:s})}var sf=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,et=x.forwardRef(function({onClick:t,discover:n="render",prefetch:r="none",relative:s,reloadDocument:i,replace:o,state:l,target:u,to:c,preventScrollReset:d,viewTransition:h,...p},g){let{basename:w}=x.useContext(Je),y=typeof c=="string"&&sf.test(c),k,m=!1;if(typeof c=="string"&&y&&(k=c,rf))try{let I=new URL(window.location.href),b=c.startsWith("//")?new URL(I.protocol+c):new URL(c),ge=gt(b.pathname,w);b.origin===I.origin&&ge!=null?c=ge+b.search+b.hash:m=!0}catch{Qe(!1,`<Link to="${c}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let f=Jv(c,{relative:s}),[v,_,j]=Og(r,p),E=Fg(c,{replace:o,state:l,target:u,preventScrollReset:d,relative:s,viewTransition:h});function N(I){t&&t(I),I.defaultPrevented||E(I)}let T=x.createElement("a",{...p,...j,href:k||f,onClick:m||i?t:N,ref:Ig(g,_),target:u,"data-discover":!y&&n==="render"?"true":void 0});return v&&!y?x.createElement(x.Fragment,null,T,x.createElement(Lg,{page:f})):T});et.displayName="Link";var Dg=x.forwardRef(function({"aria-current":t="page",caseSensitive:n=!1,className:r="",end:s=!1,style:i,to:o,viewTransition:l,children:u,...c},d){let h=os(o,{relative:c.relative}),p=wt(),g=x.useContext(Di),{navigator:w,basename:y}=x.useContext(Je),k=g!=null&&qg(h)&&l===!0,m=w.encodeLocation?w.encodeLocation(h).pathname:h.pathname,f=p.pathname,v=g&&g.navigation&&g.navigation.location?g.navigation.location.pathname:null;n||(f=f.toLowerCase(),v=v?v.toLowerCase():null,m=m.toLowerCase()),v&&y&&(v=gt(v,y)||v);const _=m!=="/"&&m.endsWith("/")?m.length-1:m.length;let j=f===m||!s&&f.startsWith(m)&&f.charAt(_)==="/",E=v!=null&&(v===m||!s&&v.startsWith(m)&&v.charAt(m.length)==="/"),N={isActive:j,isPending:E,isTransitioning:k},T=j?t:void 0,I;typeof r=="function"?I=r(N):I=[r,j?"active":null,E?"pending":null,k?"transitioning":null].filter(Boolean).join(" ");let b=typeof i=="function"?i(N):i;return x.createElement(et,{...c,"aria-current":T,className:I,ref:d,style:b,to:o,viewTransition:l},typeof u=="function"?u(N):u)});Dg.displayName="NavLink";var Ug=x.forwardRef(({discover:e="render",fetcherKey:t,navigate:n,reloadDocument:r,replace:s,state:i,method:o=qs,action:l,onSubmit:u,relative:c,preventScrollReset:d,viewTransition:h,...p},g)=>{let w=Hg(),y=Wg(l,{relative:c}),k=o.toLowerCase()==="get"?"get":"post",m=typeof l=="string"&&sf.test(l),f=v=>{if(u&&u(v),v.defaultPrevented)return;v.preventDefault();let _=v.nativeEvent.submitter,j=(_==null?void 0:_.getAttribute("formmethod"))||o;w(_||v.currentTarget,{fetcherKey:t,method:j,navigate:n,replace:s,state:i,relative:c,preventScrollReset:d,viewTransition:h})};return x.createElement("form",{ref:g,method:k,action:y,onSubmit:r?u:f,...p,"data-discover":!m&&e==="render"?"true":void 0})});Ug.displayName="Form";function Mg(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function of(e){let t=x.useContext(Yn);return W(t,Mg(e)),t}function Fg(e,{target:t,replace:n,state:r,preventScrollReset:s,relative:i,viewTransition:o}={}){let l=hn(),u=wt(),c=os(e,{relative:i});return x.useCallback(d=>{if(gg(d,t)){d.preventDefault();let h=n!==void 0?n:Jr(u)===Jr(c);l(e,{replace:h,state:r,preventScrollReset:s,relative:i,viewTransition:o})}},[u,l,c,n,r,t,e,s,i,o])}var Bg=0,Vg=()=>`__${String(++Bg)}__`;function Hg(){let{router:e}=of("useSubmit"),{basename:t}=x.useContext(Je),n=ag();return x.useCallback(async(r,s={})=>{let{action:i,method:o,encType:l,formData:u,body:c}=xg(r,t);if(s.navigate===!1){let d=s.fetcherKey||Vg();await e.fetch(d,n,s.action||i,{preventScrollReset:s.preventScrollReset,formData:u,body:c,formMethod:s.method||o,formEncType:s.encType||l,flushSync:s.flushSync})}else await e.navigate(s.action||i,{preventScrollReset:s.preventScrollReset,formData:u,body:c,formMethod:s.method||o,formEncType:s.encType||l,replace:s.replace,state:s.state,fromRouteId:n,flushSync:s.flushSync,viewTransition:s.viewTransition})},[e,t,n])}function Wg(e,{relative:t}={}){let{basename:n}=x.useContext(Je),r=x.useContext(rt);W(r,"useFormAction must be used inside a RouteContext");let[s]=r.matches.slice(-1),i={...os(e||".",{relative:t})},o=wt();if(e==null){i.search=o.search;let l=new URLSearchParams(i.search),u=l.getAll("index");if(u.some(d=>d==="")){l.delete("index"),u.filter(h=>h).forEach(h=>l.append("index",h));let d=l.toString();i.search=d?`?${d}`:""}}return(!e||e===".")&&s.route.index&&(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index"),n!=="/"&&(i.pathname=i.pathname==="/"?n:ht([n,i.pathname])),Jr(i)}function qg(e,t={}){let n=x.useContext(Jh);W(n!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=of("useViewTransitionState"),s=os(e,{relative:t.relative});if(!n.isTransitioning)return!1;let i=gt(n.currentLocation.pathname,r)||n.currentLocation.pathname,o=gt(n.nextLocation.pathname,r)||n.nextLocation.pathname;return yi(s.pathname,o)!=null||yi(s.pathname,i)!=null}[...Tg];const Kg=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>Qn(()=>Promise.resolve().then(()=>Zn),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)};class Vl extends Error{constructor(t,n="FunctionsError",r){super(t),this.name=n,this.context=r}}class Gg extends Vl{constructor(t){super("Failed to send a request to the Edge Function","FunctionsFetchError",t)}}class pc extends Vl{constructor(t){super("Relay Error invoking the Edge Function","FunctionsRelayError",t)}}class mc extends Vl{constructor(t){super("Edge Function returned a non-2xx status code","FunctionsHttpError",t)}}var Ta;(function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"})(Ta||(Ta={}));var Qg=globalThis&&globalThis.__awaiter||function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{c(r.next(d))}catch(h){o(h)}}function u(d){try{c(r.throw(d))}catch(h){o(h)}}function c(d){d.done?i(d.value):s(d.value).then(l,u)}c((r=r.apply(e,t||[])).next())})};class Jg{constructor(t,{headers:n={},customFetch:r,region:s=Ta.Any}={}){this.url=t,this.headers=n,this.region=s,this.fetch=Kg(r)}setAuth(t){this.headers.Authorization=`Bearer ${t}`}invoke(t,n={}){var r;return Qg(this,void 0,void 0,function*(){try{const{headers:s,method:i,body:o}=n;let l={},{region:u}=n;u||(u=this.region);const c=new URL(`${this.url}/${t}`);u&&u!=="any"&&(l["x-region"]=u,c.searchParams.set("forceFunctionRegion",u));let d;o&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&(typeof Blob<"u"&&o instanceof Blob||o instanceof ArrayBuffer?(l["Content-Type"]="application/octet-stream",d=o):typeof o=="string"?(l["Content-Type"]="text/plain",d=o):typeof FormData<"u"&&o instanceof FormData?d=o:(l["Content-Type"]="application/json",d=JSON.stringify(o)));const h=yield this.fetch(c.toString(),{method:i||"POST",headers:Object.assign(Object.assign(Object.assign({},l),this.headers),s),body:d}).catch(y=>{throw new Gg(y)}),p=h.headers.get("x-relay-error");if(p&&p==="true")throw new pc(h);if(!h.ok)throw new mc(h);let g=((r=h.headers.get("Content-Type"))!==null&&r!==void 0?r:"text/plain").split(";")[0].trim(),w;return g==="application/json"?w=yield h.json():g==="application/octet-stream"?w=yield h.blob():g==="text/event-stream"?w=h:g==="multipart/form-data"?w=yield h.formData():w=yield h.text(),{data:w,error:null,response:h}}catch(s){return{data:null,error:s,response:s instanceof mc||s instanceof pc?s.context:void 0}}})}}var xe={},Hl={},Mi={},as={},Fi={},Bi={},Yg=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},Wn=Yg();const Xg=Wn.fetch,af=Wn.fetch.bind(Wn),lf=Wn.Headers,Zg=Wn.Request,ey=Wn.Response,Zn=Object.freeze(Object.defineProperty({__proto__:null,Headers:lf,Request:Zg,Response:ey,default:af,fetch:Xg},Symbol.toStringTag,{value:"Module"})),ty=Df(Zn);var Vi={};Object.defineProperty(Vi,"__esModule",{value:!0});let ny=class extends Error{constructor(t){super(t.message),this.name="PostgrestError",this.details=t.details,this.hint=t.hint,this.code=t.code}};Vi.default=ny;var uf=Ie&&Ie.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Bi,"__esModule",{value:!0});const ry=uf(ty),sy=uf(Vi);let iy=class{constructor(t){this.shouldThrowOnError=!1,this.method=t.method,this.url=t.url,this.headers=t.headers,this.schema=t.schema,this.body=t.body,this.shouldThrowOnError=t.shouldThrowOnError,this.signal=t.signal,this.isMaybeSingle=t.isMaybeSingle,t.fetch?this.fetch=t.fetch:typeof fetch>"u"?this.fetch=ry.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(t,n){return this.headers=Object.assign({},this.headers),this.headers[t]=n,this}then(t,n){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const r=this.fetch;let s=r(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async i=>{var o,l,u;let c=null,d=null,h=null,p=i.status,g=i.statusText;if(i.ok){if(this.method!=="HEAD"){const m=await i.text();m===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?d=m:d=JSON.parse(m))}const y=(o=this.headers.Prefer)===null||o===void 0?void 0:o.match(/count=(exact|planned|estimated)/),k=(l=i.headers.get("content-range"))===null||l===void 0?void 0:l.split("/");y&&k&&k.length>1&&(h=parseInt(k[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(d)&&(d.length>1?(c={code:"PGRST116",details:`Results contain ${d.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},d=null,h=null,p=406,g="Not Acceptable"):d.length===1?d=d[0]:d=null)}else{const y=await i.text();try{c=JSON.parse(y),Array.isArray(c)&&i.status===404&&(d=[],c=null,p=200,g="OK")}catch{i.status===404&&y===""?(p=204,g="No Content"):c={message:y}}if(c&&this.isMaybeSingle&&(!((u=c==null?void 0:c.details)===null||u===void 0)&&u.includes("0 rows"))&&(c=null,p=200,g="OK"),c&&this.shouldThrowOnError)throw new sy.default(c)}return{error:c,data:d,count:h,status:p,statusText:g}});return this.shouldThrowOnError||(s=s.catch(i=>{var o,l,u;return{error:{message:`${(o=i==null?void 0:i.name)!==null&&o!==void 0?o:"FetchError"}: ${i==null?void 0:i.message}`,details:`${(l=i==null?void 0:i.stack)!==null&&l!==void 0?l:""}`,hint:"",code:`${(u=i==null?void 0:i.code)!==null&&u!==void 0?u:""}`},data:null,count:null,status:0,statusText:""}})),s.then(t,n)}returns(){return this}overrideTypes(){return this}};Bi.default=iy;var oy=Ie&&Ie.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Fi,"__esModule",{value:!0});const ay=oy(Bi);let ly=class extends ay.default{select(t){let n=!1;const r=(t??"*").split("").map(s=>/\s/.test(s)&&!n?"":(s==='"'&&(n=!n),s)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(t,{ascending:n=!0,nullsFirst:r,foreignTable:s,referencedTable:i=s}={}){const o=i?`${i}.order`:"order",l=this.url.searchParams.get(o);return this.url.searchParams.set(o,`${l?`${l},`:""}${t}.${n?"asc":"desc"}${r===void 0?"":r?".nullsfirst":".nullslast"}`),this}limit(t,{foreignTable:n,referencedTable:r=n}={}){const s=typeof r>"u"?"limit":`${r}.limit`;return this.url.searchParams.set(s,`${t}`),this}range(t,n,{foreignTable:r,referencedTable:s=r}={}){const i=typeof s>"u"?"offset":`${s}.offset`,o=typeof s>"u"?"limit":`${s}.limit`;return this.url.searchParams.set(i,`${t}`),this.url.searchParams.set(o,`${n-t+1}`),this}abortSignal(t){return this.signal=t,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:t=!1,verbose:n=!1,settings:r=!1,buffers:s=!1,wal:i=!1,format:o="text"}={}){var l;const u=[t?"analyze":null,n?"verbose":null,r?"settings":null,s?"buffers":null,i?"wal":null].filter(Boolean).join("|"),c=(l=this.headers.Accept)!==null&&l!==void 0?l:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${o}; for="${c}"; options=${u};`,o==="json"?this:this}rollback(){var t;return((t=this.headers.Prefer)!==null&&t!==void 0?t:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};Fi.default=ly;var uy=Ie&&Ie.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(as,"__esModule",{value:!0});const cy=uy(Fi);let dy=class extends cy.default{eq(t,n){return this.url.searchParams.append(t,`eq.${n}`),this}neq(t,n){return this.url.searchParams.append(t,`neq.${n}`),this}gt(t,n){return this.url.searchParams.append(t,`gt.${n}`),this}gte(t,n){return this.url.searchParams.append(t,`gte.${n}`),this}lt(t,n){return this.url.searchParams.append(t,`lt.${n}`),this}lte(t,n){return this.url.searchParams.append(t,`lte.${n}`),this}like(t,n){return this.url.searchParams.append(t,`like.${n}`),this}likeAllOf(t,n){return this.url.searchParams.append(t,`like(all).{${n.join(",")}}`),this}likeAnyOf(t,n){return this.url.searchParams.append(t,`like(any).{${n.join(",")}}`),this}ilike(t,n){return this.url.searchParams.append(t,`ilike.${n}`),this}ilikeAllOf(t,n){return this.url.searchParams.append(t,`ilike(all).{${n.join(",")}}`),this}ilikeAnyOf(t,n){return this.url.searchParams.append(t,`ilike(any).{${n.join(",")}}`),this}is(t,n){return this.url.searchParams.append(t,`is.${n}`),this}in(t,n){const r=Array.from(new Set(n)).map(s=>typeof s=="string"&&new RegExp("[,()]").test(s)?`"${s}"`:`${s}`).join(",");return this.url.searchParams.append(t,`in.(${r})`),this}contains(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cs.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cs.{${n.join(",")}}`):this.url.searchParams.append(t,`cs.${JSON.stringify(n)}`),this}containedBy(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cd.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cd.{${n.join(",")}}`):this.url.searchParams.append(t,`cd.${JSON.stringify(n)}`),this}rangeGt(t,n){return this.url.searchParams.append(t,`sr.${n}`),this}rangeGte(t,n){return this.url.searchParams.append(t,`nxl.${n}`),this}rangeLt(t,n){return this.url.searchParams.append(t,`sl.${n}`),this}rangeLte(t,n){return this.url.searchParams.append(t,`nxr.${n}`),this}rangeAdjacent(t,n){return this.url.searchParams.append(t,`adj.${n}`),this}overlaps(t,n){return typeof n=="string"?this.url.searchParams.append(t,`ov.${n}`):this.url.searchParams.append(t,`ov.{${n.join(",")}}`),this}textSearch(t,n,{config:r,type:s}={}){let i="";s==="plain"?i="pl":s==="phrase"?i="ph":s==="websearch"&&(i="w");const o=r===void 0?"":`(${r})`;return this.url.searchParams.append(t,`${i}fts${o}.${n}`),this}match(t){return Object.entries(t).forEach(([n,r])=>{this.url.searchParams.append(n,`eq.${r}`)}),this}not(t,n,r){return this.url.searchParams.append(t,`not.${n}.${r}`),this}or(t,{foreignTable:n,referencedTable:r=n}={}){const s=r?`${r}.or`:"or";return this.url.searchParams.append(s,`(${t})`),this}filter(t,n,r){return this.url.searchParams.append(t,`${n}.${r}`),this}};as.default=dy;var hy=Ie&&Ie.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Mi,"__esModule",{value:!0});const hr=hy(as);let fy=class{constructor(t,{headers:n={},schema:r,fetch:s}){this.url=t,this.headers=n,this.schema=r,this.fetch=s}select(t,{head:n=!1,count:r}={}){const s=n?"HEAD":"GET";let i=!1;const o=(t??"*").split("").map(l=>/\s/.test(l)&&!i?"":(l==='"'&&(i=!i),l)).join("");return this.url.searchParams.set("select",o),r&&(this.headers.Prefer=`count=${r}`),new hr.default({method:s,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(t,{count:n,defaultToNull:r=!0}={}){const s="POST",i=[];if(this.headers.Prefer&&i.push(this.headers.Prefer),n&&i.push(`count=${n}`),r||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(t)){const o=t.reduce((l,u)=>l.concat(Object.keys(u)),[]);if(o.length>0){const l=[...new Set(o)].map(u=>`"${u}"`);this.url.searchParams.set("columns",l.join(","))}}return new hr.default({method:s,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}upsert(t,{onConflict:n,ignoreDuplicates:r=!1,count:s,defaultToNull:i=!0}={}){const o="POST",l=[`resolution=${r?"ignore":"merge"}-duplicates`];if(n!==void 0&&this.url.searchParams.set("on_conflict",n),this.headers.Prefer&&l.push(this.headers.Prefer),s&&l.push(`count=${s}`),i||l.push("missing=default"),this.headers.Prefer=l.join(","),Array.isArray(t)){const u=t.reduce((c,d)=>c.concat(Object.keys(d)),[]);if(u.length>0){const c=[...new Set(u)].map(d=>`"${d}"`);this.url.searchParams.set("columns",c.join(","))}}return new hr.default({method:o,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}update(t,{count:n}={}){const r="PATCH",s=[];return this.headers.Prefer&&s.push(this.headers.Prefer),n&&s.push(`count=${n}`),this.headers.Prefer=s.join(","),new hr.default({method:r,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}delete({count:t}={}){const n="DELETE",r=[];return t&&r.push(`count=${t}`),this.headers.Prefer&&r.unshift(this.headers.Prefer),this.headers.Prefer=r.join(","),new hr.default({method:n,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};Mi.default=fy;var Hi={},Wi={};Object.defineProperty(Wi,"__esModule",{value:!0});Wi.version=void 0;Wi.version="0.0.0-automated";Object.defineProperty(Hi,"__esModule",{value:!0});Hi.DEFAULT_HEADERS=void 0;const py=Wi;Hi.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${py.version}`};var cf=Ie&&Ie.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Hl,"__esModule",{value:!0});const my=cf(Mi),vy=cf(as),gy=Hi;let yy=class df{constructor(t,{headers:n={},schema:r,fetch:s}={}){this.url=t,this.headers=Object.assign(Object.assign({},gy.DEFAULT_HEADERS),n),this.schemaName=r,this.fetch=s}from(t){const n=new URL(`${this.url}/${t}`);return new my.default(n,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new df(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(t,n={},{head:r=!1,get:s=!1,count:i}={}){let o;const l=new URL(`${this.url}/rpc/${t}`);let u;r||s?(o=r?"HEAD":"GET",Object.entries(n).filter(([d,h])=>h!==void 0).map(([d,h])=>[d,Array.isArray(h)?`{${h.join(",")}}`:`${h}`]).forEach(([d,h])=>{l.searchParams.append(d,h)})):(o="POST",u=n);const c=Object.assign({},this.headers);return i&&(c.Prefer=`count=${i}`),new vy.default({method:o,url:l,headers:c,schema:this.schemaName,body:u,fetch:this.fetch,allowEmpty:!1})}};Hl.default=yy;var er=Ie&&Ie.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(xe,"__esModule",{value:!0});xe.PostgrestError=xe.PostgrestBuilder=xe.PostgrestTransformBuilder=xe.PostgrestFilterBuilder=xe.PostgrestQueryBuilder=xe.PostgrestClient=void 0;const hf=er(Hl);xe.PostgrestClient=hf.default;const ff=er(Mi);xe.PostgrestQueryBuilder=ff.default;const pf=er(as);xe.PostgrestFilterBuilder=pf.default;const mf=er(Fi);xe.PostgrestTransformBuilder=mf.default;const vf=er(Bi);xe.PostgrestBuilder=vf.default;const gf=er(Vi);xe.PostgrestError=gf.default;var wy=xe.default={PostgrestClient:hf.default,PostgrestQueryBuilder:ff.default,PostgrestFilterBuilder:pf.default,PostgrestTransformBuilder:mf.default,PostgrestBuilder:vf.default,PostgrestError:gf.default};const{PostgrestClient:xy,PostgrestQueryBuilder:Qw,PostgrestFilterBuilder:Jw,PostgrestTransformBuilder:Yw,PostgrestBuilder:Xw,PostgrestError:Zw}=wy;function _y(){if(typeof WebSocket<"u")return WebSocket;if(typeof global.WebSocket<"u")return global.WebSocket;if(typeof window.WebSocket<"u")return window.WebSocket;if(typeof self.WebSocket<"u")return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}const ky=_y(),jy="2.11.15",Sy=`realtime-js/${jy}`,Ey="1.0.0",yf=1e4,Ny=1e3;var Tr;(function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"})(Tr||(Tr={}));var ce;(function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"})(ce||(ce={}));var Ve;(function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"})(Ve||(Ve={}));var ba;(function(e){e.websocket="websocket"})(ba||(ba={}));var Xt;(function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"})(Xt||(Xt={}));class Cy{constructor(){this.HEADER_LENGTH=1}decode(t,n){return t.constructor===ArrayBuffer?n(this._binaryDecode(t)):n(typeof t=="string"?JSON.parse(t):{})}_binaryDecode(t){const n=new DataView(t),r=new TextDecoder;return this._decodeBroadcast(t,n,r)}_decodeBroadcast(t,n,r){const s=n.getUint8(1),i=n.getUint8(2);let o=this.HEADER_LENGTH+2;const l=r.decode(t.slice(o,o+s));o=o+s;const u=r.decode(t.slice(o,o+i));o=o+i;const c=JSON.parse(r.decode(t.slice(o,t.byteLength)));return{ref:null,topic:l,event:u,payload:c}}}class wf{constructor(t,n){this.callback=t,this.timerCalc=n,this.timer=void 0,this.tries=0,this.callback=t,this.timerCalc=n}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var M;(function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"})(M||(M={}));const vc=(e,t,n={})=>{var r;const s=(r=n.skipTypes)!==null&&r!==void 0?r:[];return Object.keys(t).reduce((i,o)=>(i[o]=Py(o,e,t,s),i),{})},Py=(e,t,n,r)=>{const s=t.find(l=>l.name===e),i=s==null?void 0:s.type,o=n[e];return i&&!r.includes(i)?xf(i,o):Ra(o)},xf=(e,t)=>{if(e.charAt(0)==="_"){const n=e.slice(1,e.length);return Oy(t,n)}switch(e){case M.bool:return Ty(t);case M.float4:case M.float8:case M.int2:case M.int4:case M.int8:case M.numeric:case M.oid:return by(t);case M.json:case M.jsonb:return Ry(t);case M.timestamp:return Ly(t);case M.abstime:case M.date:case M.daterange:case M.int4range:case M.int8range:case M.money:case M.reltime:case M.text:case M.time:case M.timestamptz:case M.timetz:case M.tsrange:case M.tstzrange:return Ra(t);default:return Ra(t)}},Ra=e=>e,Ty=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},by=e=>{if(typeof e=="string"){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},Ry=e=>{if(typeof e=="string")try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},Oy=(e,t)=>{if(typeof e!="string")return e;const n=e.length-1,r=e[n];if(e[0]==="{"&&r==="}"){let i;const o=e.slice(1,n);try{i=JSON.parse("["+o+"]")}catch{i=o?o.split(","):[]}return i.map(l=>xf(t,l))}return e},Ly=e=>typeof e=="string"?e.replace(" ","T"):e,_f=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class Co{constructor(t,n,r={},s=yf){this.channel=t,this.event=n,this.payload=r,this.timeout=s,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(t){this.timeout=t,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(t){this.payload=Object.assign(Object.assign({},this.payload),t)}receive(t,n){var r;return this._hasReceived(t)&&n((r=this.receivedResp)===null||r===void 0?void 0:r.response),this.recHooks.push({status:t,callback:n}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const t=n=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=n,this._matchReceive(n)};this.channel._on(this.refEvent,{},t),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(t,n){this.refEvent&&this.channel._trigger(this.refEvent,{status:t,response:n})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:t,response:n}){this.recHooks.filter(r=>r.status===t).forEach(r=>r.callback(n))}_hasReceived(t){return this.receivedResp&&this.receivedResp.status===t}}var gc;(function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"})(gc||(gc={}));class br{constructor(t,n){this.channel=t,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(n==null?void 0:n.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},s=>{const{onJoin:i,onLeave:o,onSync:l}=this.caller;this.joinRef=this.channel._joinRef(),this.state=br.syncState(this.state,s,i,o),this.pendingDiffs.forEach(u=>{this.state=br.syncDiff(this.state,u,i,o)}),this.pendingDiffs=[],l()}),this.channel._on(r.diff,{},s=>{const{onJoin:i,onLeave:o,onSync:l}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(s):(this.state=br.syncDiff(this.state,s,i,o),l())}),this.onJoin((s,i,o)=>{this.channel._trigger("presence",{event:"join",key:s,currentPresences:i,newPresences:o})}),this.onLeave((s,i,o)=>{this.channel._trigger("presence",{event:"leave",key:s,currentPresences:i,leftPresences:o})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(t,n,r,s){const i=this.cloneDeep(t),o=this.transformState(n),l={},u={};return this.map(i,(c,d)=>{o[c]||(u[c]=d)}),this.map(o,(c,d)=>{const h=i[c];if(h){const p=d.map(k=>k.presence_ref),g=h.map(k=>k.presence_ref),w=d.filter(k=>g.indexOf(k.presence_ref)<0),y=h.filter(k=>p.indexOf(k.presence_ref)<0);w.length>0&&(l[c]=w),y.length>0&&(u[c]=y)}else l[c]=d}),this.syncDiff(i,{joins:l,leaves:u},r,s)}static syncDiff(t,n,r,s){const{joins:i,leaves:o}={joins:this.transformState(n.joins),leaves:this.transformState(n.leaves)};return r||(r=()=>{}),s||(s=()=>{}),this.map(i,(l,u)=>{var c;const d=(c=t[l])!==null&&c!==void 0?c:[];if(t[l]=this.cloneDeep(u),d.length>0){const h=t[l].map(g=>g.presence_ref),p=d.filter(g=>h.indexOf(g.presence_ref)<0);t[l].unshift(...p)}r(l,d,u)}),this.map(o,(l,u)=>{let c=t[l];if(!c)return;const d=u.map(h=>h.presence_ref);c=c.filter(h=>d.indexOf(h.presence_ref)<0),t[l]=c,s(l,c,u),c.length===0&&delete t[l]}),t}static map(t,n){return Object.getOwnPropertyNames(t).map(r=>n(r,t[r]))}static transformState(t){return t=this.cloneDeep(t),Object.getOwnPropertyNames(t).reduce((n,r)=>{const s=t[r];return"metas"in s?n[r]=s.metas.map(i=>(i.presence_ref=i.phx_ref,delete i.phx_ref,delete i.phx_ref_prev,i)):n[r]=s,n},{})}static cloneDeep(t){return JSON.parse(JSON.stringify(t))}onJoin(t){this.caller.onJoin=t}onLeave(t){this.caller.onLeave=t}onSync(t){this.caller.onSync=t}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var yc;(function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"})(yc||(yc={}));var wc;(function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"})(wc||(wc={}));var at;(function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"})(at||(at={}));class Wl{constructor(t,n={config:{}},r){this.topic=t,this.params=n,this.socket=r,this.bindings={},this.state=ce.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=t.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},n.config),this.timeout=this.socket.timeout,this.joinPush=new Co(this,Ve.join,this.params,this.timeout),this.rejoinTimer=new wf(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=ce.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(s=>s.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=ce.closed,this.socket._remove(this)}),this._onError(s=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,s),this.state=ce.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=ce.errored,this.rejoinTimer.scheduleTimeout())}),this._on(Ve.reply,{},(s,i)=>{this._trigger(this._replyEventName(i),s)}),this.presence=new br(this),this.broadcastEndpointURL=_f(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(t,n=this.timeout){var r,s;if(this.socket.isConnected()||this.socket.connect(),this.state==ce.closed){const{config:{broadcast:i,presence:o,private:l}}=this.params;this._onError(d=>t==null?void 0:t(at.CHANNEL_ERROR,d)),this._onClose(()=>t==null?void 0:t(at.CLOSED));const u={},c={broadcast:i,presence:o,postgres_changes:(s=(r=this.bindings.postgres_changes)===null||r===void 0?void 0:r.map(d=>d.filter))!==null&&s!==void 0?s:[],private:l};this.socket.accessTokenValue&&(u.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:c},u)),this.joinedOnce=!0,this._rejoin(n),this.joinPush.receive("ok",async({postgres_changes:d})=>{var h;if(this.socket.setAuth(),d===void 0){t==null||t(at.SUBSCRIBED);return}else{const p=this.bindings.postgres_changes,g=(h=p==null?void 0:p.length)!==null&&h!==void 0?h:0,w=[];for(let y=0;y<g;y++){const k=p[y],{filter:{event:m,schema:f,table:v,filter:_}}=k,j=d&&d[y];if(j&&j.event===m&&j.schema===f&&j.table===v&&j.filter===_)w.push(Object.assign(Object.assign({},k),{id:j.id}));else{this.unsubscribe(),this.state=ce.errored,t==null||t(at.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=w,t&&t(at.SUBSCRIBED);return}}).receive("error",d=>{this.state=ce.errored,t==null||t(at.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(d).join(", ")||"error")))}).receive("timeout",()=>{t==null||t(at.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(t,n={}){return await this.send({type:"presence",event:"track",payload:t},n.timeout||this.timeout)}async untrack(t={}){return await this.send({type:"presence",event:"untrack"},t)}on(t,n,r){return this._on(t,n,r)}async send(t,n={}){var r,s;if(!this._canPush()&&t.type==="broadcast"){const{event:i,payload:o}=t,u={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:o,private:this.private}]})};try{const c=await this._fetchWithTimeout(this.broadcastEndpointURL,u,(r=n.timeout)!==null&&r!==void 0?r:this.timeout);return await((s=c.body)===null||s===void 0?void 0:s.cancel()),c.ok?"ok":"error"}catch(c){return c.name==="AbortError"?"timed out":"error"}}else return new Promise(i=>{var o,l,u;const c=this._push(t.type,t,n.timeout||this.timeout);t.type==="broadcast"&&!(!((u=(l=(o=this.params)===null||o===void 0?void 0:o.config)===null||l===void 0?void 0:l.broadcast)===null||u===void 0)&&u.ack)&&i("ok"),c.receive("ok",()=>i("ok")),c.receive("error",()=>i("error")),c.receive("timeout",()=>i("timed out"))})}updateJoinPayload(t){this.joinPush.updatePayload(t)}unsubscribe(t=this.timeout){this.state=ce.leaving;const n=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(Ve.close,"leave",this._joinRef())};this.joinPush.destroy();let r=null;return new Promise(s=>{r=new Co(this,Ve.leave,{},t),r.receive("ok",()=>{n(),s("ok")}).receive("timeout",()=>{n(),s("timed out")}).receive("error",()=>{s("error")}),r.send(),this._canPush()||r.trigger("ok",{})}).finally(()=>{r==null||r.destroy()})}teardown(){this.pushBuffer.forEach(t=>t.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(t,n,r){const s=new AbortController,i=setTimeout(()=>s.abort(),r),o=await this.socket.fetch(t,Object.assign(Object.assign({},n),{signal:s.signal}));return clearTimeout(i),o}_push(t,n,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${t}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let s=new Co(this,t,n,r);return this._canPush()?s.send():(s.startTimeout(),this.pushBuffer.push(s)),s}_onMessage(t,n,r){return n}_isMember(t){return this.topic===t}_joinRef(){return this.joinPush.ref}_trigger(t,n,r){var s,i;const o=t.toLocaleLowerCase(),{close:l,error:u,leave:c,join:d}=Ve;if(r&&[l,u,c,d].indexOf(o)>=0&&r!==this._joinRef())return;let p=this._onMessage(o,n,r);if(n&&!p)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?(s=this.bindings.postgres_changes)===null||s===void 0||s.filter(g=>{var w,y,k;return((w=g.filter)===null||w===void 0?void 0:w.event)==="*"||((k=(y=g.filter)===null||y===void 0?void 0:y.event)===null||k===void 0?void 0:k.toLocaleLowerCase())===o}).map(g=>g.callback(p,r)):(i=this.bindings[o])===null||i===void 0||i.filter(g=>{var w,y,k,m,f,v;if(["broadcast","presence","postgres_changes"].includes(o))if("id"in g){const _=g.id,j=(w=g.filter)===null||w===void 0?void 0:w.event;return _&&((y=n.ids)===null||y===void 0?void 0:y.includes(_))&&(j==="*"||(j==null?void 0:j.toLocaleLowerCase())===((k=n.data)===null||k===void 0?void 0:k.type.toLocaleLowerCase()))}else{const _=(f=(m=g==null?void 0:g.filter)===null||m===void 0?void 0:m.event)===null||f===void 0?void 0:f.toLocaleLowerCase();return _==="*"||_===((v=n==null?void 0:n.event)===null||v===void 0?void 0:v.toLocaleLowerCase())}else return g.type.toLocaleLowerCase()===o}).map(g=>{if(typeof p=="object"&&"ids"in p){const w=p.data,{schema:y,table:k,commit_timestamp:m,type:f,errors:v}=w;p=Object.assign(Object.assign({},{schema:y,table:k,commit_timestamp:m,eventType:f,new:{},old:{},errors:v}),this._getPayloadRecords(w))}g.callback(p,r)})}_isClosed(){return this.state===ce.closed}_isJoined(){return this.state===ce.joined}_isJoining(){return this.state===ce.joining}_isLeaving(){return this.state===ce.leaving}_replyEventName(t){return`chan_reply_${t}`}_on(t,n,r){const s=t.toLocaleLowerCase(),i={type:s,filter:n,callback:r};return this.bindings[s]?this.bindings[s].push(i):this.bindings[s]=[i],this}_off(t,n){const r=t.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(s=>{var i;return!(((i=s.type)===null||i===void 0?void 0:i.toLocaleLowerCase())===r&&Wl.isEqual(s.filter,n))}),this}static isEqual(t,n){if(Object.keys(t).length!==Object.keys(n).length)return!1;for(const r in t)if(t[r]!==n[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(t){this._on(Ve.close,{},t)}_onError(t){this._on(Ve.error,{},n=>t(n))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(t=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=ce.joining,this.joinPush.resend(t))}_getPayloadRecords(t){const n={new:{},old:{}};return(t.type==="INSERT"||t.type==="UPDATE")&&(n.new=vc(t.columns,t.record)),(t.type==="UPDATE"||t.type==="DELETE")&&(n.old=vc(t.columns,t.old_record)),n}}const xc=()=>{},$y=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class Ay{constructor(t,n){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=yf,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=xc,this.ref=0,this.logger=xc,this.conn=null,this.sendBuffer=[],this.serializer=new Cy,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=i=>{let o;return i?o=i:typeof fetch>"u"?o=(...l)=>Qn(()=>Promise.resolve().then(()=>Zn),void 0).then(({default:u})=>u(...l)):o=fetch,(...l)=>o(...l)},this.endPoint=`${t}/${ba.websocket}`,this.httpEndpoint=_f(t),n!=null&&n.transport?this.transport=n.transport:this.transport=null,n!=null&&n.params&&(this.params=n.params),n!=null&&n.timeout&&(this.timeout=n.timeout),n!=null&&n.logger&&(this.logger=n.logger),(n!=null&&n.logLevel||n!=null&&n.log_level)&&(this.logLevel=n.logLevel||n.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),n!=null&&n.heartbeatIntervalMs&&(this.heartbeatIntervalMs=n.heartbeatIntervalMs);const s=(r=n==null?void 0:n.params)===null||r===void 0?void 0:r.apikey;if(s&&(this.accessTokenValue=s,this.apiKey=s),this.reconnectAfterMs=n!=null&&n.reconnectAfterMs?n.reconnectAfterMs:i=>[1e3,2e3,5e3,1e4][i-1]||1e4,this.encode=n!=null&&n.encode?n.encode:(i,o)=>o(JSON.stringify(i)),this.decode=n!=null&&n.decode?n.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new wf(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(n==null?void 0:n.fetch),n!=null&&n.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(n==null?void 0:n.worker)||!1,this.workerUrl=n==null?void 0:n.workerUrl}this.accessToken=(n==null?void 0:n.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=ky),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:Ey}))}disconnect(t,n){this.conn&&(this.conn.onclose=function(){},t?this.conn.close(t,n??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(r=>r.teardown()))}getChannels(){return this.channels}async removeChannel(t){const n=await t.unsubscribe();return this.channels.length===0&&this.disconnect(),n}async removeAllChannels(){const t=await Promise.all(this.channels.map(n=>n.unsubscribe()));return this.channels=[],this.disconnect(),t}log(t,n,r){this.logger(t,n,r)}connectionState(){switch(this.conn&&this.conn.readyState){case Tr.connecting:return Xt.Connecting;case Tr.open:return Xt.Open;case Tr.closing:return Xt.Closing;default:return Xt.Closed}}isConnected(){return this.connectionState()===Xt.Open}channel(t,n={config:{}}){const r=`realtime:${t}`,s=this.getChannels().find(i=>i.topic===r);if(s)return s;{const i=new Wl(`realtime:${t}`,n,this);return this.channels.push(i),i}}push(t){const{topic:n,event:r,payload:s,ref:i}=t,o=()=>{this.encode(t,l=>{var u;(u=this.conn)===null||u===void 0||u.send(l)})};this.log("push",`${n} ${r} (${i})`,s),this.isConnected()?o():this.sendBuffer.push(o)}async setAuth(t=null){let n=t||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=n&&(this.accessTokenValue=n,this.channels.forEach(r=>{const s={access_token:n,version:Sy};n&&r.updateJoinPayload(s),r.joinedOnce&&r._isJoined()&&r._push(Ve.access_token,{access_token:n})}))}async sendHeartbeat(){var t;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),(t=this.conn)===null||t===void 0||t.close(Ny,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(t){this.heartbeatCallback=t}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(t=>t()),this.sendBuffer=[])}_makeRef(){let t=this.ref+1;return t===this.ref?this.ref=0:this.ref=t,this.ref.toString()}_leaveOpenTopic(t){let n=this.channels.find(r=>r.topic===t&&(r._isJoined()||r._isJoining()));n&&(this.log("transport",`leaving duplicate topic "${t}"`),n.unsubscribe())}_remove(t){this.channels=this.channels.filter(n=>n.topic!==t.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=t=>this._onConnError(t),this.conn.onmessage=t=>this._onConnMessage(t),this.conn.onclose=t=>this._onConnClose(t))}_onConnMessage(t){this.decode(t.data,n=>{let{topic:r,event:s,payload:i,ref:o}=n;r==="phoenix"&&s==="phx_reply"&&this.heartbeatCallback(n.payload.status=="ok"?"ok":"error"),o&&o===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${r} ${s} ${o&&"("+o+")"||""}`,i),Array.from(this.channels).filter(l=>l._isMember(r)).forEach(l=>l._trigger(s,i,o)),this.stateChangeCallbacks.message.forEach(l=>l(n))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(t=>t())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const t=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(t),this.workerRef.onerror=n=>{this.log("worker","worker error",n.message),this.workerRef.terminate()},this.workerRef.onmessage=n=>{n.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(t){this.log("transport","close",t),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(n=>n(t))}_onConnError(t){this.log("transport",`${t}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(n=>n(t))}_triggerChanError(){this.channels.forEach(t=>t._trigger(Ve.error))}_appendParams(t,n){if(Object.keys(n).length===0)return t;const r=t.match(/\?/)?"&":"?",s=new URLSearchParams(n);return`${t}${r}${s}`}_workerObjectUrl(t){let n;if(t)n=t;else{const r=new Blob([$y],{type:"application/javascript"});n=URL.createObjectURL(r)}return n}}class ql extends Error{constructor(t){super(t),this.__isStorageError=!0,this.name="StorageError"}}function ne(e){return typeof e=="object"&&e!==null&&"__isStorageError"in e}class Iy extends ql{constructor(t,n){super(t),this.name="StorageApiError",this.status=n}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class Oa extends ql{constructor(t,n){super(t),this.name="StorageUnknownError",this.originalError=n}}var zy=globalThis&&globalThis.__awaiter||function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{c(r.next(d))}catch(h){o(h)}}function u(d){try{c(r.throw(d))}catch(h){o(h)}}function c(d){d.done?i(d.value):s(d.value).then(l,u)}c((r=r.apply(e,t||[])).next())})};const kf=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>Qn(()=>Promise.resolve().then(()=>Zn),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},Dy=()=>zy(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield Qn(()=>Promise.resolve().then(()=>Zn),void 0)).Response:Response}),La=e=>{if(Array.isArray(e))return e.map(n=>La(n));if(typeof e=="function"||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([n,r])=>{const s=n.replace(/([-_][a-z])/gi,i=>i.toUpperCase().replace(/[-_]/g,""));t[s]=La(r)}),t};var fn=globalThis&&globalThis.__awaiter||function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{c(r.next(d))}catch(h){o(h)}}function u(d){try{c(r.throw(d))}catch(h){o(h)}}function c(d){d.done?i(d.value):s(d.value).then(l,u)}c((r=r.apply(e,t||[])).next())})};const Po=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Uy=(e,t,n)=>fn(void 0,void 0,void 0,function*(){const r=yield Dy();e instanceof r&&!(n!=null&&n.noResolveJson)?e.json().then(s=>{t(new Iy(Po(s),e.status||500))}).catch(s=>{t(new Oa(Po(s),s))}):t(new Oa(Po(e),e))}),My=(e,t,n,r)=>{const s={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?s:(s.headers=Object.assign({"Content-Type":"application/json"},t==null?void 0:t.headers),r&&(s.body=JSON.stringify(r)),Object.assign(Object.assign({},s),n))};function ls(e,t,n,r,s,i){return fn(this,void 0,void 0,function*(){return new Promise((o,l)=>{e(n,My(t,r,s,i)).then(u=>{if(!u.ok)throw u;return r!=null&&r.noResolveJson?u:u.json()}).then(u=>o(u)).catch(u=>Uy(u,l,r))})})}function wi(e,t,n,r){return fn(this,void 0,void 0,function*(){return ls(e,"GET",t,n,r)})}function St(e,t,n,r,s){return fn(this,void 0,void 0,function*(){return ls(e,"POST",t,r,s,n)})}function Fy(e,t,n,r,s){return fn(this,void 0,void 0,function*(){return ls(e,"PUT",t,r,s,n)})}function By(e,t,n,r){return fn(this,void 0,void 0,function*(){return ls(e,"HEAD",t,Object.assign(Object.assign({},n),{noResolveJson:!0}),r)})}function jf(e,t,n,r,s){return fn(this,void 0,void 0,function*(){return ls(e,"DELETE",t,r,s,n)})}var ye=globalThis&&globalThis.__awaiter||function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{c(r.next(d))}catch(h){o(h)}}function u(d){try{c(r.throw(d))}catch(h){o(h)}}function c(d){d.done?i(d.value):s(d.value).then(l,u)}c((r=r.apply(e,t||[])).next())})};const Vy={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},_c={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class Hy{constructor(t,n={},r,s){this.url=t,this.headers=n,this.bucketId=r,this.fetch=kf(s)}uploadOrUpdate(t,n,r,s){return ye(this,void 0,void 0,function*(){try{let i;const o=Object.assign(Object.assign({},_c),s);let l=Object.assign(Object.assign({},this.headers),t==="POST"&&{"x-upsert":String(o.upsert)});const u=o.metadata;typeof Blob<"u"&&r instanceof Blob?(i=new FormData,i.append("cacheControl",o.cacheControl),u&&i.append("metadata",this.encodeMetadata(u)),i.append("",r)):typeof FormData<"u"&&r instanceof FormData?(i=r,i.append("cacheControl",o.cacheControl),u&&i.append("metadata",this.encodeMetadata(u))):(i=r,l["cache-control"]=`max-age=${o.cacheControl}`,l["content-type"]=o.contentType,u&&(l["x-metadata"]=this.toBase64(this.encodeMetadata(u)))),s!=null&&s.headers&&(l=Object.assign(Object.assign({},l),s.headers));const c=this._removeEmptyFolders(n),d=this._getFinalPath(c),h=yield this.fetch(`${this.url}/object/${d}`,Object.assign({method:t,body:i,headers:l},o!=null&&o.duplex?{duplex:o.duplex}:{})),p=yield h.json();return h.ok?{data:{path:c,id:p.Id,fullPath:p.Key},error:null}:{data:null,error:p}}catch(i){if(ne(i))return{data:null,error:i};throw i}})}upload(t,n,r){return ye(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",t,n,r)})}uploadToSignedUrl(t,n,r,s){return ye(this,void 0,void 0,function*(){const i=this._removeEmptyFolders(t),o=this._getFinalPath(i),l=new URL(this.url+`/object/upload/sign/${o}`);l.searchParams.set("token",n);try{let u;const c=Object.assign({upsert:_c.upsert},s),d=Object.assign(Object.assign({},this.headers),{"x-upsert":String(c.upsert)});typeof Blob<"u"&&r instanceof Blob?(u=new FormData,u.append("cacheControl",c.cacheControl),u.append("",r)):typeof FormData<"u"&&r instanceof FormData?(u=r,u.append("cacheControl",c.cacheControl)):(u=r,d["cache-control"]=`max-age=${c.cacheControl}`,d["content-type"]=c.contentType);const h=yield this.fetch(l.toString(),{method:"PUT",body:u,headers:d}),p=yield h.json();return h.ok?{data:{path:i,fullPath:p.Key},error:null}:{data:null,error:p}}catch(u){if(ne(u))return{data:null,error:u};throw u}})}createSignedUploadUrl(t,n){return ye(this,void 0,void 0,function*(){try{let r=this._getFinalPath(t);const s=Object.assign({},this.headers);n!=null&&n.upsert&&(s["x-upsert"]="true");const i=yield St(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:s}),o=new URL(this.url+i.url),l=o.searchParams.get("token");if(!l)throw new ql("No token returned by API");return{data:{signedUrl:o.toString(),path:t,token:l},error:null}}catch(r){if(ne(r))return{data:null,error:r};throw r}})}update(t,n,r){return ye(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",t,n,r)})}move(t,n,r){return ye(this,void 0,void 0,function*(){try{return{data:yield St(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(s){if(ne(s))return{data:null,error:s};throw s}})}copy(t,n,r){return ye(this,void 0,void 0,function*(){try{return{data:{path:(yield St(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(s){if(ne(s))return{data:null,error:s};throw s}})}createSignedUrl(t,n,r){return ye(this,void 0,void 0,function*(){try{let s=this._getFinalPath(t),i=yield St(this.fetch,`${this.url}/object/sign/${s}`,Object.assign({expiresIn:n},r!=null&&r.transform?{transform:r.transform}:{}),{headers:this.headers});const o=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return i={signedUrl:encodeURI(`${this.url}${i.signedURL}${o}`)},{data:i,error:null}}catch(s){if(ne(s))return{data:null,error:s};throw s}})}createSignedUrls(t,n,r){return ye(this,void 0,void 0,function*(){try{const s=yield St(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:n,paths:t},{headers:this.headers}),i=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return{data:s.map(o=>Object.assign(Object.assign({},o),{signedUrl:o.signedURL?encodeURI(`${this.url}${o.signedURL}${i}`):null})),error:null}}catch(s){if(ne(s))return{data:null,error:s};throw s}})}download(t,n){return ye(this,void 0,void 0,function*(){const s=typeof(n==null?void 0:n.transform)<"u"?"render/image/authenticated":"object",i=this.transformOptsToQueryString((n==null?void 0:n.transform)||{}),o=i?`?${i}`:"";try{const l=this._getFinalPath(t);return{data:yield(yield wi(this.fetch,`${this.url}/${s}/${l}${o}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(l){if(ne(l))return{data:null,error:l};throw l}})}info(t){return ye(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{const r=yield wi(this.fetch,`${this.url}/object/info/${n}`,{headers:this.headers});return{data:La(r),error:null}}catch(r){if(ne(r))return{data:null,error:r};throw r}})}exists(t){return ye(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{return yield By(this.fetch,`${this.url}/object/${n}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(ne(r)&&r instanceof Oa){const s=r.originalError;if([400,404].includes(s==null?void 0:s.status))return{data:!1,error:r}}throw r}})}getPublicUrl(t,n){const r=this._getFinalPath(t),s=[],i=n!=null&&n.download?`download=${n.download===!0?"":n.download}`:"";i!==""&&s.push(i);const l=typeof(n==null?void 0:n.transform)<"u"?"render/image":"object",u=this.transformOptsToQueryString((n==null?void 0:n.transform)||{});u!==""&&s.push(u);let c=s.join("&");return c!==""&&(c=`?${c}`),{data:{publicUrl:encodeURI(`${this.url}/${l}/public/${r}${c}`)}}}remove(t){return ye(this,void 0,void 0,function*(){try{return{data:yield jf(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:t},{headers:this.headers}),error:null}}catch(n){if(ne(n))return{data:null,error:n};throw n}})}list(t,n,r){return ye(this,void 0,void 0,function*(){try{const s=Object.assign(Object.assign(Object.assign({},Vy),n),{prefix:t||""});return{data:yield St(this.fetch,`${this.url}/object/list/${this.bucketId}`,s,{headers:this.headers},r),error:null}}catch(s){if(ne(s))return{data:null,error:s};throw s}})}encodeMetadata(t){return JSON.stringify(t)}toBase64(t){return typeof Buffer<"u"?Buffer.from(t).toString("base64"):btoa(t)}_getFinalPath(t){return`${this.bucketId}/${t}`}_removeEmptyFolders(t){return t.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(t){const n=[];return t.width&&n.push(`width=${t.width}`),t.height&&n.push(`height=${t.height}`),t.resize&&n.push(`resize=${t.resize}`),t.format&&n.push(`format=${t.format}`),t.quality&&n.push(`quality=${t.quality}`),n.join("&")}}const Wy="2.7.1",qy={"X-Client-Info":`storage-js/${Wy}`};var mn=globalThis&&globalThis.__awaiter||function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{c(r.next(d))}catch(h){o(h)}}function u(d){try{c(r.throw(d))}catch(h){o(h)}}function c(d){d.done?i(d.value):s(d.value).then(l,u)}c((r=r.apply(e,t||[])).next())})};class Ky{constructor(t,n={},r){this.url=t,this.headers=Object.assign(Object.assign({},qy),n),this.fetch=kf(r)}listBuckets(){return mn(this,void 0,void 0,function*(){try{return{data:yield wi(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(t){if(ne(t))return{data:null,error:t};throw t}})}getBucket(t){return mn(this,void 0,void 0,function*(){try{return{data:yield wi(this.fetch,`${this.url}/bucket/${t}`,{headers:this.headers}),error:null}}catch(n){if(ne(n))return{data:null,error:n};throw n}})}createBucket(t,n={public:!1}){return mn(this,void 0,void 0,function*(){try{return{data:yield St(this.fetch,`${this.url}/bucket`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(ne(r))return{data:null,error:r};throw r}})}updateBucket(t,n){return mn(this,void 0,void 0,function*(){try{return{data:yield Fy(this.fetch,`${this.url}/bucket/${t}`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(ne(r))return{data:null,error:r};throw r}})}emptyBucket(t){return mn(this,void 0,void 0,function*(){try{return{data:yield St(this.fetch,`${this.url}/bucket/${t}/empty`,{},{headers:this.headers}),error:null}}catch(n){if(ne(n))return{data:null,error:n};throw n}})}deleteBucket(t){return mn(this,void 0,void 0,function*(){try{return{data:yield jf(this.fetch,`${this.url}/bucket/${t}`,{},{headers:this.headers}),error:null}}catch(n){if(ne(n))return{data:null,error:n};throw n}})}}class Gy extends Ky{constructor(t,n={},r){super(t,n,r)}from(t){return new Hy(this.url,this.headers,t,this.fetch)}}const Qy="2.50.3";let wr="";typeof Deno<"u"?wr="deno":typeof document<"u"?wr="web":typeof navigator<"u"&&navigator.product==="ReactNative"?wr="react-native":wr="node";const Jy={"X-Client-Info":`supabase-js-${wr}/${Qy}`},Yy={headers:Jy},Xy={schema:"public"},Zy={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},e0={};var t0=globalThis&&globalThis.__awaiter||function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{c(r.next(d))}catch(h){o(h)}}function u(d){try{c(r.throw(d))}catch(h){o(h)}}function c(d){d.done?i(d.value):s(d.value).then(l,u)}c((r=r.apply(e,t||[])).next())})};const n0=e=>{let t;return e?t=e:typeof fetch>"u"?t=af:t=fetch,(...n)=>t(...n)},r0=()=>typeof Headers>"u"?lf:Headers,s0=(e,t,n)=>{const r=n0(n),s=r0();return(i,o)=>t0(void 0,void 0,void 0,function*(){var l;const u=(l=yield t())!==null&&l!==void 0?l:e;let c=new s(o==null?void 0:o.headers);return c.has("apikey")||c.set("apikey",e),c.has("Authorization")||c.set("Authorization",`Bearer ${u}`),r(i,Object.assign(Object.assign({},o),{headers:c}))})};var i0=globalThis&&globalThis.__awaiter||function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{c(r.next(d))}catch(h){o(h)}}function u(d){try{c(r.throw(d))}catch(h){o(h)}}function c(d){d.done?i(d.value):s(d.value).then(l,u)}c((r=r.apply(e,t||[])).next())})};function o0(e){return e.endsWith("/")?e:e+"/"}function a0(e,t){var n,r;const{db:s,auth:i,realtime:o,global:l}=e,{db:u,auth:c,realtime:d,global:h}=t,p={db:Object.assign(Object.assign({},u),s),auth:Object.assign(Object.assign({},c),i),realtime:Object.assign(Object.assign({},d),o),global:Object.assign(Object.assign(Object.assign({},h),l),{headers:Object.assign(Object.assign({},(n=h==null?void 0:h.headers)!==null&&n!==void 0?n:{}),(r=l==null?void 0:l.headers)!==null&&r!==void 0?r:{})}),accessToken:()=>i0(this,void 0,void 0,function*(){return""})};return e.accessToken?p.accessToken=e.accessToken:delete p.accessToken,p}const Sf="2.70.0",wn=30*1e3,$a=3,To=$a*wn,l0="http://localhost:9999",u0="supabase.auth.token",c0={"X-Client-Info":`gotrue-js/${Sf}`},Aa="X-Supabase-Api-Version",Ef={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},d0=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,h0=6e5;class Kl extends Error{constructor(t,n,r){super(t),this.__isAuthError=!0,this.name="AuthError",this.status=n,this.code=r}}function R(e){return typeof e=="object"&&e!==null&&"__isAuthError"in e}class f0 extends Kl{constructor(t,n,r){super(t,n,r),this.name="AuthApiError",this.status=n,this.code=r}}function p0(e){return R(e)&&e.name==="AuthApiError"}class Nf extends Kl{constructor(t,n){super(t),this.name="AuthUnknownError",this.originalError=n}}class Ht extends Kl{constructor(t,n,r,s){super(t,r,s),this.name=n,this.status=r}}class _t extends Ht{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function m0(e){return R(e)&&e.name==="AuthSessionMissingError"}class Ts extends Ht{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class bs extends Ht{constructor(t){super(t,"AuthInvalidCredentialsError",400,void 0)}}class Rs extends Ht{constructor(t,n=null){super(t,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function v0(e){return R(e)&&e.name==="AuthImplicitGrantRedirectError"}class kc extends Ht{constructor(t,n=null){super(t,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Ia extends Ht{constructor(t,n){super(t,"AuthRetryableFetchError",n,void 0)}}function bo(e){return R(e)&&e.name==="AuthRetryableFetchError"}class jc extends Ht{constructor(t,n,r){super(t,"AuthWeakPasswordError",n,"weak_password"),this.reasons=r}}class Rr extends Ht{constructor(t){super(t,"AuthInvalidJwtError",400,"invalid_jwt")}}const xi="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),Sc=` 	
\r=`.split(""),g0=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<Sc.length;t+=1)e[Sc[t].charCodeAt(0)]=-2;for(let t=0;t<xi.length;t+=1)e[xi[t].charCodeAt(0)]=t;return e})();function Ec(e,t,n){if(e!==null)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n(xi[r]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n(xi[r]),t.queuedBits-=6}}function Cf(e,t,n){const r=g0[e];if(r>-1)for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)n(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else{if(r===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}}function Nc(e){const t=[],n=o=>{t.push(String.fromCodePoint(o))},r={utf8seq:0,codepoint:0},s={queue:0,queuedBits:0},i=o=>{x0(o,r,n)};for(let o=0;o<e.length;o+=1)Cf(e.charCodeAt(o),s,i);return t.join("")}function y0(e,t){if(e<=127){t(e);return}else if(e<=2047){t(192|e>>6),t(128|e&63);return}else if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|e&63);return}else if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|e&63);return}throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function w0(e,t){for(let n=0;n<e.length;n+=1){let r=e.charCodeAt(n);if(r>55295&&r<=56319){const s=(r-55296)*1024&65535;r=(e.charCodeAt(n+1)-56320&65535|s)+65536,n+=1}y0(r,t)}}function x0(e,t,n){if(t.utf8seq===0){if(e<=127){n(e);return}for(let r=1;r<6;r+=1)if(!(e>>7-r&1)){t.utf8seq=r;break}if(t.utf8seq===2)t.codepoint=e&31;else if(t.utf8seq===3)t.codepoint=e&15;else if(t.utf8seq===4)t.codepoint=e&7;else throw new Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|e&63,t.utf8seq-=1,t.utf8seq===0&&n(t.codepoint)}}function _0(e){const t=[],n={queue:0,queuedBits:0},r=s=>{t.push(s)};for(let s=0;s<e.length;s+=1)Cf(e.charCodeAt(s),n,r);return new Uint8Array(t)}function k0(e){const t=[];return w0(e,n=>t.push(n)),new Uint8Array(t)}function j0(e){const t=[],n={queue:0,queuedBits:0},r=s=>{t.push(s)};return e.forEach(s=>Ec(s,n,r)),Ec(null,n,r),t.join("")}function S0(e){return Math.round(Date.now()/1e3)+e}function E0(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=Math.random()*16|0;return(e=="x"?t:t&3|8).toString(16)})}const Me=()=>typeof window<"u"&&typeof document<"u",Gt={tested:!1,writable:!1},Or=()=>{if(!Me())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(Gt.tested)return Gt.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),Gt.tested=!0,Gt.writable=!0}catch{Gt.tested=!0,Gt.writable=!1}return Gt.writable};function N0(e){const t={},n=new URL(e);if(n.hash&&n.hash[0]==="#")try{new URLSearchParams(n.hash.substring(1)).forEach((s,i)=>{t[i]=s})}catch{}return n.searchParams.forEach((r,s)=>{t[s]=r}),t}const Pf=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>Qn(()=>Promise.resolve().then(()=>Zn),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},C0=e=>typeof e=="object"&&e!==null&&"status"in e&&"ok"in e&&"json"in e&&typeof e.json=="function",Tf=async(e,t,n)=>{await e.setItem(t,JSON.stringify(n))},Os=async(e,t)=>{const n=await e.getItem(t);if(!n)return null;try{return JSON.parse(n)}catch{return n}},Ls=async(e,t)=>{await e.removeItem(t)};class qi{constructor(){this.promise=new qi.promiseConstructor((t,n)=>{this.resolve=t,this.reject=n})}}qi.promiseConstructor=Promise;function Ro(e){const t=e.split(".");if(t.length!==3)throw new Rr("Invalid JWT structure");for(let r=0;r<t.length;r++)if(!d0.test(t[r]))throw new Rr("JWT not in base64url format");return{header:JSON.parse(Nc(t[0])),payload:JSON.parse(Nc(t[1])),signature:_0(t[2]),raw:{header:t[0],payload:t[1]}}}async function P0(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function T0(e,t){return new Promise((r,s)=>{(async()=>{for(let i=0;i<1/0;i++)try{const o=await e(i);if(!t(i,null,o)){r(o);return}}catch(o){if(!t(i,o)){s(o);return}}})()})}function b0(e){return("0"+e.toString(16)).substr(-2)}function R0(){const t=new Uint32Array(56);if(typeof crypto>"u"){const n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",r=n.length;let s="";for(let i=0;i<56;i++)s+=n.charAt(Math.floor(Math.random()*r));return s}return crypto.getRandomValues(t),Array.from(t,b0).join("")}async function O0(e){const n=new TextEncoder().encode(e),r=await crypto.subtle.digest("SHA-256",n),s=new Uint8Array(r);return Array.from(s).map(i=>String.fromCharCode(i)).join("")}async function L0(e){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const n=await O0(e);return btoa(n).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function vn(e,t,n=!1){const r=R0();let s=r;n&&(s+="/PASSWORD_RECOVERY"),await Tf(e,`${t}-code-verifier`,s);const i=await L0(r);return[i,r===i?"plain":"s256"]}const $0=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function A0(e){const t=e.headers.get(Aa);if(!t||!t.match($0))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch{return null}}function I0(e){if(!e)throw new Error("Missing exp claim");const t=Math.floor(Date.now()/1e3);if(e<=t)throw new Error("JWT has expired")}function z0(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}const D0=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function gn(e){if(!D0.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var U0=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]]);return n};const Yt=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),M0=[502,503,504];async function Cc(e){var t;if(!C0(e))throw new Ia(Yt(e),0);if(M0.includes(e.status))throw new Ia(Yt(e),e.status);let n;try{n=await e.json()}catch(i){throw new Nf(Yt(i),i)}let r;const s=A0(e);if(s&&s.getTime()>=Ef["2024-01-01"].timestamp&&typeof n=="object"&&n&&typeof n.code=="string"?r=n.code:typeof n=="object"&&n&&typeof n.error_code=="string"&&(r=n.error_code),r){if(r==="weak_password")throw new jc(Yt(n),e.status,((t=n.weak_password)===null||t===void 0?void 0:t.reasons)||[]);if(r==="session_not_found")throw new _t}else if(typeof n=="object"&&n&&typeof n.weak_password=="object"&&n.weak_password&&Array.isArray(n.weak_password.reasons)&&n.weak_password.reasons.length&&n.weak_password.reasons.reduce((i,o)=>i&&typeof o=="string",!0))throw new jc(Yt(n),e.status,n.weak_password.reasons);throw new f0(Yt(n),e.status||500,r)}const F0=(e,t,n,r)=>{const s={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?s:(s.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},t==null?void 0:t.headers),s.body=JSON.stringify(r),Object.assign(Object.assign({},s),n))};async function $(e,t,n,r){var s;const i=Object.assign({},r==null?void 0:r.headers);i[Aa]||(i[Aa]=Ef["2024-01-01"].name),r!=null&&r.jwt&&(i.Authorization=`Bearer ${r.jwt}`);const o=(s=r==null?void 0:r.query)!==null&&s!==void 0?s:{};r!=null&&r.redirectTo&&(o.redirect_to=r.redirectTo);const l=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",u=await B0(e,t,n+l,{headers:i,noResolveJson:r==null?void 0:r.noResolveJson},{},r==null?void 0:r.body);return r!=null&&r.xform?r==null?void 0:r.xform(u):{data:Object.assign({},u),error:null}}async function B0(e,t,n,r,s,i){const o=F0(t,r,s,i);let l;try{l=await e(n,Object.assign({},o))}catch(u){throw console.error(u),new Ia(Yt(u),0)}if(l.ok||await Cc(l),r!=null&&r.noResolveJson)return l;try{return await l.json()}catch(u){await Cc(u)}}function it(e){var t;let n=null;q0(e)&&(n=Object.assign({},e),e.expires_at||(n.expires_at=S0(e.expires_in)));const r=(t=e.user)!==null&&t!==void 0?t:e;return{data:{session:n,user:r},error:null}}function Pc(e){const t=it(e);return!t.error&&e.weak_password&&typeof e.weak_password=="object"&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&typeof e.weak_password.message=="string"&&e.weak_password.reasons.reduce((n,r)=>n&&typeof r=="string",!0)&&(t.data.weak_password=e.weak_password),t}function Ct(e){var t;return{data:{user:(t=e.user)!==null&&t!==void 0?t:e},error:null}}function V0(e){return{data:e,error:null}}function H0(e){const{action_link:t,email_otp:n,hashed_token:r,redirect_to:s,verification_type:i}=e,o=U0(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),l={action_link:t,email_otp:n,hashed_token:r,redirect_to:s,verification_type:i},u=Object.assign({},o);return{data:{properties:l,user:u},error:null}}function W0(e){return e}function q0(e){return e.access_token&&e.refresh_token&&e.expires_in}const Oo=["global","local","others"];var K0=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]]);return n};class G0{constructor({url:t="",headers:n={},fetch:r}){this.url=t,this.headers=n,this.fetch=Pf(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(t,n=Oo[0]){if(Oo.indexOf(n)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${Oo.join(", ")}`);try{return await $(this.fetch,"POST",`${this.url}/logout?scope=${n}`,{headers:this.headers,jwt:t,noResolveJson:!0}),{data:null,error:null}}catch(r){if(R(r))return{data:null,error:r};throw r}}async inviteUserByEmail(t,n={}){try{return await $(this.fetch,"POST",`${this.url}/invite`,{body:{email:t,data:n.data},headers:this.headers,redirectTo:n.redirectTo,xform:Ct})}catch(r){if(R(r))return{data:{user:null},error:r};throw r}}async generateLink(t){try{const{options:n}=t,r=K0(t,["options"]),s=Object.assign(Object.assign({},r),n);return"newEmail"in r&&(s.new_email=r==null?void 0:r.newEmail,delete s.newEmail),await $(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:s,headers:this.headers,xform:H0,redirectTo:n==null?void 0:n.redirectTo})}catch(n){if(R(n))return{data:{properties:null,user:null},error:n};throw n}}async createUser(t){try{return await $(this.fetch,"POST",`${this.url}/admin/users`,{body:t,headers:this.headers,xform:Ct})}catch(n){if(R(n))return{data:{user:null},error:n};throw n}}async listUsers(t){var n,r,s,i,o,l,u;try{const c={nextPage:null,lastPage:0,total:0},d=await $(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(r=(n=t==null?void 0:t.page)===null||n===void 0?void 0:n.toString())!==null&&r!==void 0?r:"",per_page:(i=(s=t==null?void 0:t.perPage)===null||s===void 0?void 0:s.toString())!==null&&i!==void 0?i:""},xform:W0});if(d.error)throw d.error;const h=await d.json(),p=(o=d.headers.get("x-total-count"))!==null&&o!==void 0?o:0,g=(u=(l=d.headers.get("link"))===null||l===void 0?void 0:l.split(","))!==null&&u!==void 0?u:[];return g.length>0&&(g.forEach(w=>{const y=parseInt(w.split(";")[0].split("=")[1].substring(0,1)),k=JSON.parse(w.split(";")[1].split("=")[1]);c[`${k}Page`]=y}),c.total=parseInt(p)),{data:Object.assign(Object.assign({},h),c),error:null}}catch(c){if(R(c))return{data:{users:[]},error:c};throw c}}async getUserById(t){gn(t);try{return await $(this.fetch,"GET",`${this.url}/admin/users/${t}`,{headers:this.headers,xform:Ct})}catch(n){if(R(n))return{data:{user:null},error:n};throw n}}async updateUserById(t,n){gn(t);try{return await $(this.fetch,"PUT",`${this.url}/admin/users/${t}`,{body:n,headers:this.headers,xform:Ct})}catch(r){if(R(r))return{data:{user:null},error:r};throw r}}async deleteUser(t,n=!1){gn(t);try{return await $(this.fetch,"DELETE",`${this.url}/admin/users/${t}`,{headers:this.headers,body:{should_soft_delete:n},xform:Ct})}catch(r){if(R(r))return{data:{user:null},error:r};throw r}}async _listFactors(t){gn(t.userId);try{const{data:n,error:r}=await $(this.fetch,"GET",`${this.url}/admin/users/${t.userId}/factors`,{headers:this.headers,xform:s=>({data:{factors:s},error:null})});return{data:n,error:r}}catch(n){if(R(n))return{data:null,error:n};throw n}}async _deleteFactor(t){gn(t.userId),gn(t.id);try{return{data:await $(this.fetch,"DELETE",`${this.url}/admin/users/${t.userId}/factors/${t.id}`,{headers:this.headers}),error:null}}catch(n){if(R(n))return{data:null,error:n};throw n}}}const Q0={getItem:e=>Or()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{Or()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{Or()&&globalThis.localStorage.removeItem(e)}};function Tc(e={}){return{getItem:t=>e[t]||null,setItem:(t,n)=>{e[t]=n},removeItem:t=>{delete e[t]}}}function J0(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const yn={debug:!!(globalThis&&Or()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class bf extends Error{constructor(t){super(t),this.isAcquireTimeout=!0}}class Y0 extends bf{}async function X0(e,t,n){yn.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const r=new globalThis.AbortController;return t>0&&setTimeout(()=>{r.abort(),yn.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,t===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async s=>{if(s){yn.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,s.name);try{return await n()}finally{yn.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,s.name)}}else{if(t===0)throw yn.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new Y0(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(yn.debug)try{const i=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(i,null,"  "))}catch(i){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",i)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await n()}}))}J0();const Z0={url:l0,storageKey:u0,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:c0,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function bc(e,t,n){return await n()}class Yr{constructor(t){var n,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=Yr.nextInstanceID,Yr.nextInstanceID+=1,this.instanceID>0&&Me()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const s=Object.assign(Object.assign({},Z0),t);if(this.logDebugMessages=!!s.debug,typeof s.debug=="function"&&(this.logger=s.debug),this.persistSession=s.persistSession,this.storageKey=s.storageKey,this.autoRefreshToken=s.autoRefreshToken,this.admin=new G0({url:s.url,headers:s.headers,fetch:s.fetch}),this.url=s.url,this.headers=s.headers,this.fetch=Pf(s.fetch),this.lock=s.lock||bc,this.detectSessionInUrl=s.detectSessionInUrl,this.flowType=s.flowType,this.hasCustomAuthorizationHeader=s.hasCustomAuthorizationHeader,s.lock?this.lock=s.lock:Me()&&(!((n=globalThis==null?void 0:globalThis.navigator)===null||n===void 0)&&n.locks)?this.lock=X0:this.lock=bc,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?s.storage?this.storage=s.storage:Or()?this.storage=Q0:(this.memoryStorage={},this.storage=Tc(this.memoryStorage)):(this.memoryStorage={},this.storage=Tc(this.memoryStorage)),Me()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(i){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",i)}(r=this.broadcastChannel)===null||r===void 0||r.addEventListener("message",async i=>{this._debug("received broadcast notification from other tab or client",i),await this._notifyAllSubscribers(i.data.event,i.data.session,!1)})}this.initialize()}_debug(...t){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${Sf}) ${new Date().toISOString()}`,...t),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var t;try{const n=N0(window.location.href);let r="none";if(this._isImplicitGrantCallback(n)?r="implicit":await this._isPKCECallback(n)&&(r="pkce"),Me()&&this.detectSessionInUrl&&r!=="none"){const{data:s,error:i}=await this._getSessionFromURL(n,r);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),v0(i)){const u=(t=i.details)===null||t===void 0?void 0:t.code;if(u==="identity_already_exists"||u==="identity_not_found"||u==="single_identity_not_deletable")return{error:i}}return await this._removeSession(),{error:i}}const{session:o,redirectType:l}=s;return this._debug("#_initialize()","detected session in URL",o,"redirect type",l),await this._saveSession(o),setTimeout(async()=>{l==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",o):await this._notifyAllSubscribers("SIGNED_IN",o)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(n){return R(n)?{error:n}:{error:new Nf("Unexpected error during initialization",n)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(t){var n,r,s;try{const i=await $(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(r=(n=t==null?void 0:t.options)===null||n===void 0?void 0:n.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:(s=t==null?void 0:t.options)===null||s===void 0?void 0:s.captchaToken}},xform:it}),{data:o,error:l}=i;if(l||!o)return{data:{user:null,session:null},error:l};const u=o.session,c=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",u)),{data:{user:c,session:u},error:null}}catch(i){if(R(i))return{data:{user:null,session:null},error:i};throw i}}async signUp(t){var n,r,s;try{let i;if("email"in t){const{email:d,password:h,options:p}=t;let g=null,w=null;this.flowType==="pkce"&&([g,w]=await vn(this.storage,this.storageKey)),i=await $(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:p==null?void 0:p.emailRedirectTo,body:{email:d,password:h,data:(n=p==null?void 0:p.data)!==null&&n!==void 0?n:{},gotrue_meta_security:{captcha_token:p==null?void 0:p.captchaToken},code_challenge:g,code_challenge_method:w},xform:it})}else if("phone"in t){const{phone:d,password:h,options:p}=t;i=await $(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:d,password:h,data:(r=p==null?void 0:p.data)!==null&&r!==void 0?r:{},channel:(s=p==null?void 0:p.channel)!==null&&s!==void 0?s:"sms",gotrue_meta_security:{captcha_token:p==null?void 0:p.captchaToken}},xform:it})}else throw new bs("You must provide either an email or phone number and a password");const{data:o,error:l}=i;if(l||!o)return{data:{user:null,session:null},error:l};const u=o.session,c=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",u)),{data:{user:c,session:u},error:null}}catch(i){if(R(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithPassword(t){try{let n;if("email"in t){const{email:i,password:o,options:l}=t;n=await $(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:i,password:o,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken}},xform:Pc})}else if("phone"in t){const{phone:i,password:o,options:l}=t;n=await $(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:i,password:o,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken}},xform:Pc})}else throw new bs("You must provide either an email or phone number and a password");const{data:r,error:s}=n;return s?{data:{user:null,session:null},error:s}:!r||!r.session||!r.user?{data:{user:null,session:null},error:new Ts}:(r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:s})}catch(n){if(R(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOAuth(t){var n,r,s,i;return await this._handleProviderSignIn(t.provider,{redirectTo:(n=t.options)===null||n===void 0?void 0:n.redirectTo,scopes:(r=t.options)===null||r===void 0?void 0:r.scopes,queryParams:(s=t.options)===null||s===void 0?void 0:s.queryParams,skipBrowserRedirect:(i=t.options)===null||i===void 0?void 0:i.skipBrowserRedirect})}async exchangeCodeForSession(t){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(t))}async signInWithWeb3(t){const{chain:n}=t;if(n==="solana")return await this.signInWithSolana(t);throw new Error(`@supabase/auth-js: Unsupported chain "${n}"`)}async signInWithSolana(t){var n,r,s,i,o,l,u,c,d,h,p,g;let w,y;if("message"in t)w=t.message,y=t.signature;else{const{chain:k,wallet:m,statement:f,options:v}=t;let _;if(Me())if(typeof m=="object")_=m;else{const E=window;if("solana"in E&&typeof E.solana=="object"&&("signIn"in E.solana&&typeof E.solana.signIn=="function"||"signMessage"in E.solana&&typeof E.solana.signMessage=="function"))_=E.solana;else throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if(typeof m!="object"||!(v!=null&&v.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");_=m}const j=new URL((n=v==null?void 0:v.url)!==null&&n!==void 0?n:window.location.href);if("signIn"in _&&_.signIn){const E=await _.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},v==null?void 0:v.signInWithSolana),{version:"1",domain:j.host,uri:j.href}),f?{statement:f}:null));let N;if(Array.isArray(E)&&E[0]&&typeof E[0]=="object")N=E[0];else if(E&&typeof E=="object"&&"signedMessage"in E&&"signature"in E)N=E;else throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in N&&"signature"in N&&(typeof N.signedMessage=="string"||N.signedMessage instanceof Uint8Array)&&N.signature instanceof Uint8Array)w=typeof N.signedMessage=="string"?N.signedMessage:new TextDecoder().decode(N.signedMessage),y=N.signature;else throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in _)||typeof _.signMessage!="function"||!("publicKey"in _)||typeof _!="object"||!_.publicKey||!("toBase58"in _.publicKey)||typeof _.publicKey.toBase58!="function")throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");w=[`${j.host} wants you to sign in with your Solana account:`,_.publicKey.toBase58(),...f?["",f,""]:[""],"Version: 1",`URI: ${j.href}`,`Issued At: ${(s=(r=v==null?void 0:v.signInWithSolana)===null||r===void 0?void 0:r.issuedAt)!==null&&s!==void 0?s:new Date().toISOString()}`,...!((i=v==null?void 0:v.signInWithSolana)===null||i===void 0)&&i.notBefore?[`Not Before: ${v.signInWithSolana.notBefore}`]:[],...!((o=v==null?void 0:v.signInWithSolana)===null||o===void 0)&&o.expirationTime?[`Expiration Time: ${v.signInWithSolana.expirationTime}`]:[],...!((l=v==null?void 0:v.signInWithSolana)===null||l===void 0)&&l.chainId?[`Chain ID: ${v.signInWithSolana.chainId}`]:[],...!((u=v==null?void 0:v.signInWithSolana)===null||u===void 0)&&u.nonce?[`Nonce: ${v.signInWithSolana.nonce}`]:[],...!((c=v==null?void 0:v.signInWithSolana)===null||c===void 0)&&c.requestId?[`Request ID: ${v.signInWithSolana.requestId}`]:[],...!((h=(d=v==null?void 0:v.signInWithSolana)===null||d===void 0?void 0:d.resources)===null||h===void 0)&&h.length?["Resources",...v.signInWithSolana.resources.map(N=>`- ${N}`)]:[]].join(`
`);const E=await _.signMessage(new TextEncoder().encode(w),"utf8");if(!E||!(E instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");y=E}}try{const{data:k,error:m}=await $(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:w,signature:j0(y)},!((p=t.options)===null||p===void 0)&&p.captchaToken?{gotrue_meta_security:{captcha_token:(g=t.options)===null||g===void 0?void 0:g.captchaToken}}:null),xform:it});if(m)throw m;return!k||!k.session||!k.user?{data:{user:null,session:null},error:new Ts}:(k.session&&(await this._saveSession(k.session),await this._notifyAllSubscribers("SIGNED_IN",k.session)),{data:Object.assign({},k),error:m})}catch(k){if(R(k))return{data:{user:null,session:null},error:k};throw k}}async _exchangeCodeForSession(t){const n=await Os(this.storage,`${this.storageKey}-code-verifier`),[r,s]=(n??"").split("/");try{const{data:i,error:o}=await $(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:t,code_verifier:r},xform:it});if(await Ls(this.storage,`${this.storageKey}-code-verifier`),o)throw o;return!i||!i.session||!i.user?{data:{user:null,session:null,redirectType:null},error:new Ts}:(i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",i.session)),{data:Object.assign(Object.assign({},i),{redirectType:s??null}),error:o})}catch(i){if(R(i))return{data:{user:null,session:null,redirectType:null},error:i};throw i}}async signInWithIdToken(t){try{const{options:n,provider:r,token:s,access_token:i,nonce:o}=t,l=await $(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:s,access_token:i,nonce:o,gotrue_meta_security:{captcha_token:n==null?void 0:n.captchaToken}},xform:it}),{data:u,error:c}=l;return c?{data:{user:null,session:null},error:c}:!u||!u.session||!u.user?{data:{user:null,session:null},error:new Ts}:(u.session&&(await this._saveSession(u.session),await this._notifyAllSubscribers("SIGNED_IN",u.session)),{data:u,error:c})}catch(n){if(R(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOtp(t){var n,r,s,i,o;try{if("email"in t){const{email:l,options:u}=t;let c=null,d=null;this.flowType==="pkce"&&([c,d]=await vn(this.storage,this.storageKey));const{error:h}=await $(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:l,data:(n=u==null?void 0:u.data)!==null&&n!==void 0?n:{},create_user:(r=u==null?void 0:u.shouldCreateUser)!==null&&r!==void 0?r:!0,gotrue_meta_security:{captcha_token:u==null?void 0:u.captchaToken},code_challenge:c,code_challenge_method:d},redirectTo:u==null?void 0:u.emailRedirectTo});return{data:{user:null,session:null},error:h}}if("phone"in t){const{phone:l,options:u}=t,{data:c,error:d}=await $(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:l,data:(s=u==null?void 0:u.data)!==null&&s!==void 0?s:{},create_user:(i=u==null?void 0:u.shouldCreateUser)!==null&&i!==void 0?i:!0,gotrue_meta_security:{captcha_token:u==null?void 0:u.captchaToken},channel:(o=u==null?void 0:u.channel)!==null&&o!==void 0?o:"sms"}});return{data:{user:null,session:null,messageId:c==null?void 0:c.message_id},error:d}}throw new bs("You must provide either an email or phone number.")}catch(l){if(R(l))return{data:{user:null,session:null},error:l};throw l}}async verifyOtp(t){var n,r;try{let s,i;"options"in t&&(s=(n=t.options)===null||n===void 0?void 0:n.redirectTo,i=(r=t.options)===null||r===void 0?void 0:r.captchaToken);const{data:o,error:l}=await $(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},t),{gotrue_meta_security:{captcha_token:i}}),redirectTo:s,xform:it});if(l)throw l;if(!o)throw new Error("An error occurred on token verification.");const u=o.session,c=o.user;return u!=null&&u.access_token&&(await this._saveSession(u),await this._notifyAllSubscribers(t.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",u)),{data:{user:c,session:u},error:null}}catch(s){if(R(s))return{data:{user:null,session:null},error:s};throw s}}async signInWithSSO(t){var n,r,s;try{let i=null,o=null;return this.flowType==="pkce"&&([i,o]=await vn(this.storage,this.storageKey)),await $(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in t?{provider_id:t.providerId}:null),"domain"in t?{domain:t.domain}:null),{redirect_to:(r=(n=t.options)===null||n===void 0?void 0:n.redirectTo)!==null&&r!==void 0?r:void 0}),!((s=t==null?void 0:t.options)===null||s===void 0)&&s.captchaToken?{gotrue_meta_security:{captcha_token:t.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:o}),headers:this.headers,xform:V0})}catch(i){if(R(i))return{data:null,error:i};throw i}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async t=>{const{data:{session:n},error:r}=t;if(r)throw r;if(!n)throw new _t;const{error:s}=await $(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:n.access_token});return{data:{user:null,session:null},error:s}})}catch(t){if(R(t))return{data:{user:null,session:null},error:t};throw t}}async resend(t){try{const n=`${this.url}/resend`;if("email"in t){const{email:r,type:s,options:i}=t,{error:o}=await $(this.fetch,"POST",n,{headers:this.headers,body:{email:r,type:s,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}},redirectTo:i==null?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:o}}else if("phone"in t){const{phone:r,type:s,options:i}=t,{data:o,error:l}=await $(this.fetch,"POST",n,{headers:this.headers,body:{phone:r,type:s,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:o==null?void 0:o.message_id},error:l}}throw new bs("You must provide either an email or phone number and a type")}catch(n){if(R(n))return{data:{user:null,session:null},error:n};throw n}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async n=>n))}async _acquireLock(t,n){this._debug("#_acquireLock","begin",t);try{if(this.lockAcquired){const r=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(async()=>(await r,await n()))();return this.pendingInLock.push((async()=>{try{await s}catch{}})()),s}return await this.lock(`lock:${this.storageKey}`,t,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const r=n();for(this.pendingInLock.push((async()=>{try{await r}catch{}})()),await r;this.pendingInLock.length;){const s=[...this.pendingInLock];await Promise.all(s),this.pendingInLock.splice(0,s.length)}return await r}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(t){this._debug("#_useSession","begin");try{const n=await this.__loadSession();return await t(n)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let t=null;const n=await Os(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",n),n!==null&&(this._isValidSession(n)?t=n:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!t)return{data:{session:null},error:null};const r=t.expires_at?t.expires_at*1e3-Date.now()<To:!1;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",t.expires_at),!r){if(this.storage.isServer){let o=this.suppressGetSessionWarning;t=new Proxy(t,{get:(u,c,d)=>(!o&&c==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),o=!0,this.suppressGetSessionWarning=!0),Reflect.get(u,c,d))})}return{data:{session:t},error:null}}const{session:s,error:i}=await this._callRefreshToken(t.refresh_token);return i?{data:{session:null},error:i}:{data:{session:s},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(t){return t?await this._getUser(t):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(t){try{return t?await $(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:t,xform:Ct}):await this._useSession(async n=>{var r,s,i;const{data:o,error:l}=n;if(l)throw l;return!(!((r=o.session)===null||r===void 0)&&r.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new _t}:await $(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(i=(s=o.session)===null||s===void 0?void 0:s.access_token)!==null&&i!==void 0?i:void 0,xform:Ct})})}catch(n){if(R(n))return m0(n)&&(await this._removeSession(),await Ls(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:n};throw n}}async updateUser(t,n={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(t,n))}async _updateUser(t,n={}){try{return await this._useSession(async r=>{const{data:s,error:i}=r;if(i)throw i;if(!s.session)throw new _t;const o=s.session;let l=null,u=null;this.flowType==="pkce"&&t.email!=null&&([l,u]=await vn(this.storage,this.storageKey));const{data:c,error:d}=await $(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:n==null?void 0:n.emailRedirectTo,body:Object.assign(Object.assign({},t),{code_challenge:l,code_challenge_method:u}),jwt:o.access_token,xform:Ct});if(d)throw d;return o.user=c.user,await this._saveSession(o),await this._notifyAllSubscribers("USER_UPDATED",o),{data:{user:o.user},error:null}})}catch(r){if(R(r))return{data:{user:null},error:r};throw r}}async setSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(t))}async _setSession(t){try{if(!t.access_token||!t.refresh_token)throw new _t;const n=Date.now()/1e3;let r=n,s=!0,i=null;const{payload:o}=Ro(t.access_token);if(o.exp&&(r=o.exp,s=r<=n),s){const{session:l,error:u}=await this._callRefreshToken(t.refresh_token);if(u)return{data:{user:null,session:null},error:u};if(!l)return{data:{user:null,session:null},error:null};i=l}else{const{data:l,error:u}=await this._getUser(t.access_token);if(u)throw u;i={access_token:t.access_token,refresh_token:t.refresh_token,user:l.user,token_type:"bearer",expires_in:r-n,expires_at:r},await this._saveSession(i),await this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(n){if(R(n))return{data:{session:null,user:null},error:n};throw n}}async refreshSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(t))}async _refreshSession(t){try{return await this._useSession(async n=>{var r;if(!t){const{data:o,error:l}=n;if(l)throw l;t=(r=o.session)!==null&&r!==void 0?r:void 0}if(!(t!=null&&t.refresh_token))throw new _t;const{session:s,error:i}=await this._callRefreshToken(t.refresh_token);return i?{data:{user:null,session:null},error:i}:s?{data:{user:s.user,session:s},error:null}:{data:{user:null,session:null},error:null}})}catch(n){if(R(n))return{data:{user:null,session:null},error:n};throw n}}async _getSessionFromURL(t,n){try{if(!Me())throw new Rs("No browser detected.");if(t.error||t.error_description||t.error_code)throw new Rs(t.error_description||"Error in URL with unspecified error_description",{error:t.error||"unspecified_error",code:t.error_code||"unspecified_code"});switch(n){case"implicit":if(this.flowType==="pkce")throw new kc("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new Rs("Not a valid implicit grant flow url.");break;default:}if(n==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!t.code)throw new kc("No code detected.");const{data:f,error:v}=await this._exchangeCodeForSession(t.code);if(v)throw v;const _=new URL(window.location.href);return _.searchParams.delete("code"),window.history.replaceState(window.history.state,"",_.toString()),{data:{session:f.session,redirectType:null},error:null}}const{provider_token:r,provider_refresh_token:s,access_token:i,refresh_token:o,expires_in:l,expires_at:u,token_type:c}=t;if(!i||!l||!o||!c)throw new Rs("No session defined in URL");const d=Math.round(Date.now()/1e3),h=parseInt(l);let p=d+h;u&&(p=parseInt(u));const g=p-d;g*1e3<=wn&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${g}s, should have been closer to ${h}s`);const w=p-h;d-w>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",w,p,d):d-w<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",w,p,d);const{data:y,error:k}=await this._getUser(i);if(k)throw k;const m={provider_token:r,provider_refresh_token:s,access_token:i,expires_in:h,expires_at:p,refresh_token:o,token_type:c,user:y.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:m,redirectType:t.type},error:null}}catch(r){if(R(r))return{data:{session:null,redirectType:null},error:r};throw r}}_isImplicitGrantCallback(t){return!!(t.access_token||t.error_description)}async _isPKCECallback(t){const n=await Os(this.storage,`${this.storageKey}-code-verifier`);return!!(t.code&&n)}async signOut(t={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(t))}async _signOut({scope:t}={scope:"global"}){return await this._useSession(async n=>{var r;const{data:s,error:i}=n;if(i)return{error:i};const o=(r=s.session)===null||r===void 0?void 0:r.access_token;if(o){const{error:l}=await this.admin.signOut(o,t);if(l&&!(p0(l)&&(l.status===404||l.status===401||l.status===403)))return{error:l}}return t!=="others"&&(await this._removeSession(),await Ls(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(t){const n=E0(),r={id:n,callback:t,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",n),this.stateChangeEmitters.delete(n)}};return this._debug("#onAuthStateChange()","registered callback with id",n),this.stateChangeEmitters.set(n,r),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(n)})))(),{data:{subscription:r}}}async _emitInitialSession(t){return await this._useSession(async n=>{var r,s;try{const{data:{session:i},error:o}=n;if(o)throw o;await((r=this.stateChangeEmitters.get(t))===null||r===void 0?void 0:r.callback("INITIAL_SESSION",i)),this._debug("INITIAL_SESSION","callback id",t,"session",i)}catch(i){await((s=this.stateChangeEmitters.get(t))===null||s===void 0?void 0:s.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",t,"error",i),console.error(i)}})}async resetPasswordForEmail(t,n={}){let r=null,s=null;this.flowType==="pkce"&&([r,s]=await vn(this.storage,this.storageKey,!0));try{return await $(this.fetch,"POST",`${this.url}/recover`,{body:{email:t,code_challenge:r,code_challenge_method:s,gotrue_meta_security:{captcha_token:n.captchaToken}},headers:this.headers,redirectTo:n.redirectTo})}catch(i){if(R(i))return{data:null,error:i};throw i}}async getUserIdentities(){var t;try{const{data:n,error:r}=await this.getUser();if(r)throw r;return{data:{identities:(t=n.user.identities)!==null&&t!==void 0?t:[]},error:null}}catch(n){if(R(n))return{data:null,error:n};throw n}}async linkIdentity(t){var n;try{const{data:r,error:s}=await this._useSession(async i=>{var o,l,u,c,d;const{data:h,error:p}=i;if(p)throw p;const g=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,t.provider,{redirectTo:(o=t.options)===null||o===void 0?void 0:o.redirectTo,scopes:(l=t.options)===null||l===void 0?void 0:l.scopes,queryParams:(u=t.options)===null||u===void 0?void 0:u.queryParams,skipBrowserRedirect:!0});return await $(this.fetch,"GET",g,{headers:this.headers,jwt:(d=(c=h.session)===null||c===void 0?void 0:c.access_token)!==null&&d!==void 0?d:void 0})});if(s)throw s;return Me()&&!(!((n=t.options)===null||n===void 0)&&n.skipBrowserRedirect)&&window.location.assign(r==null?void 0:r.url),{data:{provider:t.provider,url:r==null?void 0:r.url},error:null}}catch(r){if(R(r))return{data:{provider:t.provider,url:null},error:r};throw r}}async unlinkIdentity(t){try{return await this._useSession(async n=>{var r,s;const{data:i,error:o}=n;if(o)throw o;return await $(this.fetch,"DELETE",`${this.url}/user/identities/${t.identity_id}`,{headers:this.headers,jwt:(s=(r=i.session)===null||r===void 0?void 0:r.access_token)!==null&&s!==void 0?s:void 0})})}catch(n){if(R(n))return{data:null,error:n};throw n}}async _refreshAccessToken(t){const n=`#_refreshAccessToken(${t.substring(0,5)}...)`;this._debug(n,"begin");try{const r=Date.now();return await T0(async s=>(s>0&&await P0(200*Math.pow(2,s-1)),this._debug(n,"refreshing attempt",s),await $(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:t},headers:this.headers,xform:it})),(s,i)=>{const o=200*Math.pow(2,s);return i&&bo(i)&&Date.now()+o-r<wn})}catch(r){if(this._debug(n,"error",r),R(r))return{data:{session:null,user:null},error:r};throw r}finally{this._debug(n,"end")}}_isValidSession(t){return typeof t=="object"&&t!==null&&"access_token"in t&&"refresh_token"in t&&"expires_at"in t}async _handleProviderSignIn(t,n){const r=await this._getUrlForProvider(`${this.url}/authorize`,t,{redirectTo:n.redirectTo,scopes:n.scopes,queryParams:n.queryParams});return this._debug("#_handleProviderSignIn()","provider",t,"options",n,"url",r),Me()&&!n.skipBrowserRedirect&&window.location.assign(r),{data:{provider:t,url:r},error:null}}async _recoverAndRefresh(){var t;const n="#_recoverAndRefresh()";this._debug(n,"begin");try{const r=await Os(this.storage,this.storageKey);if(this._debug(n,"session from storage",r),!this._isValidSession(r)){this._debug(n,"session is not valid"),r!==null&&await this._removeSession();return}const s=((t=r.expires_at)!==null&&t!==void 0?t:1/0)*1e3-Date.now()<To;if(this._debug(n,`session has${s?"":" not"} expired with margin of ${To}s`),s){if(this.autoRefreshToken&&r.refresh_token){const{error:i}=await this._callRefreshToken(r.refresh_token);i&&(console.error(i),bo(i)||(this._debug(n,"refresh failed with a non-retryable error, removing the session",i),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(r){this._debug(n,"error",r),console.error(r);return}finally{this._debug(n,"end")}}async _callRefreshToken(t){var n,r;if(!t)throw new _t;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const s=`#_callRefreshToken(${t.substring(0,5)}...)`;this._debug(s,"begin");try{this.refreshingDeferred=new qi;const{data:i,error:o}=await this._refreshAccessToken(t);if(o)throw o;if(!i.session)throw new _t;await this._saveSession(i.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",i.session);const l={session:i.session,error:null};return this.refreshingDeferred.resolve(l),l}catch(i){if(this._debug(s,"error",i),R(i)){const o={session:null,error:i};return bo(i)||await this._removeSession(),(n=this.refreshingDeferred)===null||n===void 0||n.resolve(o),o}throw(r=this.refreshingDeferred)===null||r===void 0||r.reject(i),i}finally{this.refreshingDeferred=null,this._debug(s,"end")}}async _notifyAllSubscribers(t,n,r=!0){const s=`#_notifyAllSubscribers(${t})`;this._debug(s,"begin",n,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:t,session:n});const i=[],o=Array.from(this.stateChangeEmitters.values()).map(async l=>{try{await l.callback(t,n)}catch(u){i.push(u)}});if(await Promise.all(o),i.length>0){for(let l=0;l<i.length;l+=1)console.error(i[l]);throw i[0]}}finally{this._debug(s,"end")}}async _saveSession(t){this._debug("#_saveSession()",t),this.suppressGetSessionWarning=!0,await Tf(this.storage,this.storageKey,t)}async _removeSession(){this._debug("#_removeSession()"),await Ls(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const t=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{t&&Me()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",t)}catch(n){console.error("removing visibilitychange callback failed",n)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const t=setInterval(()=>this._autoRefreshTokenTick(),wn);this.autoRefreshTicker=t,t&&typeof t=="object"&&typeof t.unref=="function"?t.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(t),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const t=this.autoRefreshTicker;this.autoRefreshTicker=null,t&&clearInterval(t)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async n=>{const{data:{session:r}}=n;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const s=Math.floor((r.expires_at*1e3-t)/wn);this._debug("#_autoRefreshTokenTick()",`access token expires in ${s} ticks, a tick lasts ${wn}ms, refresh threshold is ${$a} ticks`),s<=$a&&await this._callRefreshToken(r.refresh_token)})}catch(n){console.error("Auto refresh tick failed with error. This is likely a transient error.",n)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(t){if(t.isAcquireTimeout||t instanceof bf)this._debug("auto refresh token tick lock not available");else throw t}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!Me()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(t){console.error("_handleVisibilityChange",t)}}async _onVisibilityChanged(t){const n=`#_onVisibilityChanged(${t})`;this._debug(n,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),t||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(n,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(t,n,r){const s=[`provider=${encodeURIComponent(n)}`];if(r!=null&&r.redirectTo&&s.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),r!=null&&r.scopes&&s.push(`scopes=${encodeURIComponent(r.scopes)}`),this.flowType==="pkce"){const[i,o]=await vn(this.storage,this.storageKey),l=new URLSearchParams({code_challenge:`${encodeURIComponent(i)}`,code_challenge_method:`${encodeURIComponent(o)}`});s.push(l.toString())}if(r!=null&&r.queryParams){const i=new URLSearchParams(r.queryParams);s.push(i.toString())}return r!=null&&r.skipBrowserRedirect&&s.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${t}?${s.join("&")}`}async _unenroll(t){try{return await this._useSession(async n=>{var r;const{data:s,error:i}=n;return i?{data:null,error:i}:await $(this.fetch,"DELETE",`${this.url}/factors/${t.factorId}`,{headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(R(n))return{data:null,error:n};throw n}}async _enroll(t){try{return await this._useSession(async n=>{var r,s;const{data:i,error:o}=n;if(o)return{data:null,error:o};const l=Object.assign({friendly_name:t.friendlyName,factor_type:t.factorType},t.factorType==="phone"?{phone:t.phone}:{issuer:t.issuer}),{data:u,error:c}=await $(this.fetch,"POST",`${this.url}/factors`,{body:l,headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token});return c?{data:null,error:c}:(t.factorType==="totp"&&(!((s=u==null?void 0:u.totp)===null||s===void 0)&&s.qr_code)&&(u.totp.qr_code=`data:image/svg+xml;utf-8,${u.totp.qr_code}`),{data:u,error:null})})}catch(n){if(R(n))return{data:null,error:n};throw n}}async _verify(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:s,error:i}=n;if(i)return{data:null,error:i};const{data:o,error:l}=await $(this.fetch,"POST",`${this.url}/factors/${t.factorId}/verify`,{body:{code:t.code,challenge_id:t.challengeId},headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token});return l?{data:null,error:l}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:l})})}catch(n){if(R(n))return{data:null,error:n};throw n}})}async _challenge(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:s,error:i}=n;return i?{data:null,error:i}:await $(this.fetch,"POST",`${this.url}/factors/${t.factorId}/challenge`,{body:{channel:t.channel},headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(R(n))return{data:null,error:n};throw n}})}async _challengeAndVerify(t){const{data:n,error:r}=await this._challenge({factorId:t.factorId});return r?{data:null,error:r}:await this._verify({factorId:t.factorId,challengeId:n.id,code:t.code})}async _listFactors(){const{data:{user:t},error:n}=await this.getUser();if(n)return{data:null,error:n};const r=(t==null?void 0:t.factors)||[],s=r.filter(o=>o.factor_type==="totp"&&o.status==="verified"),i=r.filter(o=>o.factor_type==="phone"&&o.status==="verified");return{data:{all:r,totp:s,phone:i},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async t=>{var n,r;const{data:{session:s},error:i}=t;if(i)return{data:null,error:i};if(!s)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=Ro(s.access_token);let l=null;o.aal&&(l=o.aal);let u=l;((r=(n=s.user.factors)===null||n===void 0?void 0:n.filter(h=>h.status==="verified"))!==null&&r!==void 0?r:[]).length>0&&(u="aal2");const d=o.amr||[];return{data:{currentLevel:l,nextLevel:u,currentAuthenticationMethods:d},error:null}}))}async fetchJwk(t,n={keys:[]}){let r=n.keys.find(o=>o.kid===t);if(r||(r=this.jwks.keys.find(o=>o.kid===t),r&&this.jwks_cached_at+h0>Date.now()))return r;const{data:s,error:i}=await $(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(i)throw i;if(!s.keys||s.keys.length===0)throw new Rr("JWKS is empty");if(this.jwks=s,this.jwks_cached_at=Date.now(),r=s.keys.find(o=>o.kid===t),!r)throw new Rr("No matching signing key found in JWKS");return r}async getClaims(t,n={keys:[]}){try{let r=t;if(!r){const{data:g,error:w}=await this.getSession();if(w||!g.session)return{data:null,error:w};r=g.session.access_token}const{header:s,payload:i,signature:o,raw:{header:l,payload:u}}=Ro(r);if(I0(i.exp),!s.kid||s.alg==="HS256"||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){const{error:g}=await this.getUser(r);if(g)throw g;return{data:{claims:i,header:s,signature:o},error:null}}const c=z0(s.alg),d=await this.fetchJwk(s.kid,n),h=await crypto.subtle.importKey("jwk",d,c,!0,["verify"]);if(!await crypto.subtle.verify(c,h,o,k0(`${l}.${u}`)))throw new Rr("Invalid JWT signature");return{data:{claims:i,header:s,signature:o},error:null}}catch(r){if(R(r))return{data:null,error:r};throw r}}}Yr.nextInstanceID=0;const ew=Yr;class tw extends ew{constructor(t){super(t)}}var nw=globalThis&&globalThis.__awaiter||function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function l(d){try{c(r.next(d))}catch(h){o(h)}}function u(d){try{c(r.throw(d))}catch(h){o(h)}}function c(d){d.done?i(d.value):s(d.value).then(l,u)}c((r=r.apply(e,t||[])).next())})};class rw{constructor(t,n,r){var s,i,o;if(this.supabaseUrl=t,this.supabaseKey=n,!t)throw new Error("supabaseUrl is required.");if(!n)throw new Error("supabaseKey is required.");const l=o0(t),u=new URL(l);this.realtimeUrl=new URL("realtime/v1",u),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",u),this.storageUrl=new URL("storage/v1",u),this.functionsUrl=new URL("functions/v1",u);const c=`sb-${u.hostname.split(".")[0]}-auth-token`,d={db:Xy,realtime:e0,auth:Object.assign(Object.assign({},Zy),{storageKey:c}),global:Yy},h=a0(r??{},d);this.storageKey=(s=h.auth.storageKey)!==null&&s!==void 0?s:"",this.headers=(i=h.global.headers)!==null&&i!==void 0?i:{},h.accessToken?(this.accessToken=h.accessToken,this.auth=new Proxy({},{get:(p,g)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(g)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((o=h.auth)!==null&&o!==void 0?o:{},this.headers,h.global.fetch),this.fetch=s0(n,this._getAccessToken.bind(this),h.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},h.realtime)),this.rest=new xy(new URL("rest/v1",u).href,{headers:this.headers,schema:h.db.schema,fetch:this.fetch}),h.accessToken||this._listenForAuthEvents()}get functions(){return new Jg(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new Gy(this.storageUrl.href,this.headers,this.fetch)}from(t){return this.rest.from(t)}schema(t){return this.rest.schema(t)}rpc(t,n={},r={}){return this.rest.rpc(t,n,r)}channel(t,n={config:{}}){return this.realtime.channel(t,n)}getChannels(){return this.realtime.getChannels()}removeChannel(t){return this.realtime.removeChannel(t)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var t,n;return nw(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return(n=(t=r.session)===null||t===void 0?void 0:t.access_token)!==null&&n!==void 0?n:null})}_initSupabaseAuthClient({autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:s,storageKey:i,flowType:o,lock:l,debug:u},c,d){const h={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new tw({url:this.authUrl.href,headers:Object.assign(Object.assign({},h),c),storageKey:i,autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:s,flowType:o,lock:l,debug:u,fetch:d,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(t){return new Ay(this.realtimeUrl.href,Object.assign(Object.assign({},t),{params:Object.assign({apikey:this.supabaseKey},t==null?void 0:t.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((n,r)=>{this._handleTokenChanged(n,"CLIENT",r==null?void 0:r.access_token)})}_handleTokenChanged(t,n,r){(t==="TOKEN_REFRESHED"||t==="SIGNED_IN")&&this.changedAccessToken!==r?this.changedAccessToken=r:t==="SIGNED_OUT"&&(this.realtime.setAuth(),n=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const sw=(e,t,n)=>new rw(e,t,n),iw="https://xktprpguxerqyqtjgfyw.supabase.co",ow="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Kx8zQQGWJHVL-_Qs8vGzJxEqKQGWJHVL_Qs8vGzJxEqK",Re=sw(iw,ow,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}}),Rf=x.createContext({}),Wt=()=>{const e=x.useContext(Rf);if(!e)throw new Error("useAuth must be used within an AuthProvider");return e},aw=({children:e})=>{const[t,n]=x.useState(null),[r,s]=x.useState(null),[i,o]=x.useState(!0);x.useEffect(()=>{(async()=>{const{data:{session:m}}=await Re.auth.getSession();n((m==null?void 0:m.user)??null),m!=null&&m.user&&await l(m.user.id),o(!1)})();const{data:{subscription:k}}=Re.auth.onAuthStateChange(async(m,f)=>{n((f==null?void 0:f.user)??null),f!=null&&f.user?await l(f.user.id):s(null),o(!1)});return()=>k.unsubscribe()},[]);const l=async y=>{try{const{data:k,error:m}=await Re.from("user_profiles").select("*").eq("id",y).single();if(m&&m.code!=="PGRST116"){console.error("Error fetching user profile:",m);return}s(k)}catch(k){console.error("Error fetching user profile:",k)}},w={user:t,userProfile:r,loading:i,signInWithGoogle:async()=>{try{const{data:y,error:k}=await Re.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});if(k)throw k;return{data:y,error:null}}catch(y){return console.error("Error signing in with Google:",y),{data:null,error:y}}},signInWithEmail:async(y,k)=>{try{const{data:m,error:f}=await Re.auth.signInWithPassword({email:y,password:k});if(f)throw f;return{data:m,error:null}}catch(m){return console.error("Error signing in with email:",m),{data:null,error:m}}},signUpWithEmail:async(y,k,m={})=>{try{const{data:f,error:v}=await Re.auth.signUp({email:y,password:k,options:{data:m}});if(v)throw v;return{data:f,error:null}}catch(f){return console.error("Error signing up with email:",f),{data:null,error:f}}},createUserProfile:async(y,k)=>{try{const{data:m,error:f}=await Re.from("user_profiles").insert([{id:y,...k,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}]).select().single();if(f)throw f;return s(m),{data:m,error:null}}catch(m){return console.error("Error creating user profile:",m),{data:null,error:m}}},updateUserProfile:async y=>{try{const{data:k,error:m}=await Re.from("user_profiles").update({...y,updated_at:new Date().toISOString()}).eq("id",t.id).select().single();if(m)throw m;return s(k),{data:k,error:null}}catch(k){return console.error("Error updating user profile:",k),{data:null,error:k}}},signOut:async()=>{try{const{error:y}=await Re.auth.signOut();if(y)throw y;return n(null),s(null),{error:null}}catch(y){return console.error("Error signing out:",y),{error:y}}},fetchUserProfile:l};return a.jsx(Rf.Provider,{value:w,children:e})};var lw={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const uw=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),cw=(e,t)=>{const n=x.forwardRef(({color:r="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:o,children:l,...u},c)=>x.createElement("svg",{ref:c,...lw,width:s,height:s,stroke:r,strokeWidth:o?Number(i)*24/Number(s):i,className:`lucide lucide-${uw(e)}`,...u},[...t.map(([d,h])=>x.createElement(d,h)),...(Array.isArray(l)?l:[l])||[]]));return n.displayName=`${e}`,n};var D=cw;const dw=D("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]]),Rc=D("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),Gl=D("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),za=D("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]),Xr=D("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),hw=D("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),Of=D("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),fw=D("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),Da=D("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),Lf=D("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]),Ki=D("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),Mt=D("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),pw=D("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),mw=D("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]),Ua=D("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]),Ql=D("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),vw=D("Quote",[["path",{d:"M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z",key:"4rm80e"}],["path",{d:"M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z",key:"10za9r"}]]),Ma=D("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),gw=D("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]),$f=D("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),yw=D("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]),We=D("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]),ww=D("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]]),xw=D("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),_i=D("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),_w=D("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),Af=D("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),Gi=D("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),Oc=D("Video",[["path",{d:"m22 8-6 4 6 4V8Z",key:"50v9me"}],["rect",{width:"14",height:"12",x:"2",y:"6",rx:"2",ry:"2",key:"1rqjg6"}]]),kw=D("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),jw=D("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);const Sw=()=>{const[e,t]=Vc.useState(!1),{user:n,userProfile:r,signOut:s}=Wt(),i=hn(),o=async()=>{await s(),i("/")},l=()=>r!=null&&r.role?r.role==="vendor"?"/vendor/dashboard":"/vlogger/dashboard":"/select-role";return a.jsx("header",{className:"header",children:a.jsx("div",{className:"container",children:a.jsxs("div",{className:"header-content",children:[a.jsx("div",{className:"logo",children:a.jsx(et,{to:"/",children:a.jsx("h1",{children:"Bhookad"})})}),a.jsxs("nav",{className:`nav ${e?"nav-open":""}`,children:[a.jsx("a",{href:"#home",className:"nav-link",children:"Home"}),a.jsx("a",{href:"#how-it-works",className:"nav-link",children:"How it Works"}),a.jsx("a",{href:"#vendors",className:"nav-link",children:"Vendors"}),a.jsx("a",{href:"#about",className:"nav-link",children:"About"}),a.jsx("a",{href:"#contact",className:"nav-link",children:"Contact"})]}),a.jsx("div",{className:"header-actions",children:n?a.jsxs("div",{className:"user-menu",children:[a.jsxs(et,{to:l(),className:"user-profile",children:[a.jsx(Af,{size:20}),a.jsx("span",{children:(r==null?void 0:r.full_name)||"Dashboard"})]}),a.jsx("button",{className:"btn-secondary",onClick:o,children:"Sign Out"})]}):a.jsxs(a.Fragment,{children:[a.jsx(et,{to:"/login",className:"btn-secondary",children:"Login"}),a.jsx(et,{to:"/register",className:"btn-primary",children:"Sign Up"})]})}),a.jsx("button",{className:"mobile-menu-btn",onClick:()=>t(!e),children:e?a.jsx(kw,{size:24}):a.jsx(pw,{size:24})})]})})})};const Ew=()=>a.jsx("section",{className:"hero",id:"home",children:a.jsxs("div",{className:"container",children:[a.jsxs("div",{className:"hero-content",children:[a.jsxs("div",{className:"hero-text",children:[a.jsxs("h1",{className:"hero-title",children:["Connecting you",a.jsx("br",{}),"to ",a.jsx("span",{className:"highlight",children:"trending plates!"})]}),a.jsx("p",{className:"hero-description",children:"Discover the best street food vendors in your area. From spicy chaat to delicious momos, find authentic flavors that will make your taste buds dance!"}),a.jsxs("div",{className:"hero-buttons",children:[a.jsx("button",{className:"btn-primary",children:"Explore Now"}),a.jsx("button",{className:"btn-secondary",children:"Learn More"})]})]}),a.jsx("div",{className:"hero-image",children:a.jsxs("div",{className:"food-card",children:[a.jsx("div",{className:"food-image-placeholder",children:"🍛"}),a.jsx("h3",{children:"Trending Biryani"}),a.jsxs("div",{className:"rating",children:[a.jsx(We,{className:"star-filled",size:16}),a.jsx(We,{className:"star-filled",size:16}),a.jsx(We,{className:"star-filled",size:16}),a.jsx(We,{className:"star-filled",size:16}),a.jsx(We,{className:"star-filled",size:16}),a.jsx("span",{children:"4.8"})]})]})})]}),a.jsxs("div",{className:"stats",children:[a.jsxs("div",{className:"stat-item",children:[a.jsx("div",{className:"stat-icon",children:a.jsx(Gi,{size:24})}),a.jsx("div",{className:"stat-number",children:"200+"}),a.jsx("div",{className:"stat-label",children:"Happy Customers"})]}),a.jsxs("div",{className:"stat-item",children:[a.jsx("div",{className:"stat-icon",children:a.jsx(Mt,{size:24})}),a.jsx("div",{className:"stat-number",children:"100+"}),a.jsx("div",{className:"stat-label",children:"Vendors"})]}),a.jsxs("div",{className:"stat-item",children:[a.jsx("div",{className:"stat-icon",children:a.jsx(We,{size:24})}),a.jsx("div",{className:"stat-number",children:"250+"}),a.jsx("div",{className:"stat-label",children:"Reviews"})]}),a.jsxs("div",{className:"stat-item",children:[a.jsx("div",{className:"stat-icon",children:a.jsx(_i,{size:24})}),a.jsx("div",{className:"stat-number",children:"500+"}),a.jsx("div",{className:"stat-label",children:"Orders"})]})]})]})});const Nw=()=>{const e=[{icon:a.jsx(Ma,{size:40}),title:"Discover",description:"Browse through trending street food vendors in your area"},{icon:a.jsx(Mt,{size:40}),title:"Locate",description:"Find the exact location of your favorite food vendors"},{icon:a.jsx(yw,{size:40}),title:"Enjoy",description:"Visit and enjoy authentic street food experiences"}];return a.jsx("section",{className:"how-it-works section",id:"how-it-works",children:a.jsxs("div",{className:"container",children:[a.jsx("h2",{className:"section-title",children:"How Bhookad Works"}),a.jsx("div",{className:"steps-grid",children:e.map((t,n)=>a.jsxs("div",{className:"step-card",children:[a.jsx("div",{className:"step-number",children:n+1}),a.jsx("div",{className:"step-icon",children:t.icon}),a.jsx("h3",{className:"step-title",children:t.title}),a.jsx("p",{className:"step-description",children:t.description})]},n))})]})})},Cw="http://localhost:3001/api",fr=async(e,t={})=>{try{const n=`${Cw}${e}`,r={headers:{"Content-Type":"application/json",...t.headers},...t},s=await fetch(n,r),i=await s.json();if(!s.ok)throw new Error(i.message||"API call failed");return i}catch(n){throw console.error("API Error:",n),n}},Zr={testConnection:()=>fr("/test"),getVendors:(e={})=>{const t=new URLSearchParams(e).toString();return fr(`/vendors${t?`?${t}`:""}`)},getVendorById:e=>fr(`/vendors/${e}`),submitContact:e=>fr("/contact",{method:"POST",body:JSON.stringify(e)}),healthCheck:()=>fr("/health",{baseURL:"http://localhost:5000"})},Pw={getAll:Zr.getVendors,getById:Zr.getVendorById},Tw={submit:Zr.submitContact};const bw=()=>{const[e,t]=x.useState([]),[n,r]=x.useState(!0),[s,i]=x.useState(null);return x.useEffect(()=>{(async()=>{try{r(!0);const l=await Pw.getAll({limit:6});l.success?t(l.vendors):i("Failed to load vendors")}catch(l){console.error("Error fetching vendors:",l),i("Failed to connect to server"),t([{id:1,name:"Sharma Ji Ka Dhaba",cuisine_type:"North Indian",rating:4.8,address:"Connaught Place",specialties:["Butter Chicken","Naan"]},{id:2,name:"Mumbai Chaat Corner",cuisine_type:"Street Food",rating:4.6,address:"Khan Market",specialties:["Pani Puri","Bhel Puri"]},{id:3,name:"Delhi Paratha Wala",cuisine_type:"North Indian",rating:4.7,address:"Chandni Chowk",specialties:["Aloo Paratha","Lassi"]},{id:4,name:"South Indian Express",cuisine_type:"South Indian",rating:4.5,address:"CP Metro Station",specialties:["Dosa","Idli","Sambhar"]},{id:5,name:"Rajasthani Thali House",cuisine_type:"Rajasthani",rating:4.9,address:"Karol Bagh",specialties:["Dal Baati","Gatte Ki Sabzi"]},{id:6,name:"Bengali Sweet Corner",cuisine_type:"Bengali",rating:4.4,address:"CR Park",specialties:["Rasgulla","Sandesh"]}])}finally{r(!1)}})()},[]),n?a.jsx("section",{className:"trending-vendors section",id:"vendors",children:a.jsxs("div",{className:"container",children:[a.jsx("h2",{className:"section-title",children:"Trending Street Food Vendors"}),a.jsx("div",{className:"loading",children:"Loading vendors..."})]})}):s?a.jsx("section",{className:"trending-vendors section",id:"vendors",children:a.jsxs("div",{className:"container",children:[a.jsx("h2",{className:"section-title",children:"Trending Street Food Vendors"}),a.jsxs("div",{className:"error",children:["Error: ",s]})]})}):a.jsx("section",{className:"trending-vendors section",id:"vendors",children:a.jsxs("div",{className:"container",children:[a.jsx("h2",{className:"section-title",children:"Trending Street Food Vendors"}),a.jsx("div",{className:"vendors-grid",children:e.map((o,l)=>a.jsxs("div",{className:"vendor-card",children:[a.jsx("div",{className:"vendor-image",children:a.jsx("span",{className:"food-emoji",children:"🍛"})}),a.jsxs("div",{className:"vendor-info",children:[a.jsx("h3",{className:"vendor-name",children:o.name}),a.jsx("p",{className:"vendor-cuisine",children:o.cuisine_type}),a.jsx("p",{className:"vendor-speciality",children:o.specialties?o.specialties.join(", "):"Delicious food"}),a.jsxs("div",{className:"vendor-details",children:[a.jsxs("div",{className:"rating",children:[a.jsx(We,{className:"star-filled",size:16}),a.jsx("span",{children:o.rating})]}),a.jsxs("div",{className:"location",children:[a.jsx(Mt,{size:16}),a.jsx("span",{children:o.address||o.city})]}),a.jsxs("div",{className:"timing",children:[a.jsx(Gl,{size:16}),a.jsx("span",{children:"10 AM - 10 PM"})]})]}),a.jsx("button",{className:"btn-primary vendor-btn",children:"Visit Now"})]})]},o.id||l))})]})})};const Rw=()=>{const e=[{name:"Priya Sharma",location:"Delhi",rating:5,story:"Bhookad helped me discover the most amazing street food vendors in my area. The authentic flavors and quality recommendations are outstanding!",avatar:"👩"},{name:"Rahul Kumar",location:"Mumbai",rating:5,story:"As a food blogger, Bhookad has been my go-to platform for finding trending street food spots. Highly recommended for all food lovers!",avatar:"👨"},{name:"Anjali Patel",location:"Bangalore",rating:4,story:"Great platform to explore local street food culture. Found some hidden gems through Bhookad that I would never have discovered otherwise.",avatar:"👩‍💼"}];return a.jsx("section",{className:"success-stories section",children:a.jsxs("div",{className:"container",children:[a.jsx("h2",{className:"section-title",children:"Success Stories"}),a.jsx("div",{className:"stories-grid",children:e.map((t,n)=>a.jsxs("div",{className:"story-card",children:[a.jsx(vw,{className:"quote-icon",size:24}),a.jsxs("p",{className:"story-text",children:['"',t.story,'"']}),a.jsx("div",{className:"story-rating",children:[...Array(5)].map((r,s)=>a.jsx(We,{size:16,className:s<t.rating?"star-filled":"star-empty"},s))}),a.jsxs("div",{className:"story-author",children:[a.jsx("div",{className:"author-avatar",children:t.avatar}),a.jsxs("div",{className:"author-info",children:[a.jsx("h4",{className:"author-name",children:t.name}),a.jsx("p",{className:"author-location",children:t.location})]})]})]},n))})]})})};const Ow=()=>{const e=[{icon:a.jsx(Of,{size:32}),title:"Passion for Food",description:"Hum authentic street food culture ko promote karte hain aur local vendors ko support karte hain."},{icon:a.jsx(Gi,{size:32}),title:"Community First",description:"Food lovers aur vendors ke beech ek strong community banane mein believe karte hain."},{icon:a.jsx(dw,{size:32}),title:"Quality Assurance",description:"Sirf best quality aur hygienic food vendors ko hi recommend karte hain."},{icon:a.jsx(jw,{size:32}),title:"Innovation",description:"Technology ka use karke street food discovery ko easy aur enjoyable banate hain."}];return a.jsx("section",{className:"about section",id:"about",children:a.jsxs("div",{className:"container",children:[a.jsx("h2",{className:"section-title",children:"About Bhookad"}),a.jsxs("div",{className:"about-intro",children:[a.jsxs("div",{className:"about-text",children:[a.jsx("h3",{children:"Hamara Mission"}),a.jsx("p",{children:"Bhookad ek revolutionary platform hai jo food lovers ko unke area ke best street food vendors se connect karta hai. Hum believe karte hain ki authentic street food sirf khana nahi, balki ek cultural experience hai."}),a.jsx("p",{children:"Hamara goal hai ki har food lover ko trending aur quality street food easily mil sake, aur local vendors ko zyada customers aur recognition mile."})]}),a.jsxs("div",{className:"mission-vision",children:[a.jsxs("div",{className:"mission-card",children:[a.jsx(xw,{className:"card-icon",size:40}),a.jsx("h4",{children:"Our Mission"}),a.jsx("p",{children:"Street food culture ko digitally transform karna aur local food ecosystem ko strengthen karna."})]}),a.jsxs("div",{className:"vision-card",children:[a.jsx(Xr,{className:"card-icon",size:40}),a.jsx("h4",{children:"Our Vision"}),a.jsx("p",{children:"India ka #1 street food discovery platform banna aur har city mein food lovers ko connect karna."})]})]})]}),a.jsxs("div",{className:"our-values",children:[a.jsx("h3",{className:"values-title",children:"Our Values"}),a.jsx("div",{className:"values-grid",children:e.map((t,n)=>a.jsxs("div",{className:"value-card",children:[a.jsx("div",{className:"value-icon",children:t.icon}),a.jsx("h4",{className:"value-title",children:t.title}),a.jsx("p",{className:"value-description",children:t.description})]},n))})]}),a.jsxs("div",{className:"about-stats",children:[a.jsxs("div",{className:"stat-box",children:[a.jsx("h4",{children:"200+"}),a.jsx("p",{children:"Happy Customers"})]}),a.jsxs("div",{className:"stat-box",children:[a.jsx("h4",{children:"100+"}),a.jsx("p",{children:"Partner Vendors"})]}),a.jsxs("div",{className:"stat-box",children:[a.jsx("h4",{children:"15+"}),a.jsx("p",{children:"Cities Covered"})]}),a.jsxs("div",{className:"stat-box",children:[a.jsx("h4",{children:"500+"}),a.jsx("p",{children:"Food Items"})]})]})]})})};const Lw=()=>{const[e,t]=x.useState({name:"",email:"",phone:"",subject:"",message:""}),n=i=>{t({...e,[i.target.name]:i.target.value})},r=async i=>{i.preventDefault();try{const o=await Tw.submit(e);o.success&&(alert(`Thank you ${e.name}! ${o.message}`),t({name:"",email:"",phone:"",subject:"",message:""}))}catch(o){console.error("Contact form error:",o),alert("Sorry, there was an error submitting your message. Please try again.")}},s=[{icon:a.jsx(Mt,{size:24}),title:"Address",details:["Connaught Place","New Delhi, India","110001"]},{icon:a.jsx(Ql,{size:24}),title:"Phone",details:["+91 98765 43210","+91 87654 32109"]},{icon:a.jsx(Ki,{size:24}),title:"Email",details:["<EMAIL>","<EMAIL>"]},{icon:a.jsx(Gl,{size:24}),title:"Working Hours",details:["Mon - Fri: 9:00 AM - 8:00 PM","Sat - Sun: 10:00 AM - 6:00 PM"]}];return a.jsx("section",{className:"contact section",id:"contact",children:a.jsxs("div",{className:"container",children:[a.jsx("h2",{className:"section-title",children:"Contact Us"}),a.jsx("p",{className:"contact-subtitle",children:"Koi sawal hai ya suggestion? Hum sunne ke liye ready hain!"}),a.jsxs("div",{className:"contact-content",children:[a.jsxs("div",{className:"contact-info",children:[a.jsx("h3",{children:"Get in Touch"}),a.jsx("p",{children:"Bhookad team se connect karne ke liye neeche diye gaye details use kar sakte hain. Hum 24 hours mein reply karne ki koshish karte hain."}),a.jsx("div",{className:"contact-cards",children:s.map((i,o)=>a.jsxs("div",{className:"contact-card",children:[a.jsx("div",{className:"contact-icon",children:i.icon}),a.jsxs("div",{className:"contact-details",children:[a.jsx("h4",{children:i.title}),i.details.map((l,u)=>a.jsx("p",{children:l},u))]})]},o))})]}),a.jsxs("div",{className:"contact-form-section",children:[a.jsx("h3",{children:"Send us a Message"}),a.jsxs("form",{className:"contact-form",onSubmit:r,children:[a.jsxs("div",{className:"form-row",children:[a.jsxs("div",{className:"form-group",children:[a.jsx("label",{htmlFor:"name",children:"Name *"}),a.jsx("input",{type:"text",id:"name",name:"name",value:e.name,onChange:n,required:!0,placeholder:"Aapka naam"})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{htmlFor:"email",children:"Email *"}),a.jsx("input",{type:"email",id:"email",name:"email",value:e.email,onChange:n,required:!0,placeholder:"<EMAIL>"})]})]}),a.jsxs("div",{className:"form-row",children:[a.jsxs("div",{className:"form-group",children:[a.jsx("label",{htmlFor:"phone",children:"Phone"}),a.jsx("input",{type:"tel",id:"phone",name:"phone",value:e.phone,onChange:n,placeholder:"+91 98765 43210"})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{htmlFor:"subject",children:"Subject *"}),a.jsxs("select",{id:"subject",name:"subject",value:e.subject,onChange:n,required:!0,children:[a.jsx("option",{value:"",children:"Select Subject"}),a.jsx("option",{value:"general",children:"General Inquiry"}),a.jsx("option",{value:"vendor",children:"Vendor Partnership"}),a.jsx("option",{value:"support",children:"Technical Support"}),a.jsx("option",{value:"feedback",children:"Feedback"}),a.jsx("option",{value:"other",children:"Other"})]})]})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{htmlFor:"message",children:"Message *"}),a.jsx("textarea",{id:"message",name:"message",value:e.message,onChange:n,required:!0,rows:"5",placeholder:"Apna message yahan likhiye..."})]}),a.jsxs("button",{type:"submit",className:"btn-primary submit-btn",children:[a.jsx(gw,{size:20}),"Send Message"]})]})]})]}),a.jsxs("div",{className:"contact-cta",children:[a.jsx(mw,{size:40}),a.jsx("h3",{children:"Quick Response Guarantee"}),a.jsx("p",{children:"Hum 24 hours ke andar reply karne ki guarantee dete hain!"})]})]})})};const $w=()=>a.jsx("footer",{className:"footer",children:a.jsxs("div",{className:"container",children:[a.jsxs("div",{className:"footer-content",children:[a.jsxs("div",{className:"footer-section",children:[a.jsx("h3",{className:"footer-title",children:"Bhookad"}),a.jsx("p",{className:"footer-description",children:"Connecting food lovers to the best street food vendors in their area. Discover authentic flavors and trending plates near you."}),a.jsxs("div",{className:"social-links",children:[a.jsx("a",{href:"#",className:"social-link",children:a.jsx(hw,{size:20})}),a.jsx("a",{href:"#",className:"social-link",children:a.jsx(_w,{size:20})}),a.jsx("a",{href:"#",className:"social-link",children:a.jsx(fw,{size:20})})]})]}),a.jsxs("div",{className:"footer-section",children:[a.jsx("h4",{className:"footer-subtitle",children:"Quick Links"}),a.jsxs("ul",{className:"footer-links",children:[a.jsx("li",{children:a.jsx("a",{href:"#home",children:"Home"})}),a.jsx("li",{children:a.jsx("a",{href:"#how-it-works",children:"How it Works"})}),a.jsx("li",{children:a.jsx("a",{href:"#vendors",children:"Vendors"})}),a.jsx("li",{children:a.jsx("a",{href:"#about",children:"About Us"})}),a.jsx("li",{children:a.jsx("a",{href:"#contact",children:"Contact"})})]})]}),a.jsxs("div",{className:"footer-section",children:[a.jsx("h4",{className:"footer-subtitle",children:"Categories"}),a.jsxs("ul",{className:"footer-links",children:[a.jsx("li",{children:a.jsx("a",{href:"#",children:"Street Food"})}),a.jsx("li",{children:a.jsx("a",{href:"#",children:"North Indian"})}),a.jsx("li",{children:a.jsx("a",{href:"#",children:"South Indian"})}),a.jsx("li",{children:a.jsx("a",{href:"#",children:"Chinese"})}),a.jsx("li",{children:a.jsx("a",{href:"#",children:"Desserts"})})]})]}),a.jsxs("div",{className:"footer-section",children:[a.jsx("h4",{className:"footer-subtitle",children:"Contact Info"}),a.jsxs("div",{className:"contact-info",children:[a.jsxs("div",{className:"contact-item",children:[a.jsx(Mt,{size:16}),a.jsx("span",{children:"Delhi, India"})]}),a.jsxs("div",{className:"contact-item",children:[a.jsx(Ql,{size:16}),a.jsx("span",{children:"+91 98765 43210"})]}),a.jsxs("div",{className:"contact-item",children:[a.jsx(Ki,{size:16}),a.jsx("span",{children:"<EMAIL>"})]})]})]})]}),a.jsxs("div",{className:"footer-bottom",children:[a.jsx("p",{children:"© 2024 Bhookad. All rights reserved."}),a.jsxs("div",{className:"footer-bottom-links",children:[a.jsx("a",{href:"#",children:"Privacy Policy"}),a.jsx("a",{href:"#",children:"Terms of Service"})]})]})]})}),Aw=()=>{const[e,t]=x.useState({connection:null,vendors:null,health:null}),[n,r]=x.useState(!1),s=async()=>{r(!0);const i={};try{console.log("Testing API connection...");const o=await Zr.testConnection();i.connection=o,console.log("Connection test result:",o)}catch(o){i.connection={error:o.message},console.error("Connection test failed:",o)}try{console.log("Testing vendors endpoint...");const o=await Zr.getVendors();i.vendors=o,console.log("Vendors test result:",o)}catch(o){i.vendors={error:o.message},console.error("Vendors test failed:",o)}t(i),r(!1)};return x.useEffect(()=>{s()},[]),a.jsxs("div",{style:{position:"fixed",top:"10px",right:"10px",background:"white",padding:"20px",border:"1px solid #ccc",borderRadius:"8px",maxWidth:"300px",fontSize:"12px",zIndex:1e3},children:[a.jsx("h4",{children:"API Test Results"}),a.jsxs("div",{children:[a.jsx("strong",{children:"Connection Test:"}),n?a.jsx("span",{children:" Loading..."}):e.connection?e.connection.error?a.jsxs("span",{style:{color:"red"},children:[" ❌ ",e.connection.error]}):a.jsxs("span",{style:{color:"green"},children:[" ✅ ",e.connection.message]}):a.jsx("span",{children:" Not tested"})]}),a.jsxs("div",{children:[a.jsx("strong",{children:"Vendors Test:"}),n?a.jsx("span",{children:" Loading..."}):e.vendors?e.vendors.error?a.jsxs("span",{style:{color:"red"},children:[" ❌ ",e.vendors.error]}):a.jsxs("span",{style:{color:"green"},children:[" ✅ ",e.vendors.count," vendors loaded"]}):a.jsx("span",{children:" Not tested"})]}),a.jsx("button",{onClick:s,style:{marginTop:"10px",padding:"5px 10px",background:"#007bff",color:"white",border:"none",borderRadius:"4px",cursor:"pointer"},children:"Retest API"})]})};const Iw=()=>{const[e,t]=x.useState(""),[n,r]=x.useState(""),[s,i]=x.useState(!1),[o,l]=x.useState(!1),[u,c]=x.useState(""),{signInWithEmail:d,signInWithGoogle:h}=Wt();hn();const p=async w=>{if(w.preventDefault(),!e||!n){c("Please fill in all fields");return}l(!0),c("");try{const{data:y,error:k}=await d(e,n);k?c(k.message):console.log("Login successful")}catch{c("An unexpected error occurred")}finally{l(!1)}},g=async()=>{l(!0),c("");try{const{data:w,error:y}=await h();y&&c(y.message)}catch{c("Failed to sign in with Google")}finally{l(!1)}};return a.jsx("div",{className:"auth-container",children:a.jsxs("div",{className:"auth-card",children:[a.jsxs("div",{className:"auth-header",children:[a.jsx("h1",{children:"Welcome Back to Bhookad! 🍛"}),a.jsx("p",{children:"Sign in to discover amazing street food"})]}),u&&a.jsx("div",{className:"error-message",children:u}),a.jsxs("form",{onSubmit:p,className:"auth-form",children:[a.jsxs("div",{className:"form-group",children:[a.jsx("label",{htmlFor:"email",children:"Email Address"}),a.jsxs("div",{className:"input-with-icon",children:[a.jsx(Ki,{size:20}),a.jsx("input",{type:"email",id:"email",value:e,onChange:w=>t(w.target.value),placeholder:"Enter your email",required:!0})]})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{htmlFor:"password",children:"Password"}),a.jsxs("div",{className:"input-with-icon",children:[a.jsx(Da,{size:20}),a.jsx("input",{type:s?"text":"password",id:"password",value:n,onChange:w=>r(w.target.value),placeholder:"Enter your password",required:!0}),a.jsx("button",{type:"button",className:"password-toggle",onClick:()=>i(!s),children:s?a.jsx(za,{size:20}):a.jsx(Xr,{size:20})})]})]}),a.jsx("button",{type:"submit",className:"btn-primary auth-btn",disabled:o,children:o?"Signing In...":"Sign In"})]}),a.jsx("div",{className:"auth-divider",children:a.jsx("span",{children:"or"})}),a.jsxs("button",{onClick:g,className:"btn-google",disabled:o,children:[a.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",children:[a.jsx("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),a.jsx("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),a.jsx("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),a.jsx("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Continue with Google"]}),a.jsxs("div",{className:"auth-footer",children:[a.jsxs("p",{children:["Don't have an account?"," ",a.jsx(et,{to:"/register",className:"auth-link",children:"Sign up here"})]}),a.jsx(et,{to:"/forgot-password",className:"auth-link",children:"Forgot your password?"})]})]})})},zw=()=>{const[e,t]=x.useState({email:"",password:"",confirmPassword:"",fullName:"",phone:"",role:""}),[n,r]=x.useState(!1),[s,i]=x.useState(!1),[o,l]=x.useState(!1),[u,c]=x.useState(""),[d,h]=x.useState(1),{signUpWithEmail:p,signInWithGoogle:g,createUserProfile:w}=Wt();hn();const y=v=>{t({...e,[v.target.name]:v.target.value})},k=v=>{t({...e,role:v}),h(2)},m=async v=>{if(v.preventDefault(),!e.email||!e.password||!e.fullName||!e.role){c("Please fill in all required fields");return}if(e.password!==e.confirmPassword){c("Passwords do not match");return}if(e.password.length<6){c("Password must be at least 6 characters long");return}l(!0),c("");try{const{data:_,error:j}=await p(e.email,e.password,{full_name:e.fullName,role:e.role});if(j)c(j.message);else if(_.user){const E={email:e.email,full_name:e.fullName,phone:e.phone,role:e.role},{error:N}=await w(_.user.id,E);N&&console.error("Profile creation error:",N),c(""),alert("Registration successful! Please check your email to verify your account.")}}catch{c("An unexpected error occurred")}finally{l(!1)}},f=async()=>{if(!e.role){c("Please select your role first");return}l(!0),c("");try{const{data:v,error:_}=await g();_&&c(_.message)}catch{c("Failed to sign up with Google")}finally{l(!1)}};return d===1?a.jsx("div",{className:"auth-container",children:a.jsxs("div",{className:"auth-card",children:[a.jsxs("div",{className:"auth-header",children:[a.jsx("h1",{children:"Join Bhookad! 🍛"}),a.jsx("p",{children:"Choose your role to get started"})]}),a.jsxs("div",{className:"role-selection",children:[a.jsxs("div",{className:"role-card",onClick:()=>k("vendor"),children:[a.jsx("div",{className:"role-icon",children:"🏪"}),a.jsx("h3",{children:"I'm a Vendor"}),a.jsx("p",{children:"List your street food business and connect with food lovers"}),a.jsxs("ul",{children:[a.jsx("li",{children:"Create your business profile"}),a.jsx("li",{children:"Showcase your specialties"}),a.jsx("li",{children:"Connect with vloggers"}),a.jsx("li",{children:"Grow your customer base"})]})]}),a.jsxs("div",{className:"role-card",onClick:()=>k("vlogger"),children:[a.jsx("div",{className:"role-icon",children:"📹"}),a.jsx("h3",{children:"I'm a Vlogger"}),a.jsx("p",{children:"Discover amazing street food and create content"}),a.jsxs("ul",{children:[a.jsx("li",{children:"Find unique vendors"}),a.jsx("li",{children:"Create food content"}),a.jsx("li",{children:"Build your audience"}),a.jsx("li",{children:"Collaborate with vendors"})]})]})]}),a.jsx("div",{className:"auth-footer",children:a.jsxs("p",{children:["Already have an account?"," ",a.jsx(et,{to:"/login",className:"auth-link",children:"Sign in here"})]})})]})}):a.jsx("div",{className:"auth-container",children:a.jsxs("div",{className:"auth-card",children:[a.jsxs("div",{className:"auth-header",children:[a.jsx("h1",{children:"Create Your Account 🍛"}),a.jsxs("p",{children:["Joining as a ",a.jsx("strong",{children:e.role}),a.jsx("button",{className:"change-role-btn",onClick:()=>h(1),children:"Change"})]})]}),u&&a.jsx("div",{className:"error-message",children:u}),a.jsxs("form",{onSubmit:m,className:"auth-form",children:[a.jsxs("div",{className:"form-group",children:[a.jsx("label",{htmlFor:"fullName",children:"Full Name *"}),a.jsxs("div",{className:"input-with-icon",children:[a.jsx(Af,{size:20}),a.jsx("input",{type:"text",id:"fullName",name:"fullName",value:e.fullName,onChange:y,placeholder:"Enter your full name",required:!0})]})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{htmlFor:"email",children:"Email Address *"}),a.jsxs("div",{className:"input-with-icon",children:[a.jsx(Ki,{size:20}),a.jsx("input",{type:"email",id:"email",name:"email",value:e.email,onChange:y,placeholder:"Enter your email",required:!0})]})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{htmlFor:"phone",children:"Phone Number"}),a.jsxs("div",{className:"input-with-icon",children:[a.jsx(Ql,{size:20}),a.jsx("input",{type:"tel",id:"phone",name:"phone",value:e.phone,onChange:y,placeholder:"Enter your phone number"})]})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{htmlFor:"password",children:"Password *"}),a.jsxs("div",{className:"input-with-icon",children:[a.jsx(Da,{size:20}),a.jsx("input",{type:n?"text":"password",id:"password",name:"password",value:e.password,onChange:y,placeholder:"Create a password (min 6 characters)",required:!0}),a.jsx("button",{type:"button",className:"password-toggle",onClick:()=>r(!n),children:n?a.jsx(za,{size:20}):a.jsx(Xr,{size:20})})]})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{htmlFor:"confirmPassword",children:"Confirm Password *"}),a.jsxs("div",{className:"input-with-icon",children:[a.jsx(Da,{size:20}),a.jsx("input",{type:s?"text":"password",id:"confirmPassword",name:"confirmPassword",value:e.confirmPassword,onChange:y,placeholder:"Confirm your password",required:!0}),a.jsx("button",{type:"button",className:"password-toggle",onClick:()=>i(!s),children:s?a.jsx(za,{size:20}):a.jsx(Xr,{size:20})})]})]}),a.jsx("button",{type:"submit",className:"btn-primary auth-btn",disabled:o,children:o?"Creating Account...":"Create Account"})]}),a.jsx("div",{className:"auth-divider",children:a.jsx("span",{children:"or"})}),a.jsxs("button",{onClick:f,className:"btn-google",disabled:o,children:[a.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",children:[a.jsx("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),a.jsx("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),a.jsx("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),a.jsx("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Continue with Google"]}),a.jsx("div",{className:"auth-footer",children:a.jsxs("p",{children:["Already have an account?"," ",a.jsx(et,{to:"/login",className:"auth-link",children:"Sign in here"})]})})]})})},Dw=()=>{const[e,t]=x.useState(!0),[n,r]=x.useState(null),s=hn(),{createUserProfile:i,fetchUserProfile:o}=Wt();return x.useEffect(()=>{(async()=>{try{const{data:u,error:c}=await Re.auth.getSession();if(c){console.error("Auth callback error:",c),r("Authentication failed. Please try again."),setTimeout(()=>s("/login"),3e3);return}if(u.session&&u.session.user){const d=u.session.user,{data:h}=await Re.from("user_profiles").select("*").eq("id",d.id).single();if(!h){const g=d.user_metadata||{};if(g.role)await i(d.id,{email:d.email,full_name:g.full_name||d.email,role:g.role});else{s("/select-role");return}}await o(d.id);const{data:p}=await Re.from("user_profiles").select("role").eq("id",d.id).single();(p==null?void 0:p.role)==="vendor"?s("/vendor/dashboard"):(p==null?void 0:p.role)==="vlogger"?s("/vlogger/dashboard"):s("/select-role")}else r("No session found. Redirecting to login..."),setTimeout(()=>s("/login"),2e3)}catch(u){console.error("Callback handling error:",u),r("An unexpected error occurred. Please try again."),setTimeout(()=>s("/login"),3e3)}finally{t(!1)}})()},[s,i,o]),e?a.jsx("div",{className:"auth-container",children:a.jsxs("div",{className:"auth-card",children:[a.jsxs("div",{className:"auth-header",children:[a.jsx("h1",{children:"Completing Sign In... 🍛"}),a.jsx("p",{children:"Please wait while we set up your account"})]}),a.jsx("div",{className:"loading-spinner",children:a.jsx("div",{className:"spinner"})})]})}):n?a.jsx("div",{className:"auth-container",children:a.jsxs("div",{className:"auth-card",children:[a.jsxs("div",{className:"auth-header",children:[a.jsx("h1",{children:"Authentication Error"}),a.jsx("p",{children:n})]}),a.jsx("div",{className:"error-actions",children:a.jsx("button",{className:"btn-primary",onClick:()=>s("/login"),children:"Back to Login"})})]})}):null},Uw=()=>{const[e,t]=x.useState(""),[n,r]=x.useState(!1),[s,i]=x.useState(""),{user:o,updateUserProfile:l,createUserProfile:u,userProfile:c}=Wt(),d=hn(),h=async p=>{var g;if(!o){i("User not authenticated");return}r(!0),i(""),t(p);try{let w;c?w=await l({role:p}):w=await u(o.id,{email:o.email,full_name:((g=o.user_metadata)==null?void 0:g.full_name)||o.email,role:p}),w.error?i(w.error.message):p==="vendor"?d("/vendor/dashboard"):p==="vlogger"&&d("/vlogger/dashboard")}catch(w){i("Failed to set role. Please try again."),console.error("Role selection error:",w)}finally{r(!1),t("")}};return a.jsx("div",{className:"auth-container",children:a.jsxs("div",{className:"auth-card",children:[a.jsxs("div",{className:"auth-header",children:[a.jsx("h1",{children:"Choose Your Role 🍛"}),a.jsx("p",{children:"How would you like to use Bhookad?"})]}),s&&a.jsx("div",{className:"error-message",children:s}),a.jsxs("div",{className:"role-selection",children:[a.jsxs("div",{className:`role-card ${e==="vendor"?"selecting":""}`,onClick:()=>!n&&h("vendor"),style:{opacity:n&&e!=="vendor"?.5:1,cursor:n?"not-allowed":"pointer"},children:[n&&e==="vendor"&&a.jsx("div",{className:"role-loading",children:"Setting up..."}),a.jsx("div",{className:"role-icon",children:"🏪"}),a.jsx("h3",{children:"I'm a Vendor"}),a.jsx("p",{children:"List your street food business and connect with food lovers"}),a.jsxs("ul",{children:[a.jsx("li",{children:"Create your business profile"}),a.jsx("li",{children:"Showcase your specialties"}),a.jsx("li",{children:"Connect with vloggers"}),a.jsx("li",{children:"Grow your customer base"})]})]}),a.jsxs("div",{className:`role-card ${e==="vlogger"?"selecting":""}`,onClick:()=>!n&&h("vlogger"),style:{opacity:n&&e!=="vlogger"?.5:1,cursor:n?"not-allowed":"pointer"},children:[n&&e==="vlogger"&&a.jsx("div",{className:"role-loading",children:"Setting up..."}),a.jsx("div",{className:"role-icon",children:"📹"}),a.jsx("h3",{children:"I'm a Vlogger"}),a.jsx("p",{children:"Discover amazing street food and create content"}),a.jsxs("ul",{children:[a.jsx("li",{children:"Find unique vendors"}),a.jsx("li",{children:"Create food content"}),a.jsx("li",{children:"Build your audience"}),a.jsx("li",{children:"Collaborate with vendors"})]})]})]}),a.jsx("div",{className:"auth-footer",children:a.jsx("p",{style:{color:"#666",fontSize:"0.85rem"},children:"You can change your role later in your profile settings"})})]})})},Lo=({children:e,requiredRole:t=null,redirectTo:n="/login"})=>{const{user:r,userProfile:s,loading:i}=Wt(),o=wt();if(i)return a.jsx("div",{className:"auth-container",children:a.jsxs("div",{className:"auth-card",children:[a.jsxs("div",{className:"auth-header",children:[a.jsx("h1",{children:"Loading... 🍛"}),a.jsx("p",{children:"Checking your authentication"})]}),a.jsx("div",{className:"loading-spinner",children:a.jsx("div",{className:"spinner"})})]})});if(!r)return a.jsx(yr,{to:n,state:{from:o},replace:!0});if(t&&!s)return a.jsx(yr,{to:"/select-role",state:{from:o},replace:!0});if(t&&s&&s.role!==t){const l=s.role==="vendor"?"/vendor/dashboard":"/vlogger/dashboard";return a.jsx(yr,{to:l,replace:!0})}return!t&&s&&!s.role?a.jsx(yr,{to:"/select-role",state:{from:o},replace:!0}):e};const Mw=()=>{var d;const{user:e,userProfile:t,signOut:n}=Wt(),[r,s]=x.useState("overview"),[i,o]=x.useState({totalViews:1250,totalReviews:48,averageRating:4.6,monthlyGrowth:12}),l=async()=>{await n()},u=()=>a.jsxs("div",{className:"dashboard-content",children:[a.jsxs("div",{className:"welcome-section",children:[a.jsxs("h1",{children:["Welcome back, ",(t==null?void 0:t.full_name)||"Vendor","! 🏪"]}),a.jsx("p",{children:"Here's how your business is performing today"})]}),a.jsxs("div",{className:"stats-grid",children:[a.jsxs("div",{className:"stat-card",children:[a.jsx("div",{className:"stat-icon",children:a.jsx(Gi,{size:24})}),a.jsxs("div",{className:"stat-info",children:[a.jsx("h3",{children:i.totalViews}),a.jsx("p",{children:"Profile Views"}),a.jsxs("span",{className:"stat-change positive",children:["+",i.monthlyGrowth,"% this month"]})]})]}),a.jsxs("div",{className:"stat-card",children:[a.jsx("div",{className:"stat-icon",children:a.jsx(We,{size:24})}),a.jsxs("div",{className:"stat-info",children:[a.jsx("h3",{children:i.averageRating}),a.jsx("p",{children:"Average Rating"}),a.jsx("span",{className:"stat-change positive",children:"+0.2 from last month"})]})]}),a.jsxs("div",{className:"stat-card",children:[a.jsx("div",{className:"stat-icon",children:a.jsx(_i,{size:24})}),a.jsxs("div",{className:"stat-info",children:[a.jsx("h3",{children:i.totalReviews}),a.jsx("p",{children:"Total Reviews"}),a.jsx("span",{className:"stat-change positive",children:"+8 this week"})]})]}),a.jsxs("div",{className:"stat-card",children:[a.jsx("div",{className:"stat-icon",children:a.jsx(Rc,{size:24})}),a.jsxs("div",{className:"stat-info",children:[a.jsx("h3",{children:"12"}),a.jsx("p",{children:"Vlogger Visits"}),a.jsx("span",{className:"stat-change positive",children:"+3 this month"})]})]})]}),a.jsxs("div",{className:"quick-actions",children:[a.jsx("h2",{children:"Quick Actions"}),a.jsxs("div",{className:"action-buttons",children:[a.jsxs("button",{className:"action-btn primary",children:[a.jsx(Ua,{size:20}),"Update Menu"]}),a.jsxs("button",{className:"action-btn secondary",children:[a.jsx(Rc,{size:20}),"Add Photos"]}),a.jsxs("button",{className:"action-btn secondary",children:[a.jsx(Gl,{size:20}),"Update Hours"]}),a.jsxs("button",{className:"action-btn secondary",children:[a.jsx(Mt,{size:20}),"Update Location"]})]})]}),a.jsxs("div",{className:"recent-activity",children:[a.jsx("h2",{children:"Recent Activity"}),a.jsxs("div",{className:"activity-list",children:[a.jsxs("div",{className:"activity-item",children:[a.jsx("div",{className:"activity-icon",children:"⭐"}),a.jsxs("div",{className:"activity-content",children:[a.jsxs("p",{children:[a.jsx("strong",{children:"New 5-star review"})," from Rahul Kumar"]}),a.jsx("span",{className:"activity-time",children:"2 hours ago"})]})]}),a.jsxs("div",{className:"activity-item",children:[a.jsx("div",{className:"activity-icon",children:"📹"}),a.jsxs("div",{className:"activity-content",children:[a.jsxs("p",{children:[a.jsx("strong",{children:"Food vlogger visit"})," - FoodieExplorer featured your stall"]}),a.jsx("span",{className:"activity-time",children:"1 day ago"})]})]}),a.jsxs("div",{className:"activity-item",children:[a.jsx("div",{className:"activity-icon",children:"👥"}),a.jsxs("div",{className:"activity-content",children:[a.jsxs("p",{children:[a.jsx("strong",{children:"Profile viewed"})," 25 times today"]}),a.jsx("span",{className:"activity-time",children:"Today"})]})]})]})]})]}),c=()=>a.jsx("div",{className:"dashboard-content",children:a.jsxs("div",{className:"profile-section",children:[a.jsx("h1",{children:"Business Profile"}),a.jsxs("div",{className:"profile-form",children:[a.jsxs("div",{className:"form-row",children:[a.jsxs("div",{className:"form-group",children:[a.jsx("label",{children:"Business Name"}),a.jsx("input",{type:"text",value:(t==null?void 0:t.business_name)||"",placeholder:"Enter your business name"})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{children:"Cuisine Type"}),a.jsx("input",{type:"text",value:(t==null?void 0:t.cuisine_type)||"",placeholder:"e.g., North Indian, Street Food"})]})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{children:"Business Address"}),a.jsx("textarea",{value:(t==null?void 0:t.business_address)||"",placeholder:"Enter your complete business address",rows:"3"})]}),a.jsxs("div",{className:"form-row",children:[a.jsxs("div",{className:"form-group",children:[a.jsx("label",{children:"Phone Number"}),a.jsx("input",{type:"tel",value:(t==null?void 0:t.business_phone)||"",placeholder:"Enter business phone"})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{children:"City"}),a.jsx("input",{type:"text",value:(t==null?void 0:t.location_city)||"",placeholder:"Enter your city"})]})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{children:"Specialties"}),a.jsx("input",{type:"text",placeholder:"e.g., Butter Chicken, Naan, Lassi (comma separated)"})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{children:"Business Description"}),a.jsx("textarea",{value:(t==null?void 0:t.bio)||"",placeholder:"Tell customers about your business...",rows:"4"})]}),a.jsx("button",{className:"btn-primary",children:"Save Changes"})]})]})});return a.jsxs("div",{className:"dashboard-container",children:[a.jsxs("div",{className:"dashboard-sidebar",children:[a.jsx("div",{className:"sidebar-header",children:a.jsxs("div",{className:"user-info",children:[a.jsx("div",{className:"user-avatar",children:((d=t==null?void 0:t.full_name)==null?void 0:d.charAt(0))||"V"}),a.jsxs("div",{className:"user-details",children:[a.jsx("h3",{children:(t==null?void 0:t.full_name)||"Vendor"}),a.jsx("p",{children:(t==null?void 0:t.business_name)||"Your Business"}),a.jsx("span",{className:"user-role",children:"Vendor"})]})]})}),a.jsxs("nav",{className:"sidebar-nav",children:[a.jsxs("button",{className:`nav-item ${r==="overview"?"active":""}`,onClick:()=>s("overview"),children:[a.jsx(ww,{size:20}),"Overview"]}),a.jsxs("button",{className:`nav-item ${r==="profile"?"active":""}`,onClick:()=>s("profile"),children:[a.jsx(Ua,{size:20}),"Profile"]}),a.jsxs("button",{className:`nav-item ${r==="analytics"?"active":""}`,onClick:()=>s("analytics"),children:[a.jsx(_i,{size:20}),"Analytics"]}),a.jsxs("button",{className:`nav-item ${r==="settings"?"active":""}`,onClick:()=>s("settings"),children:[a.jsx($f,{size:20}),"Settings"]})]}),a.jsx("div",{className:"sidebar-footer",children:a.jsxs("button",{className:"nav-item logout",onClick:l,children:[a.jsx(Lf,{size:20}),"Sign Out"]})})]}),a.jsxs("div",{className:"dashboard-main",children:[r==="overview"&&u(),r==="profile"&&c(),r==="analytics"&&a.jsxs("div",{className:"dashboard-content",children:[a.jsx("h1",{children:"Analytics"}),a.jsx("p",{children:"Analytics features coming soon..."})]}),r==="settings"&&a.jsxs("div",{className:"dashboard-content",children:[a.jsx("h1",{children:"Settings"}),a.jsx("p",{children:"Settings panel coming soon..."})]})]})]})},Fw=()=>{var p;const{user:e,userProfile:t,signOut:n}=Wt(),[r,s]=x.useState("overview"),[i,o]=x.useState({totalVideos:24,totalViews:15420,subscribers:2850,monthlyGrowth:18}),[l]=x.useState([{id:1,name:"Sharma Ji Ka Dhaba",cuisine:"North Indian",rating:4.8,distance:"0.5 km",specialties:["Butter Chicken","Naan"]},{id:2,name:"Mumbai Chaat Corner",cuisine:"Street Food",rating:4.6,distance:"1.2 km",specialties:["Pani Puri","Bhel Puri"]},{id:3,name:"Delhi Paratha Wala",cuisine:"North Indian",rating:4.7,distance:"0.8 km",specialties:["Aloo Paratha","Lassi"]}]),u=async()=>{await n()},c=()=>a.jsxs("div",{className:"dashboard-content",children:[a.jsxs("div",{className:"welcome-section",children:[a.jsxs("h1",{children:["Welcome back, ",(t==null?void 0:t.full_name)||"Vlogger","! 📹"]}),a.jsx("p",{children:"Ready to discover and create amazing food content?"})]}),a.jsxs("div",{className:"stats-grid",children:[a.jsxs("div",{className:"stat-card",children:[a.jsx("div",{className:"stat-icon",children:a.jsx(Oc,{size:24})}),a.jsxs("div",{className:"stat-info",children:[a.jsx("h3",{children:i.totalVideos}),a.jsx("p",{children:"Videos Created"}),a.jsx("span",{className:"stat-change positive",children:"+3 this month"})]})]}),a.jsxs("div",{className:"stat-card",children:[a.jsx("div",{className:"stat-icon",children:a.jsx(Xr,{size:24})}),a.jsxs("div",{className:"stat-info",children:[a.jsx("h3",{children:i.totalViews.toLocaleString()}),a.jsx("p",{children:"Total Views"}),a.jsxs("span",{className:"stat-change positive",children:["+",i.monthlyGrowth,"% this month"]})]})]}),a.jsxs("div",{className:"stat-card",children:[a.jsx("div",{className:"stat-icon",children:a.jsx(Gi,{size:24})}),a.jsxs("div",{className:"stat-info",children:[a.jsx("h3",{children:i.subscribers.toLocaleString()}),a.jsx("p",{children:"Subscribers"}),a.jsx("span",{className:"stat-change positive",children:"+125 this week"})]})]}),a.jsxs("div",{className:"stat-card",children:[a.jsx("div",{className:"stat-icon",children:a.jsx(Of,{size:24})}),a.jsxs("div",{className:"stat-info",children:[a.jsx("h3",{children:"892"}),a.jsx("p",{children:"Total Likes"}),a.jsx("span",{className:"stat-change positive",children:"+45 today"})]})]})]}),a.jsxs("div",{className:"content-sections",children:[a.jsxs("div",{className:"section",children:[a.jsx("h2",{children:"Nearby Vendors to Explore"}),a.jsx("div",{className:"vendor-list",children:l.map(g=>a.jsxs("div",{className:"vendor-card-small",children:[a.jsxs("div",{className:"vendor-info",children:[a.jsx("h4",{children:g.name}),a.jsx("p",{className:"vendor-cuisine",children:g.cuisine}),a.jsx("p",{className:"vendor-specialties",children:g.specialties.join(", ")}),a.jsxs("div",{className:"vendor-meta",children:[a.jsxs("span",{className:"rating",children:[a.jsx(We,{size:14}),g.rating]}),a.jsxs("span",{className:"distance",children:[a.jsx(Mt,{size:14}),g.distance]})]})]}),a.jsxs("div",{className:"vendor-actions",children:[a.jsx("button",{className:"btn-small primary",children:"Visit"}),a.jsx("button",{className:"btn-small secondary",children:"Save"})]})]},g.id))}),a.jsx("button",{className:"btn-secondary",children:"Explore More Vendors"})]}),a.jsxs("div",{className:"section",children:[a.jsx("h2",{children:"Content Ideas"}),a.jsxs("div",{className:"idea-list",children:[a.jsxs("div",{className:"idea-item",children:[a.jsx("div",{className:"idea-icon",children:"🌮"}),a.jsxs("div",{className:"idea-content",children:[a.jsx("h4",{children:"Street Taco Challenge"}),a.jsx("p",{children:"Try 5 different taco vendors in one day"})]})]}),a.jsxs("div",{className:"idea-item",children:[a.jsx("div",{className:"idea-icon",children:"🍜"}),a.jsxs("div",{className:"idea-content",children:[a.jsx("h4",{children:"Ramen vs Street Noodles"}),a.jsx("p",{children:"Compare traditional ramen with local street noodles"})]})]}),a.jsxs("div",{className:"idea-item",children:[a.jsx("div",{className:"idea-icon",children:"🥘"}),a.jsxs("div",{className:"idea-content",children:[a.jsx("h4",{children:"Regional Curry Tour"}),a.jsx("p",{children:"Explore different regional curry styles"})]})]})]})]})]})]}),d=()=>a.jsx("div",{className:"dashboard-content",children:a.jsxs("div",{className:"profile-section",children:[a.jsx("h1",{children:"Creator Profile"}),a.jsxs("div",{className:"profile-form",children:[a.jsxs("div",{className:"form-row",children:[a.jsxs("div",{className:"form-group",children:[a.jsx("label",{children:"Channel Name"}),a.jsx("input",{type:"text",value:(t==null?void 0:t.full_name)||"",placeholder:"Enter your channel name"})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{children:"Content Category"}),a.jsxs("select",{value:(t==null?void 0:t.content_category)||"",children:[a.jsx("option",{value:"",children:"Select category"}),a.jsx("option",{value:"food-reviews",children:"Food Reviews"}),a.jsx("option",{value:"street-food",children:"Street Food"}),a.jsx("option",{value:"cooking",children:"Cooking"}),a.jsx("option",{value:"food-travel",children:"Food Travel"})]})]})]}),a.jsxs("div",{className:"form-row",children:[a.jsxs("div",{className:"form-group",children:[a.jsx("label",{children:"YouTube Channel"}),a.jsx("input",{type:"url",value:(t==null?void 0:t.youtube_channel)||"",placeholder:"https://youtube.com/@yourchannel"})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{children:"Instagram Handle"}),a.jsx("input",{type:"text",value:(t==null?void 0:t.instagram_handle)||"",placeholder:"@yourusername"})]})]}),a.jsxs("div",{className:"form-row",children:[a.jsxs("div",{className:"form-group",children:[a.jsx("label",{children:"Subscriber Count"}),a.jsx("input",{type:"number",value:(t==null?void 0:t.subscriber_count)||"",placeholder:"Enter your subscriber count"})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{children:"City"}),a.jsx("input",{type:"text",value:(t==null?void 0:t.location_city)||"",placeholder:"Enter your city"})]})]}),a.jsxs("div",{className:"form-group",children:[a.jsx("label",{children:"Bio"}),a.jsx("textarea",{value:(t==null?void 0:t.bio)||"",placeholder:"Tell vendors and viewers about yourself...",rows:"4"})]}),a.jsx("button",{className:"btn-primary",children:"Save Changes"})]})]})}),h=()=>a.jsx("div",{className:"dashboard-content",children:a.jsxs("div",{className:"discover-section",children:[a.jsx("h1",{children:"Discover Vendors"}),a.jsxs("div",{className:"search-filters",children:[a.jsxs("div",{className:"search-bar",children:[a.jsx(Ma,{size:20}),a.jsx("input",{type:"text",placeholder:"Search vendors, cuisine, location..."})]}),a.jsxs("div",{className:"filter-buttons",children:[a.jsx("button",{className:"filter-btn active",children:"All"}),a.jsx("button",{className:"filter-btn",children:"North Indian"}),a.jsx("button",{className:"filter-btn",children:"Street Food"}),a.jsx("button",{className:"filter-btn",children:"South Indian"}),a.jsx("button",{className:"filter-btn",children:"Chinese"})]})]}),a.jsx("div",{className:"vendor-grid",children:l.map(g=>a.jsxs("div",{className:"vendor-card",children:[a.jsx("div",{className:"vendor-image",children:a.jsx("span",{className:"food-emoji",children:"🍛"})}),a.jsxs("div",{className:"vendor-info",children:[a.jsx("h3",{children:g.name}),a.jsx("p",{className:"vendor-cuisine",children:g.cuisine}),a.jsx("p",{className:"vendor-specialties",children:g.specialties.join(", ")}),a.jsxs("div",{className:"vendor-meta",children:[a.jsxs("span",{className:"rating",children:[a.jsx(We,{size:16}),g.rating]}),a.jsxs("span",{className:"distance",children:[a.jsx(Mt,{size:16}),g.distance]})]}),a.jsxs("div",{className:"vendor-actions",children:[a.jsx("button",{className:"btn-primary",children:"Visit Now"}),a.jsx("button",{className:"btn-secondary",children:"Save for Later"})]})]})]},g.id))})]})});return a.jsxs("div",{className:"dashboard-container",children:[a.jsxs("div",{className:"dashboard-sidebar",children:[a.jsx("div",{className:"sidebar-header",children:a.jsxs("div",{className:"user-info",children:[a.jsx("div",{className:"user-avatar",children:((p=t==null?void 0:t.full_name)==null?void 0:p.charAt(0))||"V"}),a.jsxs("div",{className:"user-details",children:[a.jsx("h3",{children:(t==null?void 0:t.full_name)||"Vlogger"}),a.jsxs("p",{children:[(t==null?void 0:t.subscriber_count)||0," subscribers"]}),a.jsx("span",{className:"user-role",children:"Vlogger"})]})]})}),a.jsxs("nav",{className:"sidebar-nav",children:[a.jsxs("button",{className:`nav-item ${r==="overview"?"active":""}`,onClick:()=>s("overview"),children:[a.jsx(Oc,{size:20}),"Overview"]}),a.jsxs("button",{className:`nav-item ${r==="discover"?"active":""}`,onClick:()=>s("discover"),children:[a.jsx(Ma,{size:20}),"Discover"]}),a.jsxs("button",{className:`nav-item ${r==="profile"?"active":""}`,onClick:()=>s("profile"),children:[a.jsx(Ua,{size:20}),"Profile"]}),a.jsxs("button",{className:`nav-item ${r==="analytics"?"active":""}`,onClick:()=>s("analytics"),children:[a.jsx(_i,{size:20}),"Analytics"]}),a.jsxs("button",{className:`nav-item ${r==="settings"?"active":""}`,onClick:()=>s("settings"),children:[a.jsx($f,{size:20}),"Settings"]})]}),a.jsx("div",{className:"sidebar-footer",children:a.jsxs("button",{className:"nav-item logout",onClick:u,children:[a.jsx(Lf,{size:20}),"Sign Out"]})})]}),a.jsxs("div",{className:"dashboard-main",children:[r==="overview"&&c(),r==="discover"&&h(),r==="profile"&&d(),r==="analytics"&&a.jsxs("div",{className:"dashboard-content",children:[a.jsx("h1",{children:"Analytics"}),a.jsx("p",{children:"Analytics features coming soon..."})]}),r==="settings"&&a.jsxs("div",{className:"dashboard-content",children:[a.jsx("h1",{children:"Settings"}),a.jsx("p",{children:"Settings panel coming soon..."})]})]})]})};const Bw=()=>a.jsxs("div",{className:"App",children:[a.jsx(Sw,{}),a.jsx(Ew,{}),a.jsx(Nw,{}),a.jsx(bw,{}),a.jsx(Rw,{}),a.jsx(Ow,{}),a.jsx(Lw,{}),a.jsx($w,{}),a.jsx(Aw,{})]});function Vw(){return a.jsx(aw,{children:a.jsx(zg,{children:a.jsxs(hg,{children:[a.jsx(st,{path:"/",element:a.jsx(Bw,{})}),a.jsx(st,{path:"/login",element:a.jsx(Iw,{})}),a.jsx(st,{path:"/register",element:a.jsx(zw,{})}),a.jsx(st,{path:"/auth/callback",element:a.jsx(Dw,{})}),a.jsx(st,{path:"/select-role",element:a.jsx(Lo,{children:a.jsx(Uw,{})})}),a.jsx(st,{path:"/vendor/dashboard",element:a.jsx(Lo,{requiredRole:"vendor",children:a.jsx(Mw,{})})}),a.jsx(st,{path:"/vlogger/dashboard",element:a.jsx(Lo,{requiredRole:"vlogger",children:a.jsx(Fw,{})})}),a.jsx(st,{path:"*",element:a.jsx(yr,{to:"/",replace:!0})})]})})})}$o.createRoot(document.getElementById("root")).render(a.jsx(Vc.StrictMode,{children:a.jsx(Vw,{})}));
