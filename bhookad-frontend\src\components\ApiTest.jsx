import React, { useState, useEffect } from 'react'
import { apiService } from '../services/api'

const ApiTest = () => {
  const [testResults, setTestResults] = useState({
    connection: null,
    vendors: null,
    health: null
  })
  const [loading, setLoading] = useState(false)

  const testAPI = async () => {
    setLoading(true)
    const results = {}

    try {
      // Test basic connection
      console.log('Testing API connection...')
      const connectionTest = await apiService.testConnection()
      results.connection = connectionTest
      console.log('Connection test result:', connectionTest)
    } catch (error) {
      results.connection = { error: error.message }
      console.error('Connection test failed:', error)
    }

    try {
      // Test vendors endpoint
      console.log('Testing vendors endpoint...')
      const vendorsTest = await apiService.getVendors()
      results.vendors = vendorsTest
      console.log('Vendors test result:', vendorsTest)
    } catch (error) {
      results.vendors = { error: error.message }
      console.error('Vendors test failed:', error)
    }

    setTestResults(results)
    setLoading(false)
  }

  useEffect(() => {
    testAPI()
  }, [])

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      right: '10px', 
      background: 'white', 
      padding: '20px', 
      border: '1px solid #ccc',
      borderRadius: '8px',
      maxWidth: '300px',
      fontSize: '12px',
      zIndex: 1000
    }}>
      <h4>API Test Results</h4>
      
      <div>
        <strong>Connection Test:</strong>
        {loading ? (
          <span> Loading...</span>
        ) : testResults.connection ? (
          testResults.connection.error ? (
            <span style={{ color: 'red' }}> ❌ {testResults.connection.error}</span>
          ) : (
            <span style={{ color: 'green' }}> ✅ {testResults.connection.message}</span>
          )
        ) : (
          <span> Not tested</span>
        )}
      </div>

      <div>
        <strong>Vendors Test:</strong>
        {loading ? (
          <span> Loading...</span>
        ) : testResults.vendors ? (
          testResults.vendors.error ? (
            <span style={{ color: 'red' }}> ❌ {testResults.vendors.error}</span>
          ) : (
            <span style={{ color: 'green' }}> ✅ {testResults.vendors.count} vendors loaded</span>
          )
        ) : (
          <span> Not tested</span>
        )}
      </div>

      <button 
        onClick={testAPI} 
        style={{ 
          marginTop: '10px', 
          padding: '5px 10px',
          background: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Retest API
      </button>
    </div>
  )
}

export default ApiTest
