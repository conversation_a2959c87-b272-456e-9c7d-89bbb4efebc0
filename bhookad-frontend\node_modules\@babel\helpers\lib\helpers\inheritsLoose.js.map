{"version": 3, "names": ["_setPrototypeOf", "require", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "prototype", "Object", "create", "constructor", "setPrototypeOf"], "sources": ["../../src/helpers/inheritsLoose.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport setPrototypeOf from \"./setPrototypeOf.ts\";\n\nexport default function _inheritsLoose(\n  subClass: Function,\n  superClass: Function,\n) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,eAAA,GAAAC,OAAA;AAEe,SAASC,cAAcA,CACpCC,QAAkB,EAClBC,UAAoB,EACpB;EACAD,QAAQ,CAACE,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACH,UAAU,CAACC,SAAS,CAAC;EACxDF,QAAQ,CAACE,SAAS,CAACG,WAAW,GAAGL,QAAQ;EACzC,IAAAM,uBAAc,EAACN,QAAQ,EAAEC,UAAU,CAAC;AACtC", "ignoreList": []}