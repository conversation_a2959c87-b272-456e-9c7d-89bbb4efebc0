{"version": 3, "file": "subselects.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/css-select/93caad96c807da1d48f08166ef14cf26916b9364/src/", "sources": ["pseudo-selectors/subselects.ts"], "names": [], "mappings": "AACA,OAAO,QAAQ,MAAM,UAAU,CAAC;AAOhC,OAAO,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AAEzC,gFAAgF;AAChF,MAAM,CAAC,MAAM,mBAAmB,GAAG,EAAE,CAAC;AAEtC,MAAM,UAAU,WAAW,CACvB,IAAgC,EAChC,OAAmC;IAEnC,IAAI,IAAI,KAAK,QAAQ,CAAC,SAAS;QAAE,OAAO,QAAQ,CAAC,SAAS,CAAC;IAC3D,OAAO,CAAC,IAAU,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7D,CAAC;AAUD,MAAM,UAAU,eAAe,CAC3B,IAAU,EACV,OAAmC;IAEnC,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC3C,IAAI,QAAQ,CAAC,MAAM,IAAI,CAAC;QAAE,OAAO,EAAE,CAAC;IACpC,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACzC,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,KAAK,QAAQ,CAAC,MAAM,GAAG,CAAC;QAAE,OAAO,EAAE,CAAC;IAClE,OAAO,QAAQ,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/D,CAAC;AAED,SAAS,WAAW,CAChB,OAA2C;IAE3C,gCAAgC;IAChC,OAAO;QACH,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO;QAC1B,uBAAuB,EAAE,CAAC,CAAC,OAAO,CAAC,uBAAuB;QAC1D,aAAa,EAAE,CAAC,CAAC,OAAO,CAAC,aAAa;QACtC,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU;QAChC,YAAY,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY;QACpC,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,MAAM,EAAE,OAAO,CAAC,MAAM;KACzB,CAAC;AACN,CAAC;AAED,MAAM,EAAE,GAAc,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE;IAClE,MAAM,IAAI,GAAG,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC;IAEhE,OAAO,IAAI,KAAK,QAAQ,CAAC,QAAQ;QAC7B,CAAC,CAAC,IAAI;QACN,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,SAAS;YAC7B,CAAC,CAAC,QAAQ,CAAC,SAAS;YACpB,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7C,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,UAAU,GAA8B;IACjD,EAAE;IACF;;OAEG;IACH,OAAO,EAAE,EAAE;IACX,KAAK,EAAE,EAAE;IACT,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY;QAC3C,MAAM,IAAI,GAAG,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC;QAEhE,OAAO,IAAI,KAAK,QAAQ,CAAC,SAAS;YAC9B,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,QAAQ;gBAC5B,CAAC,CAAC,QAAQ,CAAC,SAAS;gBACpB,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IACD,GAAG,CACC,IAAgC,EAChC,SAAuB,EACvB,OAA2C,EAC3C,QAA4B,EAC5B,YAA6C;QAE7C,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QAE5B,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAE7B,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACtD,CAAC,CAAC,mEAAmE;gBAClE,CAAC,mBAAmB,CAA8B;YACrD,CAAC,CAAC,SAAS,CAAC;QAEhB,MAAM,QAAQ,GAAG,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,QAAQ,KAAK,QAAQ,CAAC,SAAS;YAAE,OAAO,QAAQ,CAAC,SAAS,CAAC;QAE/D,MAAM,UAAU,GAAG,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAElD,iDAAiD;QACjD,IAAI,OAAO,IAAI,QAAQ,KAAK,QAAQ,CAAC,QAAQ,EAAE;YAC3C;;;eAGG;YACH,MAAM,EAAE,sBAAsB,GAAG,KAAK,EAAE,GAAG,QAAQ,CAAC;YAEpD,OAAO,CAAC,IAAI,EAAE,EAAE;gBACZ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;oBAAE,OAAO,KAAK,CAAC;gBAE9B,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;gBAClB,MAAM,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACzC,MAAM,YAAY,GAAG,sBAAsB;oBACvC,CAAC,CAAC,CAAC,GAAG,MAAM,EAAE,GAAG,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;oBAChD,CAAC,CAAC,MAAM,CAAC;gBAEb,OAAO,OAAO,CAAC,SAAS,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;YACvD,CAAC,CAAC;SACL;QAED,OAAO,CAAC,IAAI,EAAE,EAAE,CACZ,IAAI,CAAC,IAAI,CAAC;YACV,OAAO,CAAC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IACjE,CAAC;CACJ,CAAC"}