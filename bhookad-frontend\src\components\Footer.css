.footer {
  background: #2c3e50;
  color: white;
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section {
  display: flex;
  flex-direction: column;
}

.footer-title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #ff6b35;
}

.footer-subtitle {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #ecf0f1;
}

.footer-description {
  color: #bdc3c7;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  background: #34495e;
  color: white;
  padding: 0.75rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  text-decoration: none;
}

.social-link:hover {
  background: #ff6b35;
  transform: translateY(-2px);
}

.footer-links {
  list-style: none;
  padding: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: #bdc3c7;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #ff6b35;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #bdc3c7;
}

.contact-item svg {
  color: #ff6b35;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  padding-top: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #bdc3c7;
}

.footer-bottom-links {
  display: flex;
  gap: 1rem;
}

.footer-bottom-links a {
  color: #bdc3c7;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
  color: #ff6b35;
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}
