import React, { useState, useEffect } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { 
  Store, 
  Users, 
  Star, 
  TrendingUp, 
  MapPin, 
  Phone, 
  Clock,
  Camera,
  Edit,
  Settings,
  LogOut
} from 'lucide-react'
import './Dashboard.css'

const VendorDashboard = () => {
  const { user, userProfile, signOut } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')
  const [stats, setStats] = useState({
    totalViews: 1250,
    totalReviews: 48,
    averageRating: 4.6,
    monthlyGrowth: 12,
    monthlyRevenue: 45000,
    vloggerVisits: 12,
    customerRetention: 78,
    promotionSpend: 5000
  })

  const handleSignOut = async () => {
    await signOut()
  }

  const renderOverview = () => (
    <div className="dashboard-content">
      <div className="welcome-section">
        <h1>Welcome back, {userProfile?.full_name || 'Vendor'}! 🏪</h1>
        <p>Here's how your business is performing today</p>
      </div>

      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">
            <Users size={24} />
          </div>
          <div className="stat-info">
            <h3>{stats.totalViews}</h3>
            <p>Profile Views</p>
            <span className="stat-change positive">+{stats.monthlyGrowth}% this month</span>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <Star size={24} />
          </div>
          <div className="stat-info">
            <h3>{stats.averageRating}</h3>
            <p>Average Rating</p>
            <span className="stat-change positive">+0.2 from last month</span>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <TrendingUp size={24} />
          </div>
          <div className="stat-info">
            <h3>{stats.totalReviews}</h3>
            <p>Total Reviews</p>
            <span className="stat-change positive">+8 this week</span>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <Camera size={24} />
          </div>
          <div className="stat-info">
            <h3>12</h3>
            <p>Vlogger Visits</p>
            <span className="stat-change positive">+3 this month</span>
          </div>
        </div>
      </div>

      <div className="quick-actions">
        <h2>Quick Actions</h2>
        <div className="action-buttons">
          <button className="action-btn primary">
            <Edit size={20} />
            Update Menu
          </button>
          <button className="action-btn secondary">
            <Camera size={20} />
            Add Photos
          </button>
          <button className="action-btn secondary">
            <Clock size={20} />
            Update Hours
          </button>
          <button className="action-btn secondary">
            <MapPin size={20} />
            Update Location
          </button>
        </div>
      </div>

      <div className="recent-activity">
        <h2>Recent Activity</h2>
        <div className="activity-list">
          <div className="activity-item">
            <div className="activity-icon">⭐</div>
            <div className="activity-content">
              <p><strong>New 5-star review</strong> from Rahul Kumar</p>
              <span className="activity-time">2 hours ago</span>
            </div>
          </div>
          <div className="activity-item">
            <div className="activity-icon">📹</div>
            <div className="activity-content">
              <p><strong>Food vlogger visit</strong> - FoodieExplorer featured your stall</p>
              <span className="activity-time">1 day ago</span>
            </div>
          </div>
          <div className="activity-item">
            <div className="activity-icon">👥</div>
            <div className="activity-content">
              <p><strong>Profile viewed</strong> 25 times today</p>
              <span className="activity-time">Today</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderProfile = () => (
    <div className="dashboard-content">
      <div className="profile-section">
        <h1>Business Profile</h1>
        <div className="profile-form">
          <div className="form-row">
            <div className="form-group">
              <label>Business Name</label>
              <input 
                type="text" 
                value={userProfile?.business_name || ''} 
                placeholder="Enter your business name"
              />
            </div>
            <div className="form-group">
              <label>Cuisine Type</label>
              <input 
                type="text" 
                value={userProfile?.cuisine_type || ''} 
                placeholder="e.g., North Indian, Street Food"
              />
            </div>
          </div>

          <div className="form-group">
            <label>Business Address</label>
            <textarea 
              value={userProfile?.business_address || ''} 
              placeholder="Enter your complete business address"
              rows="3"
            />
          </div>

          <div className="form-row">
            <div className="form-group">
              <label>Phone Number</label>
              <input 
                type="tel" 
                value={userProfile?.business_phone || ''} 
                placeholder="Enter business phone"
              />
            </div>
            <div className="form-group">
              <label>City</label>
              <input 
                type="text" 
                value={userProfile?.location_city || ''} 
                placeholder="Enter your city"
              />
            </div>
          </div>

          <div className="form-group">
            <label>Specialties</label>
            <input 
              type="text" 
              placeholder="e.g., Butter Chicken, Naan, Lassi (comma separated)"
            />
          </div>

          <div className="form-group">
            <label>Business Description</label>
            <textarea 
              value={userProfile?.bio || ''} 
              placeholder="Tell customers about your business..."
              rows="4"
            />
          </div>

          <button className="btn-primary">Save Changes</button>
        </div>
      </div>
    </div>
  )

  const renderAnalytics = () => (
    <div className="dashboard-content">
      <div className="analytics-header">
        <h1>Business Analytics</h1>
        <p>Detailed insights about your business performance</p>
      </div>

      <div className="analytics-grid">
        <div className="analytics-card">
          <h3>Revenue Analytics</h3>
          <div className="revenue-stats">
            <div className="revenue-item">
              <span className="revenue-label">This Month</span>
              <span className="revenue-value">₹{stats.monthlyRevenue.toLocaleString()}</span>
              <span className="revenue-change positive">+15%</span>
            </div>
            <div className="revenue-item">
              <span className="revenue-label">Average Daily</span>
              <span className="revenue-value">₹{Math.round(stats.monthlyRevenue/30).toLocaleString()}</span>
              <span className="revenue-change positive">+8%</span>
            </div>
            <div className="revenue-item">
              <span className="revenue-label">Peak Hours</span>
              <span className="revenue-value">12PM - 2PM</span>
              <span className="revenue-change neutral">Lunch Rush</span>
            </div>
          </div>
        </div>

        <div className="analytics-card">
          <h3>Customer Insights</h3>
          <div className="customer-metrics">
            <div className="metric">
              <span className="metric-number">{stats.customerRetention}%</span>
              <span className="metric-label">Customer Retention</span>
            </div>
            <div className="metric">
              <span className="metric-number">4.2</span>
              <span className="metric-label">Avg Order Value</span>
            </div>
            <div className="metric">
              <span className="metric-number">85%</span>
              <span className="metric-label">Satisfaction Rate</span>
            </div>
          </div>
        </div>

        <div className="analytics-card">
          <h3>Vlogger Collaborations</h3>
          <div className="vlogger-stats">
            <div className="vlogger-item">
              <span>Total Collaborations</span>
              <span className="stat-value">{stats.vloggerVisits}</span>
            </div>
            <div className="vlogger-item">
              <span>Avg Views per Video</span>
              <span className="stat-value">15.2K</span>
            </div>
            <div className="vlogger-item">
              <span>Conversion Rate</span>
              <span className="stat-value">12.5%</span>
            </div>
          </div>
        </div>

        <div className="analytics-card">
          <h3>Promotion ROI</h3>
          <div className="promotion-metrics">
            <div className="roi-item">
              <span>Amount Spent</span>
              <span className="amount">₹{stats.promotionSpend.toLocaleString()}</span>
            </div>
            <div className="roi-item">
              <span>Revenue Generated</span>
              <span className="amount positive">₹{(stats.promotionSpend * 3.2).toLocaleString()}</span>
            </div>
            <div className="roi-item">
              <span>ROI</span>
              <span className="roi positive">320%</span>
            </div>
          </div>
        </div>
      </div>

      <div className="performance-chart">
        <h3>Weekly Performance</h3>
        <div className="chart-placeholder">
          <p>📊 Interactive charts coming soon...</p>
          <div className="chart-bars">
            <div className="bar" style={{height: '60%'}}><span>Mon</span></div>
            <div className="bar" style={{height: '80%'}}><span>Tue</span></div>
            <div className="bar" style={{height: '45%'}}><span>Wed</span></div>
            <div className="bar" style={{height: '90%'}}><span>Thu</span></div>
            <div className="bar" style={{height: '75%'}}><span>Fri</span></div>
            <div className="bar" style={{height: '95%'}}><span>Sat</span></div>
            <div className="bar" style={{height: '85%'}}><span>Sun</span></div>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="dashboard-container">
      <div className="dashboard-sidebar">
        <div className="sidebar-header">
          <div className="user-info">
            <div className="user-avatar">
              {userProfile?.full_name?.charAt(0) || 'V'}
            </div>
            <div className="user-details">
              <h3>{userProfile?.full_name || 'Vendor'}</h3>
              <p>{userProfile?.business_name || 'Your Business'}</p>
              <span className="user-role">Vendor</span>
            </div>
          </div>
        </div>

        <nav className="sidebar-nav">
          <button 
            className={`nav-item ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            <Store size={20} />
            Overview
          </button>
          <button 
            className={`nav-item ${activeTab === 'profile' ? 'active' : ''}`}
            onClick={() => setActiveTab('profile')}
          >
            <Edit size={20} />
            Profile
          </button>
          <button
            className={`nav-item ${activeTab === 'analytics' ? 'active' : ''}`}
            onClick={() => setActiveTab('analytics')}
          >
            <TrendingUp size={20} />
            Analytics
          </button>
          <button
            className={`nav-item ${activeTab === 'vloggers' ? 'active' : ''}`}
            onClick={() => setActiveTab('vloggers')}
          >
            <Camera size={20} />
            Vloggers
          </button>
          <button
            className={`nav-item ${activeTab === 'promotion' ? 'active' : ''}`}
            onClick={() => setActiveTab('promotion')}
          >
            <Star size={20} />
            Promotion
          </button>
          <button
            className={`nav-item ${activeTab === 'settings' ? 'active' : ''}`}
            onClick={() => setActiveTab('settings')}
          >
            <Settings size={20} />
            Settings
          </button>
        </nav>

        <div className="sidebar-footer">
          <button className="nav-item logout" onClick={handleSignOut}>
            <LogOut size={20} />
            Sign Out
          </button>
        </div>
      </div>

      <div className="dashboard-main">
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'profile' && renderProfile()}
        {activeTab === 'analytics' && renderAnalytics()}
        {activeTab === 'vloggers' && (
          <div className="dashboard-content">
            <h1>Vlogger Collaborations</h1>
            <div className="vlogger-section">
              <div className="collaboration-requests">
                <h3>Pending Collaboration Requests</h3>
                <div className="request-list">
                  <div className="request-item">
                    <div className="vlogger-info">
                      <img src="/api/placeholder/50/50" alt="Vlogger" className="vlogger-avatar" />
                      <div>
                        <h4>FoodieExplorer</h4>
                        <p>85K followers • Food Reviews</p>
                        <span className="request-time">2 hours ago</span>
                      </div>
                    </div>
                    <div className="request-actions">
                      <button className="btn-accept">Accept</button>
                      <button className="btn-decline">Decline</button>
                    </div>
                  </div>
                  <div className="request-item">
                    <div className="vlogger-info">
                      <img src="/api/placeholder/50/50" alt="Vlogger" className="vlogger-avatar" />
                      <div>
                        <h4>StreetFoodLover</h4>
                        <p>42K followers • Street Food</p>
                        <span className="request-time">1 day ago</span>
                      </div>
                    </div>
                    <div className="request-actions">
                      <button className="btn-accept">Accept</button>
                      <button className="btn-decline">Decline</button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="active-collaborations">
                <h3>Active Collaborations</h3>
                <div className="collaboration-list">
                  <div className="collaboration-item">
                    <div className="collab-info">
                      <h4>Delhi Food Diaries</h4>
                      <p>Video scheduled for tomorrow</p>
                      <span className="status active">In Progress</span>
                    </div>
                    <div className="collab-stats">
                      <span>Expected reach: 50K+</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        {activeTab === 'promotion' && (
          <div className="dashboard-content">
            <h1>Promotion Center</h1>
            <div className="promotion-section">
              <div className="current-promotions">
                <h3>Active Promotions</h3>
                <div className="promotion-card active">
                  <div className="promotion-info">
                    <h4>Premium Boost Package</h4>
                    <p>Homepage featured placement</p>
                    <div className="promotion-stats">
                      <span>Views: 15,240</span>
                      <span>Clicks: 1,890</span>
                      <span>CTR: 12.4%</span>
                    </div>
                  </div>
                  <div className="promotion-status">
                    <span className="status-badge active">Active</span>
                    <span className="days-left">12 days left</span>
                  </div>
                </div>
              </div>

              <div className="promotion-packages">
                <h3>Available Packages</h3>
                <div className="packages-grid">
                  <div className="package-option">
                    <h4>Basic Boost</h4>
                    <p className="package-price">₹2,999/month</p>
                    <ul>
                      <li>Featured in search</li>
                      <li>Homepage banner (2 days)</li>
                      <li>Social media mentions</li>
                    </ul>
                    <button className="btn-primary">Upgrade</button>
                  </div>
                  <div className="package-option popular">
                    <h4>Premium Push</h4>
                    <p className="package-price">₹7,999/month</p>
                    <ul>
                      <li>Top search placement</li>
                      <li>Homepage banner (1 week)</li>
                      <li>Vlogger priority</li>
                      <li>Advanced analytics</li>
                    </ul>
                    <button className="btn-primary">Upgrade</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        {activeTab === 'settings' && (
          <div className="dashboard-content">
            <h1>Settings</h1>
            <div className="settings-section">
              <div className="setting-group">
                <h3>Notification Preferences</h3>
                <div className="setting-item">
                  <label>
                    <input type="checkbox" defaultChecked />
                    Email notifications for new reviews
                  </label>
                </div>
                <div className="setting-item">
                  <label>
                    <input type="checkbox" defaultChecked />
                    SMS alerts for vlogger requests
                  </label>
                </div>
                <div className="setting-item">
                  <label>
                    <input type="checkbox" />
                    Weekly performance reports
                  </label>
                </div>
              </div>

              <div className="setting-group">
                <h3>Business Hours</h3>
                <div className="hours-grid">
                  <div className="day-hours">
                    <span>Monday</span>
                    <input type="time" defaultValue="09:00" />
                    <span>to</span>
                    <input type="time" defaultValue="22:00" />
                  </div>
                  <div className="day-hours">
                    <span>Tuesday</span>
                    <input type="time" defaultValue="09:00" />
                    <span>to</span>
                    <input type="time" defaultValue="22:00" />
                  </div>
                </div>
                <button className="btn-secondary">Save Hours</button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default VendorDashboard
