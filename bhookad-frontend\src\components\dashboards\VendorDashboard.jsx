import React, { useState, useEffect } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { 
  Store, 
  Users, 
  Star, 
  TrendingUp, 
  MapPin, 
  Phone, 
  Clock,
  Camera,
  Edit,
  Settings,
  LogOut
} from 'lucide-react'
import './Dashboard.css'

const VendorDashboard = () => {
  const { user, userProfile, signOut } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')
  const [stats, setStats] = useState({
    totalViews: 1250,
    totalReviews: 48,
    averageRating: 4.6,
    monthlyGrowth: 12
  })

  const handleSignOut = async () => {
    await signOut()
  }

  const renderOverview = () => (
    <div className="dashboard-content">
      <div className="welcome-section">
        <h1>Welcome back, {userProfile?.full_name || 'Vendor'}! 🏪</h1>
        <p>Here's how your business is performing today</p>
      </div>

      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">
            <Users size={24} />
          </div>
          <div className="stat-info">
            <h3>{stats.totalViews}</h3>
            <p>Profile Views</p>
            <span className="stat-change positive">+{stats.monthlyGrowth}% this month</span>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <Star size={24} />
          </div>
          <div className="stat-info">
            <h3>{stats.averageRating}</h3>
            <p>Average Rating</p>
            <span className="stat-change positive">+0.2 from last month</span>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <TrendingUp size={24} />
          </div>
          <div className="stat-info">
            <h3>{stats.totalReviews}</h3>
            <p>Total Reviews</p>
            <span className="stat-change positive">+8 this week</span>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <Camera size={24} />
          </div>
          <div className="stat-info">
            <h3>12</h3>
            <p>Vlogger Visits</p>
            <span className="stat-change positive">+3 this month</span>
          </div>
        </div>
      </div>

      <div className="quick-actions">
        <h2>Quick Actions</h2>
        <div className="action-buttons">
          <button className="action-btn primary">
            <Edit size={20} />
            Update Menu
          </button>
          <button className="action-btn secondary">
            <Camera size={20} />
            Add Photos
          </button>
          <button className="action-btn secondary">
            <Clock size={20} />
            Update Hours
          </button>
          <button className="action-btn secondary">
            <MapPin size={20} />
            Update Location
          </button>
        </div>
      </div>

      <div className="recent-activity">
        <h2>Recent Activity</h2>
        <div className="activity-list">
          <div className="activity-item">
            <div className="activity-icon">⭐</div>
            <div className="activity-content">
              <p><strong>New 5-star review</strong> from Rahul Kumar</p>
              <span className="activity-time">2 hours ago</span>
            </div>
          </div>
          <div className="activity-item">
            <div className="activity-icon">📹</div>
            <div className="activity-content">
              <p><strong>Food vlogger visit</strong> - FoodieExplorer featured your stall</p>
              <span className="activity-time">1 day ago</span>
            </div>
          </div>
          <div className="activity-item">
            <div className="activity-icon">👥</div>
            <div className="activity-content">
              <p><strong>Profile viewed</strong> 25 times today</p>
              <span className="activity-time">Today</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderProfile = () => (
    <div className="dashboard-content">
      <div className="profile-section">
        <h1>Business Profile</h1>
        <div className="profile-form">
          <div className="form-row">
            <div className="form-group">
              <label>Business Name</label>
              <input 
                type="text" 
                value={userProfile?.business_name || ''} 
                placeholder="Enter your business name"
              />
            </div>
            <div className="form-group">
              <label>Cuisine Type</label>
              <input 
                type="text" 
                value={userProfile?.cuisine_type || ''} 
                placeholder="e.g., North Indian, Street Food"
              />
            </div>
          </div>

          <div className="form-group">
            <label>Business Address</label>
            <textarea 
              value={userProfile?.business_address || ''} 
              placeholder="Enter your complete business address"
              rows="3"
            />
          </div>

          <div className="form-row">
            <div className="form-group">
              <label>Phone Number</label>
              <input 
                type="tel" 
                value={userProfile?.business_phone || ''} 
                placeholder="Enter business phone"
              />
            </div>
            <div className="form-group">
              <label>City</label>
              <input 
                type="text" 
                value={userProfile?.location_city || ''} 
                placeholder="Enter your city"
              />
            </div>
          </div>

          <div className="form-group">
            <label>Specialties</label>
            <input 
              type="text" 
              placeholder="e.g., Butter Chicken, Naan, Lassi (comma separated)"
            />
          </div>

          <div className="form-group">
            <label>Business Description</label>
            <textarea 
              value={userProfile?.bio || ''} 
              placeholder="Tell customers about your business..."
              rows="4"
            />
          </div>

          <button className="btn-primary">Save Changes</button>
        </div>
      </div>
    </div>
  )

  return (
    <div className="dashboard-container">
      <div className="dashboard-sidebar">
        <div className="sidebar-header">
          <div className="user-info">
            <div className="user-avatar">
              {userProfile?.full_name?.charAt(0) || 'V'}
            </div>
            <div className="user-details">
              <h3>{userProfile?.full_name || 'Vendor'}</h3>
              <p>{userProfile?.business_name || 'Your Business'}</p>
              <span className="user-role">Vendor</span>
            </div>
          </div>
        </div>

        <nav className="sidebar-nav">
          <button 
            className={`nav-item ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            <Store size={20} />
            Overview
          </button>
          <button 
            className={`nav-item ${activeTab === 'profile' ? 'active' : ''}`}
            onClick={() => setActiveTab('profile')}
          >
            <Edit size={20} />
            Profile
          </button>
          <button 
            className={`nav-item ${activeTab === 'analytics' ? 'active' : ''}`}
            onClick={() => setActiveTab('analytics')}
          >
            <TrendingUp size={20} />
            Analytics
          </button>
          <button 
            className={`nav-item ${activeTab === 'settings' ? 'active' : ''}`}
            onClick={() => setActiveTab('settings')}
          >
            <Settings size={20} />
            Settings
          </button>
        </nav>

        <div className="sidebar-footer">
          <button className="nav-item logout" onClick={handleSignOut}>
            <LogOut size={20} />
            Sign Out
          </button>
        </div>
      </div>

      <div className="dashboard-main">
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'profile' && renderProfile()}
        {activeTab === 'analytics' && (
          <div className="dashboard-content">
            <h1>Analytics</h1>
            <p>Analytics features coming soon...</p>
          </div>
        )}
        {activeTab === 'settings' && (
          <div className="dashboard-content">
            <h1>Settings</h1>
            <p>Settings panel coming soon...</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default VendorDashboard
